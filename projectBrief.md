# ✅ Project Brief – Backend e Frontend Gestione Opzioni Lavorazioni Odontotecniche

**Versione Documento:** 2.0
**Data:** 19 Giugno 2025
**Autore:** <PERSON><PERSON> (Technical Assistant)

---

## 🎯 Obiettivo del Progetto

Sviluppare un **sistema completo** per la configurazione dinamica di opzioni e dipendenze tra parametri di lavorazioni odontotecniche, con:

* Backend robusto, API RESTful sicure e tipizzate.
* Interfaccia amministrativa React moderna, basata su Vite, HeroUI, TanStack Query.
* Logica di regole di dipendenza complessa completamente implementata.
* Autenticazione JWT e gestione utenti sicura.

---

## ⚙️ Stack Tecnologico

| Livello            | Dettagli                                                                    |
| ------------------ | --------------------------------------------------------------------------- |
| **Backend**        | Python (Flask), SQLAlchemy, Alembic, Flask-JWT-Extended, Marshmallow        |
| **Database**       | MySQL                                                                       |
| **Frontend**       | React + Vite + TypeScript + HeroUI + TanStack Query + React Hook Form + Zod |
| **API**            | RESTful (JSON) + Specifica OpenAPI (Swagger UI)                             |
| **Autenticazione** | JWT                                                                         |

---

## 🗂️ Componenti dell'Architettura

### ✅ Database MySQL

* Struttura aggiornata con:

  * **Parametri** & **ValoriParametro** (campi: `foto`, `colore`, `descrizione`).
  * **RegolaDipendenzaComplessa**, **CondizioniRegola**, **RisultatiRegola** per logica di dipendenze.
  * Tabelle obsolete rimosse.

### ✅ Backend Flask

* API modulari:

  * **/api/v1**: parametri radice, valutazione dipendenze.
  * **/api/admin**: CRUD completo per utenti, parametri, valori, regole.
* Autenticazione JWT (login, logout, token validation, refresh token).
* Validazione input (Marshmallow + controlli manuali).
* Documentazione OpenAPI in progress.

### ✅ Frontend React (Admin GUI)

* Struttura modulare (`components`, `pages`, `layouts`, `services`, `contexts`, `hooks`, `types`).
* Routing protetto (`ProtectedRoute`), stato globale (`AuthContext`).
* CRUD UI per:

  * Parametri, ValoriParametro, Utenti.
  * Regole di Dipendenza (form dinamici Condizioni & Risultati).
* TanStack Query per fetching, caching, invalidazione.
* Validazione robusta con Zod.

---

## 🔑 Funzionalità Realizzate

| Sezione                               | Stato          |
| ------------------------------------- | -------------- |
| Autenticazione Admin (con Refresh Token) | ✅              |
| CRUD Parametri/ValoriParametro/Utenti | ✅              |
| CRUD Regole Dipendenza Complesse      | ✅              |
| API Utente (`/api/v1`)                | ✅              |
| Documentazione API                    | 🔄 In progress |

---

## 🔄 Esempio di Flusso

1️⃣ L'utente seleziona opzioni.
2️⃣ `/api/v1/evaluate_and_get_dependent_parameters` calcola parametri dipendenti.
3️⃣ La GUI si aggiorna in tempo reale.
4️⃣ L'admin gestisce tutto da GUI React.

---

## 🚀 Nuova Strategia di Sviluppo

| Passo    | Stato                                 |
| -------- | ------------------------------------- |
| FASE 0   | ✅ Setup ambiente                      |
| FASE 1   | ✅ Modelli DB e migrazioni             |
| FASE 2   | ✅ API autenticazione                  |
| FASE 3   | ✅ Frontend base + routing + auth      |
| FASE 4–5 | ✅ CRUD Parametri, Valori, Utenti      |
| FASE 6–7 | ✅ CRUD Regole Dipendenza, UI dinamica |
| FASE 8   | 🔄 Test E2E, OpenAPI, QA finale       |

---

## ⚠️ Rischi Mitigati

* Logica dipendenze gestita con transazioni atomiche.
* Sicurezza JWT e protezione endpoint.
* Performance con query ottimizzate e caching client.
* Manutenibilità grazie a struttura modulare e SRP.

---

## ✅ Prossimi Passi Finali

1️⃣ Completare test E2E.
2️⃣ Finalizzare OpenAPI e Swagger UI.
3️⃣ QA condiviso e demo.
4️⃣ Preparare CI/CD per deploy.

---

## 📋 Note Operative

* `Flask-Admin` sostituito da React Admin.
* Config `.env` consolidato: `JWT_SECRET_KEY`, `DATABASE_URL`, `VITE_API_BASE_URL`.

---

## ✅ Allegati

* Dettagli FASE 0–FASE 8
* Schema ER (se richiesto)
* Specifica OpenAPI (in corso)

---

**Fine Documento – Versione Markdown**
