# Test della Funzionalità di Riordino Valori Parametri

## Modifiche Implementate

### Backend (app/api_admin/routes.py)
1. **Nuovo endpoint per riordinare valori**: `PUT /admin/parametri/<id_parametro>/valori/reorder`
   - Accetta un array di oggetti con `id_valore_parametro` e `ordine_visualizzazione`
   - Valida che tutti i valori appartengano al parametro specificato
   - Aggiorna l'ordine di visualizzazione in modo atomico

2. **Aggiornamento endpoint valori-for-select**: 
   - Ora ordina per `ordine_visualizzazione`
   - Restituisce tutti i campi necessari incluso `ordine_visualizzazione`

3. **Miglioramento creazione valori**:
   - Se non specificato, l'ordine viene calcolato automaticamente come max + 1

### Frontend (frontend2/src/pages/ValueParametersListPage.tsx)
1. **Aggiunta colonna Ordine**: Mostra l'ordine di visualizzazione corrente
2. **Aggiunta colonna Riordina**: Contiene frecce su/giù per spostare i valori
3. **Funzioni moveUp/moveDown**: Scambiano l'ordine tra elementi adiacenti
4. **Mutation per riordino**: Gestisce l'aggiornamento dell'ordine via API
5. **Disabilitazione bottoni**: I bottoni sono disabilitati quando appropriato

### Tipi TypeScript (frontend2/src/types/value-parameters.ts)
1. **Aggiunto campo ordine_visualizzazione** a `IValueParameter`
2. **Aggiunto campo opzionale** a `IValueParameterFormPayload`

### Form di Modifica (frontend2/src/pages/ValueParameterFormPage.tsx)
1. **Supporto completo per ordine_visualizzazione** nel form
2. **Mapping corretto** dei dati dal backend

## Come Testare

### 1. Prerequisiti
- Backend Flask in esecuzione
- Frontend React in esecuzione
- Database con almeno un parametro e alcuni valori

### 2. Test Creazione Valori
1. Andare su `/admin/parameters/{id}/values`
2. Creare alcuni valori senza specificare l'ordine
3. Verificare che vengano assegnati ordini incrementali automaticamente

### 3. Test Riordino con Frecce
1. Nella lista valori, verificare la presenza delle colonne "Ordine" e "Riordina"
2. Cliccare sulla freccia ↑ per spostare un valore verso l'alto
3. Cliccare sulla freccia ↓ per spostare un valore verso il basso
4. Verificare che:
   - L'ordine si aggiorna immediatamente
   - I bottoni si disabilitano durante l'operazione
   - Viene mostrato un toast di successo/errore
   - La lista si ricarica con il nuovo ordine

### 4. Test Casi Limite
1. **Primo elemento**: La freccia ↑ deve essere disabilitata
2. **Ultimo elemento**: La freccia ↓ deve essere disabilitata
3. **Durante operazione**: Entrambe le frecce devono essere disabilitate
4. **Errore di rete**: Deve essere mostrato un messaggio di errore

### 5. Test Form di Modifica
1. Modificare un valore esistente
2. Verificare che l'ordine di visualizzazione sia precompilato correttamente
3. Modificare l'ordine e salvare
4. Verificare che il nuovo ordine sia applicato nella lista

## API Endpoints Coinvolti

### GET /admin/parametri/{id}/valori-for-select
- **Scopo**: Recupera i valori ordinati per visualizzazione
- **Risposta**: Array di oggetti con tutti i campi incluso `ordine_visualizzazione`

### PUT /admin/parametri/{id}/valori/reorder
- **Scopo**: Aggiorna l'ordine di visualizzazione di più valori
- **Payload**: `[{"id_valore_parametro": 1, "ordine_visualizzazione": 0}, ...]`
- **Risposta**: Messaggio di successo o errore

### GET /admin/valoriparametro/{id}
- **Scopo**: Recupera un singolo valore per modifica
- **Risposta**: Oggetto con tutti i campi incluso `ordine_visualizzazione`

### PUT /admin/valoriparametro/{id}
- **Scopo**: Aggiorna un valore esistente
- **Supporta**: Aggiornamento dell'ordine di visualizzazione

### POST /admin/valoriparametro
- **Scopo**: Crea un nuovo valore
- **Comportamento**: Se ordine non specificato, usa max + 1

## Note Tecniche

### Gestione Stato Frontend
- Usa TanStack Query per cache e invalidazione
- Mutation separata per riordino con gestione errori
- Toast notifications per feedback utente

### Sicurezza Backend
- Validazione che i valori appartengano al parametro
- Transazioni atomiche per consistenza dati
- Autenticazione JWT richiesta

### Performance
- Operazioni di riordino coinvolgono solo 2 elementi alla volta
- Query ottimizzate con ordinamento a livello database
- Cache invalidation mirata per aggiornamenti real-time