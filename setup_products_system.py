#!/usr/bin/env python3
"""
Script per configurare il sistema di gestione prodotti.
Crea i parametri necessari se non esistono.
"""

from app import app, db
from app.models import Parametri, ValoriParametro, TipoControlloUI

def setup_products_system():
    """Configura il sistema per la gestione prodotti"""
    
    with app.app_context():
        print("=== Setup Sistema Gestione Prodotti ===")
        
        # 1. Crea parametro Diametro se non esiste
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        if not parametro_diametro:
            parametro_diametro = Parametri(
                nome_parametro="Diametro",
                tipo_controllo_ui=TipoControlloUI.SELECT,
                descrizione="Diametro del prodotto finale",
                is_root=False,
                attivo=True
            )
            db.session.add(parametro_diametro)
            db.session.commit()
            print("✓ Creato parametro 'Diametro'")
        else:
            print("✓ Parametro 'Diametro' già esistente")
        
        # 2. Crea parametro Tecnica se non esiste
        parametro_tecnica = Parametri.query.filter_by(nome_parametro="Tecnica").first()
        if not parametro_tecnica:
            parametro_tecnica = Parametri(
                nome_parametro="Tecnica",
                tipo_controllo_ui=TipoControlloUI.SELECT,
                descrizione="Tecnica di lavorazione",
                is_root=False,
                attivo=True
            )
            db.session.add(parametro_tecnica)
            db.session.commit()
            print("✓ Creato parametro 'Tecnica'")
            
            # Aggiungi alcuni valori di esempio
            valori_tecnica = [
                {"testo_visualizzato_ui": "Tecnica CAD/CAM", "ordine_visualizzazione": 1},
                {"testo_visualizzato_ui": "Tecnica Tradizionale", "ordine_visualizzazione": 2},
                {"testo_visualizzato_ui": "Tecnica Ibrida", "ordine_visualizzazione": 3},
            ]
            
            for val_data in valori_tecnica:
                valore = ValoriParametro(
                    id_parametro=parametro_tecnica.id_parametro,
                    **val_data
                )
                db.session.add(valore)
            
            db.session.commit()
            print("✓ Aggiunti valori di esempio per 'Tecnica'")
        else:
            print("✓ Parametro 'Tecnica' già esistente")
        
        # 3. Crea parametro Connessione se non esiste
        parametro_connessione = Parametri.query.filter_by(nome_parametro="Connessione").first()
        if not parametro_connessione:
            parametro_connessione = Parametri(
                nome_parametro="Connessione",
                tipo_controllo_ui=TipoControlloUI.SELECT,
                descrizione="Tipo di connessione",
                is_root=False,
                attivo=True
            )
            db.session.add(parametro_connessione)
            db.session.commit()
            print("✓ Creato parametro 'Connessione'")
            
            # Aggiungi alcuni valori di esempio
            valori_connessione = [
                {"testo_visualizzato_ui": "Connessione Interna", "ordine_visualizzazione": 1},
                {"testo_visualizzato_ui": "Connessione Esterna", "ordine_visualizzazione": 2},
                {"testo_visualizzato_ui": "Connessione Conica", "ordine_visualizzazione": 3},
            ]
            
            for val_data in valori_connessione:
                valore = ValoriParametro(
                    id_parametro=parametro_connessione.id_parametro,
                    **val_data
                )
                db.session.add(valore)
            
            db.session.commit()
            print("✓ Aggiunti valori di esempio per 'Connessione'")
        else:
            print("✓ Parametro 'Connessione' già esistente")
        
        # 4. Verifica che esistano tutti i prerequisiti
        print("\n=== Verifica Prerequisiti ===")
        
        # Lavorazioni
        param_lavorazioni = Parametri.query.filter_by(nome_parametro="Lavorazioni").first()
        if param_lavorazioni:
            elemento_singolo = ValoriParametro.query.filter_by(
                id_parametro=param_lavorazioni.id_parametro,
                testo_visualizzato_ui="Elemento Singolo Avvitato"
            ).first()
            if elemento_singolo:
                print("✓ Lavorazione 'Elemento Singolo Avvitato' trovata")
            else:
                print("⚠ Lavorazione 'Elemento Singolo Avvitato' NON trovata")
        else:
            print("⚠ Parametro 'Lavorazioni' NON trovato")
        
        # Tipologia Impianto
        param_tipologia = Parametri.query.filter_by(nome_parametro="Tipologia Impianto").first()
        if param_tipologia:
            print("✓ Parametro 'Tipologia Impianto' trovato")
        else:
            print("⚠ Parametro 'Tipologia Impianto' NON trovato")
        
        print("\n=== Setup Completato ===")
        print("Il sistema è ora pronto per la gestione prodotti!")
        print("\nEndpoint disponibili:")
        print("- GET /admin/products - Lista prodotti")
        print("- POST /admin/products - Crea prodotto")
        print("- GET /admin/products/prerequisites - Prerequisiti")
        print("- GET /admin/products/<id> - Dettaglio prodotto")
        print("- PUT /admin/products/<id> - Aggiorna prodotto")
        print("- DELETE /admin/products/<id> - Elimina prodotto")

if __name__ == "__main__":
    setup_products_system()