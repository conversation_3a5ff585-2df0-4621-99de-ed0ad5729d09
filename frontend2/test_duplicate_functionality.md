# Test della Funzionalità di Duplicazione Regole

## Descrizione
È stata implementata la funzionalità di duplicazione delle regole di dipendenza nel frontend2.

## Implementazione

### Backend (app/api_admin/routes.py)
- Aggiunto endpoint `POST /admin/dependency-rules/<id>/duplicate`
- La regola duplicata ha il nome originale + " - Copia"
- La regola duplicata inizia come inattiva per sicurezza
- Vengono duplicate tutte le condizioni e risultati associati

### Frontend (frontend2/)

#### Hook (src/hooks/useDependencyRules.ts)
- Aggiunto `useDuplicateDependencyRule()` hook
- Gestisce la chiamata API e l'invalidazione della cache

#### UI (src/pages/DependencyRulesListPage.tsx)
- Aggiunto pulsante di duplicazione con icona `DocumentDuplicateIcon`
- Posizionato tra il pulsante di modifica e quello di eliminazione
- Colore primario per distinguerlo dagli altri pulsanti
- Conferma utente prima della duplicazione
- Toast di successo/errore

## Funzionalità
1. **Duplicazione completa**: Copia la regola con tutte le condizioni e risultati
2. **Sicurezza**: La regola duplicata è inattiva di default
3. **Feedback utente**: Conferma prima dell'azione e notifiche di successo/errore
4. **Aggiornamento automatico**: La lista si aggiorna automaticamente dopo la duplicazione

## Test Manuale
1. Accedere alla pagina delle regole di dipendenza
2. Cliccare sul pulsante di duplicazione (icona documenti sovrapposti)
3. Confermare l'azione nel dialog
4. Verificare che la nuova regola appaia nella lista con nome "Originale - Copia"
5. Verificare che la regola duplicata sia inattiva
6. Verificare che tutte le condizioni e risultati siano stati copiati