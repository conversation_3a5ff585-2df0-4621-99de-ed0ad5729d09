This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where content has been compressed (code blocks are separated by ⋮---- delimiter).

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
- Pay special attention to the Repository Description. These contain important context and guidelines specific to this project.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>
<user_provided_header>
This repository contains the source code for the Repomix tool.
Repomix is designed to pack repository contents into a single file,
making it easier for AI systems to analyze and process the codebase.

Key Features:
- Configurable ignore patterns
- Custom header text support
- Efficient file processing and packing

Please refer to the README.md file for more detailed information on usage and configuration.

</user_provided_header>

</additional_info>

</file_summary>

<directory_structure>
public/
  vite.svg
src/
  components/
    CondizioneFormItem.tsx
    ProtectedRoute.tsx
    RisultatoFormItem.tsx
  contexts/
    AuthContext.tsx
  hooks/
    useDependencyRules.ts
    useParametersForSelect.ts
  layouts/
    AdminLayout.tsx
    AuthLayout.tsx
  pages/
    AdminDashboard.tsx
    DependencyRuleFormPage.tsx
    DependencyRulesListPage.tsx
    LoginPage.tsx
    ParameterFormPage.tsx
    ParametersListPage.tsx
    UserFormPage.tsx
    UsersListPage.tsx
    ValueParameterFormPage.tsx
    ValueParametersListPage.tsx
  schemas/
    dependencyRuleSchemas.ts
  services/
    api.ts
  types/
    auth.ts
    dependency-rules.ts
    parameters.ts
    users.ts
    value-parameters.ts
  App.css
  App.tsx
  env.d.ts
  index.css
  logo.svg
  main.tsx
  react-app-env.d.ts
  setupTests.ts
.gitignore
.npmrc
eslint.config.mjs
index.html
LICENSE
package.json
postcss.config.js
README.md
tailwind.config.js
tsconfig.json
tsconfig.node.json
vercel.json
vite.config.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="public/vite.svg">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--logos" width="31.88" height="32" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 257"><defs><linearGradient id="IconifyId1813088fe1fbc01fb466" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%"><stop offset="0%" stop-color="#41D1FF"></stop><stop offset="100%" stop-color="#BD34FE"></stop></linearGradient><linearGradient id="IconifyId1813088fe1fbc01fb467" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%"><stop offset="0%" stop-color="#FFEA83"></stop><stop offset="8.333%" stop-color="#FFDD35"></stop><stop offset="100%" stop-color="#FFA800"></stop></linearGradient></defs><path fill="url(#IconifyId1813088fe1fbc01fb466)" d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z"></path><path fill="url(#IconifyId1813088fe1fbc01fb467)" d="M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z"></path></svg>
</file>

<file path="src/components/CondizioneFormItem.tsx">
import { useFormContext, Controller, FieldErrors } from 'react-hook-form';
import { useGetParameterValuesForSelect } from '../hooks/useParametersForSelect';
import { RegolaDipendenzaComplessaFormSchema, CondizioneRegolaFormSchema } from '../schemas/dependencyRuleSchemas'; // Importa gli schemi centralizzati
import { z } from 'zod';
⋮----
type RootFormInput = z.infer<typeof RegolaDipendenzaComplessaFormSchema>;
⋮----
// Non serve Ridefinire qui lo schema, lo importiamo
// type FormInput = z.infer<typeof CondizioneRegolaFormSchema>; // Tipo per la singola condizione
⋮----
interface ICondizioneFormItemProps {
  index: number;
  remove: (index?: number | number[]) => void;
  allParameters: { value: number; label: string }[];
  // control: any; // Non passare control qui, useFormContext lo fornisce
}
⋮----
// control: any; // Non passare control qui, useFormContext lo fornisce
⋮----
// Watch sul parametro_id della condizione corrente per abilitare il select successivo
⋮----
// Converti null in undefined per la funzione useGetParameterValuesForSelect
</file>

<file path="src/components/ProtectedRoute.tsx">
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext'; // Importa useAuth
⋮----
interface ProtectedRouteProps {
  children: React.ReactNode;
}
⋮----
const ProtectedRoute: React.FC<ProtectedRouteProps> = (
⋮----
const { isAuthenticated, loading } = useAuth(); // Ottiene lo stato di autenticazione dal contesto
⋮----
return <div>Caricamento autenticazione...</div>; // O uno spinner/componente di caricamento
⋮----
// Reindirizza l'utente alla pagina di login se non autenticato
</file>

<file path="src/components/RisultatoFormItem.tsx">
import React from 'react';
import { useFormContext, Controller, FieldErrors } from 'react-hook-form';
import { useGetParameterValuesForSelect } from '../hooks/useParametersForSelect';
import { RegolaDipendenzaComplessaFormSchema, RisultatoRegolaFormSchema } from '../schemas/dependencyRuleSchemas';
import { z } from 'zod';
⋮----
type RootFormInput = z.infer<typeof RegolaDipendenzaComplessaFormSchema>;
⋮----
// Non serve Ridefinire qui lo schema, lo importiamo
// type FormInput = z.infer<typeof RisultatoRegolaFormSchema>; // Tipo per il singolo risultato
⋮----
interface IRisultatoFormItemProps {
  index: number;
  remove: (index?: number | number[]) => void;
  activeParameters: { value: number; label: string }[];
  // control: any; // Non passare control qui, useFormContext lo fornisce
}
⋮----
// control: any; // Non passare control qui, useFormContext lo fornisce
</file>

<file path="src/contexts/AuthContext.tsx">
import React, { createContext, useReducer, useEffect, ReactNode, useContext } from 'react';
import { CurrentUser, AuthState, AuthAction, LoginFormData } from '../types/auth';
⋮----
// Definizione dello stato iniziale
⋮----
// Reducer per la gestione dello stato di autenticazione
const authReducer = (state: AuthState, action: AuthAction): AuthState =>
⋮----
// Definizione del tipo per il contesto
interface AuthContextType extends AuthState {
  login: (credentials: LoginFormData) => Promise<void>;
  logout: () => void;
}
⋮----
// Creazione del contesto
⋮----
// Provider dell'autenticazione
interface AuthProviderProps {
  children: ReactNode;
}
⋮----
export const AuthProvider: React.FC<AuthProviderProps> = (
⋮----
// Carica lo stato di autenticazione da localStorage all'avvio dell'applicazione
⋮----
const storedToken = localStorage.getItem('access_token'); // Modificato da 'token' a 'access_token'
const storedUser = localStorage.getItem('user'); // Modificato da 'currentUser' a 'user'
⋮----
const login = async (credentials: LoginFormData) =>
⋮----
// Qui verrà fatta la chiamata API per l'autenticazione
// Per ora, simuliamo una chiamata API di successo
⋮----
// Salva token e utente in localStorage
// Rimuovi la simulazione della chiamata API e usa la logica reale
// La chiamata API e il salvataggio in localStorage sono gestiti in LoginPage.tsx
// Questo blocco non dovrebbe più essere responsabile del salvataggio del token/utente
// in quanto LoginPage.tsx lo fa già.
// Se la logica di login fosse qui, la chiamata axios dovrebbe essere qui.
// Per ora, assumiamo che LoginPage.tsx gestisca la chiamata e il salvataggio.
// Quindi, il dispatch dovrebbe avvenire dopo che LoginPage.tsx ha salvato i dati.
// Questo è un punto critico da rivedere per una gestione più pulita.
⋮----
// Per ora, per far funzionare il flusso, dispatchiamo LOGIN_SUCCESS basandoci
// sul fatto che LoginPage.tsx ha già salvato i dati.
// In una refactorizzazione futura, la logica di login API dovrebbe essere qui.
⋮----
const logout = () =>
⋮----
localStorage.removeItem('access_token'); // Modificato da 'token' a 'access_token'
⋮----
// Hook personalizzato per usare il contesto di autenticazione
export const useAuth = () =>
</file>

<file path="src/hooks/useDependencyRules.ts">
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { get, post, put, del } from '../services/api';
import { IRegolaDipendenzaComplessa, IRegolaDipendenzaComplessaFormPayload } from '../types/dependency-rules';
import { AxiosError } from 'axios';
⋮----
// Hook per ottenere tutte le regole di dipendenza complesse
export const useGetDependencyRules = () =>
⋮----
// Hook per ottenere una singola regola di dipendenza complessa
export const useGetDependencyRule = (id: number) =>
⋮----
enabled: !!id, // Abilita la query solo se l'ID è presente
⋮----
// Hook per creare una nuova regola di dipendenza complessa
export const useCreateDependencyRule = () =>
⋮----
// Hook per aggiornare una regola di dipendenza complessa esistente
export const useUpdateDependencyRule = () =>
⋮----
// Hook per eliminare una regola di dipendenza complessa
export const useDeleteDependencyRule = () =>
</file>

<file path="src/hooks/useParametersForSelect.ts">
import { useQuery } from '@tanstack/react-query';
import { get } from '../services/api';
⋮----
interface ISelectOption {
  value: number;
  label: string;
}
⋮----
// Hook per ottenere tutti i parametri per le select (id, nome)
export const useGetAllParametersForSelect = () =>
⋮----
// Hook per ottenere i valori di un parametro specifico per le select
export const useGetParameterValuesForSelect = (parameterId: number | undefined) =>
⋮----
enabled: !!parameterId, // Abilita la query solo se parameterId è presente
⋮----
// Hook per ottenere i parametri attivi (non is_root) per le select
export const useGetActiveParametersForSelect = () =>
⋮----
// Assumiamo che l'endpoint restituisca solo parametri non is_root o che questa logica sia lato backend
// Se non esiste un endpoint specifico, si può filtrare useGetAllParametersForSelect
const response = await get<ISelectOption[]>('/api/v1/admin/parameters/all-for-select'); // Placeholder, adattare se l'endpoint cambia
// Filtra qui se l'API non fornisce direttamente un endpoint per 'is_root=false'
// return response.data.filter(param => !param.is_root);
</file>

<file path="src/layouts/AdminLayout.tsx">
import React, { PropsWithChildren } from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  AdjustmentsHorizontalIcon,
  SparklesIcon, // Importa una nuova icona per le regole di dipendenza
} from '@heroicons/react/24/outline'; // Importa le icone necessarie
⋮----
SparklesIcon, // Importa una nuova icona per le regole di dipendenza
} from '@heroicons/react/24/outline'; // Importa le icone necessarie
⋮----
{ name: 'Dashboard', href: '/admin', icon: HomeIcon }, // Rotta per la dashboard
{ name: 'Parametri', href: '/admin/parameters', icon: Cog6ToothIcon }, // Rotta per i parametri
{ name: 'Valori Parametro', href: '/admin/parameters/:parameterId/values', icon: AdjustmentsHorizontalIcon }, // Rotta per i valori dei parametri
{ name: 'Utenti', href: '/admin/users', icon: UserGroupIcon }, // Rotta per gli utenti
{ name: 'Regole Dipendenza', href: '/admin/dependency-rules', icon: SparklesIcon }, // Nuova rotta per le regole di dipendenza
// Aggiungi qui altre voci di navigazione se necessario
⋮----
{/* Sidebar */}
⋮----
{/* Main Content Area */}
⋮----
{/* Navbar (Placeholder) */}
⋮----
<h1 className="text-xl font-semibold">Dashboard</h1> {/* Potrebbe essere dinamico */}
{/* Altri elementi della navbar (es. profilo utente) */}
⋮----
{/* Page Content */}
⋮----
{/* Footer (Placeholder) */}
</file>

<file path="src/layouts/AuthLayout.tsx">
import React from 'react';
import { Outlet } from 'react-router-dom'; // Importa Outlet
⋮----
const AuthLayout: React.FC = () =>
⋮----
<Outlet /> {/* Qui verranno renderizzate le rotte nidificate */}
</file>

<file path="src/pages/AdminDashboard.tsx">
import React from 'react';
⋮----
const AdminDashboard: React.FC = () =>
⋮----
{/* Contenuto futuro della dashboard verrà aggiunto qui */}
</file>

<file path="src/pages/DependencyRuleFormPage.tsx">
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, useFieldArray, Controller, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
⋮----
import { toast } from 'react-toastify';
⋮----
import {
  useGetDependencyRule,
  useCreateDependencyRule,
  useUpdateDependencyRule,
} from '../hooks/useDependencyRules';
import {
  useGetAllParametersForSelect,
  useGetActiveParametersForSelect,
} from '../hooks/useParametersForSelect';
⋮----
// Importa gli schemi Zod centralizzati
import {
  RegolaDipendenzaComplessaFormSchema,
  RegolaDipendenzaComplessaPayloadSchema,
} from '../schemas/dependencyRuleSchemas';
⋮----
// Importa i nuovi componenti per le voci singole
import CondizioneFormItem from '../components/CondizioneFormItem';
import RisultatoFormItem from '../components/RisultatoFormItem';
⋮----
type FormInput = z.infer<typeof RegolaDipendenzaComplessaFormSchema>;
type ApiPayload = z.infer<typeof RegolaDipendenzaComplessaPayloadSchema>;
⋮----
// Mappa i dati della regola in arrivo dal backend per adattarli al FormInput
⋮----
// Converti 0 in null se necessario per il form, altrimenti usa il valore
⋮----
const onSubmit = async (data: FormInput) =>
⋮----
// Trasforma i dati del form nel payload API definitivo
⋮----
.filter(cond => cond.parametro_id !== null && cond.parametro_id !== undefined) // Rimuovi righe con parametro_id non selezionato
⋮----
parametro_id: cond.parametro_id!, // Garantito da filter
⋮----
.filter(res => res.parametro_attivo_id !== null && res.parametro_attivo_id !== undefined) // Rimuovi righe con parametro_attivo_id non selezionato
⋮----
parametro_attivo_id: res.parametro_attivo_id!, // Garantito da filter
⋮----
// Validiamo il payload finale con lo schema dedicato ai payload API
⋮----
// Migliore gestione degli errori di validazione Zod
</file>

<file path="src/pages/DependencyRulesListPage.tsx">
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useGetDependencyRules, useDeleteDependencyRule } from '../hooks/useDependencyRules';
⋮----
const handleDelete = (id: number) =>
</file>

<file path="src/pages/LoginPage.tsx">
import React, { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input, Button } from '@heroui/react'; // Tornato all'importazione aggregata per HeroUI
import { useNavigate } from 'react-router-dom';
import axios, { isAxiosError } from 'axios'; // Importa isAxiosError
import { LoginFormData, AuthResponse } from '../types/auth'; // Importa le interfacce definite
⋮----
// Schema di validazione Zod basato sull'interfaccia LoginFormData
⋮----
const onSubmit: SubmitHandler<LoginFormData> = async (data) =>
⋮----
reset(); // Resetta il form dopo il login
navigate('/admin/dashboard'); // Reindirizza l'utente alla dashboard di amministrazione dopo il login
</file>

<file path="src/pages/ParameterFormPage.tsx">
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
⋮----
import { IParameterFormPayload, IParameter } from '../types/parameters';
import { get, post, put } from '../services/api'; // Importa le funzioni get, post, put da api.ts
⋮----
// Schema di validazione per i dati dell'utente - versione semplificata per il form
⋮----
// Schema di trasformazione per convertire i dati del form in payload API
⋮----
// Tipi inferiti dagli schemi Zod
type FormSchemaType = z.infer<typeof parameterFormSchema>;
⋮----
const fetchParameter = async (id: string): Promise<IParameter> => { // Il fetch restituisce IParameter
const response = await get(`/admin/parametri/${id}`); // Usa la funzione get di api.ts
⋮----
const createParameter = async (data: IParameterFormPayload): Promise<void> =>
⋮----
await post('/admin/parametri', data); // Usa la funzione post di api.ts
⋮----
const updateParameter = async (id: string, data: IParameterFormPayload): Promise<void> =>
⋮----
await put(`/admin/parametri/${id}`, data); // Usa la funzione put di api.ts
⋮----
} = useForm<FormSchemaType>({ // Usa il tipo del form schema
⋮----
defaultValues: { // Aggiunto defaultValues per checkbox
obbligatorio: false, // Default per il campo checkbox
⋮----
const { data, error, isLoading } = useQuery<IParameter, Error>({ // Usa IParameter per la query
⋮----
// Effettua il reset con i dati dell'API, assicurandosi che valore_default sia una stringa per l'input
⋮----
: '' // Converti in stringa per l'input HTML
⋮----
mutationFn: (formData) => { // formData qui è già di tipo IParameterFormPayload grazie alla trasformazione di Zod
⋮----
const onSubmit = (formData: FormSchemaType) =>
⋮----
// Applica la trasformazione per convertire i dati del form in payload API
⋮----
<form onSubmit=
⋮----
<button type="button" onClick=
</file>

<file path="src/pages/ParametersListPage.tsx">
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useNavigate } from 'react-router-dom';
⋮----
import { IParameter } from '../types/parameters';
import { get, del } from '../services/api'; // Importa le funzioni get e del da api.ts
⋮----
const fetchParameters = async (): Promise<IParameter[]> =>
⋮----
const response = await get('/admin/parametri'); // Usa la funzione get di api.ts
⋮----
const deleteParameter = async (id: number): Promise<void> =>
⋮----
await del(`/admin/parametri/${id}`); // Usa la funzione del di api.ts
⋮----
const handleDelete = (id: number) =>
</file>

<file path="src/pages/UserFormPage.tsx">
import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
⋮----
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { get, post, put } from '../services/api';
import { IUserFormPayload, IUser } from '../types/users';
import AdminLayout from '../layouts/AdminLayout'; // Assicurati di avere un AdminLayout
import { toast } from 'react-toastify';
⋮----
// Estende IUserFormPayload per includere il campo confirmPassword per il form
interface IUserFormInput extends IUserFormPayload {
  confirmPassword?: string;
}
⋮----
// Schema di validazione per i dati dell'utente
⋮----
password: z.string().optional(), // La password è opzionale qui, gestita separatamente per la modifica
⋮----
enabled: isEditMode, // Esegui la query solo in modalità modifica
⋮----
// Non resettiamo i campi password in modalità modifica per mantenerli vuoti di default
⋮----
const onSubmit = (data: IUserFormInput) =>
</file>

<file path="src/pages/UsersListPage.tsx">
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useNavigate } from 'react-router-dom';
import { del, get } from '../services/api';
import { IUser } from '../types/users';
import AdminLayout from '../layouts/AdminLayout'; // Assicurati di avere un AdminLayout
// import { toast } from 'react-toastify'; // Importa toast per le notifiche
// import 'react-toastify/dist/ReactToastify.css'; // Importa i CSS del toast
⋮----
const handleDelete = (id: number) =>
</file>

<file path="src/pages/ValueParameterFormPage.tsx">
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { IValueParameterFormPayload, IValueParameter } from '../types/value-parameters';
⋮----
// Definizione dello schema Zod per la validazione del form
⋮----
foto: z.any().optional(), // Gestito separatamente per l'upload di file
⋮----
// Aggiungiamo IValueParameter[keyof IValueParameter] al tipo `valore` per permettere i tipi esistenti da API
type ValueParameterFormData = Omit<IValueParameterFormPayload, 'parametro_id' | 'foto'> & {
  valore: IValueParameter['valore'] | string; // Permette sia i tipi esistenti che stringhe per input
  foto?: FileList; // Per gestire l'upload del file
};
⋮----
valore: IValueParameter['valore'] | string; // Permette sia i tipi esistenti che stringhe per input
foto?: FileList; // Per gestire l'upload del file
⋮----
const fetchValueParameter = async (parameterId: string, valueId: string): Promise<IValueParameter> =>
⋮----
const saveValueParameter = async (parameterId: string, data: FormData, valueId?: string): Promise<void> =>
⋮----
body: data, // FormData gestisce automaticamente l'header Content-Type per multipart/form-data
⋮----
watch, // Aggiunto watch
⋮----
mode: 'onBlur', // Aggiunto per Triggerare la validazione on blur
⋮----
enabled: !!valueId && !!parameterId, // Abilita solo se entrambi gli ID sono presenti per la modifica
⋮----
// Non resettare il campo foto direttamente, perché è un FileList
// La gestione dell'upload sarà separata
⋮----
// Aggiungi la logica per l'upload del file
⋮----
// Se si sta modificando e non viene fornita una nuova foto,
// e la foto esistente è stata rimossa (come da form di modifica che pulisce il campo),
// potremmo voler inviare un segnale per rimuoverla lato backend.
// Per ora, non inviamo nulla se il campo è vuoto.
// Potrebbe essere implementata una logica `set_foto_null: true` nel payload se necessario.
⋮----
const onSubmit = (formData: ValueParameterFormData) =>
⋮----
<form onSubmit=
⋮----
{/* Opzione per rimuovere la foto esistente se necessario, si può aggiungere un input hidden o un checkbox */}
⋮----
<button type="button" onClick=
</file>

<file path="src/pages/ValueParametersListPage.tsx">
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { IValueParameter } from '../types/value-parameters';
⋮----
const fetchValueParameters = async (parameterId: string | undefined): Promise<IValueParameter[]> =>
⋮----
const deleteValueParameter = async (payload:
⋮----
enabled: !!parameterId, // Abilita la query solo se parameterId è presente
⋮----
const handleDelete = (valueId: number) =>
</file>

<file path="src/schemas/dependencyRuleSchemas.ts">
// Schemi Zod per i dati del form (compatibili con gli input HTML)
⋮----
id_condizione: z.union([z.number(), z.string().uuid()]).optional(), // UUID temporaneo o ID esistente
parametro_id: z.number().int().positive('L\'ID del parametro deve essere un numero positivo').nullable(), // Permette undefined temporaneamente per la selezione
⋮----
id_risultato: z.union([z.number(), z.string().uuid()]).optional(), // UUID temporaneo o ID esistente
parametro_attivo_id: z.number().int().positive('L\'ID del parametro attivo deve essere un numero positivo').nullable(), // Permette undefined temporaneamente
⋮----
id_regola: z.union([z.number(), z.string().uuid()]).optional(), // Opzionale per la creazione
⋮----
// Schemi Zod per i payload API (dopo la trasformazione)
⋮----
id_condizione: z.number().optional(), // ID numerico per elementi esistenti
⋮----
id_risultato: z.number().optional(), // ID numerico per elementi esistenti
</file>

<file path="src/services/api.ts">
import axios, { AxiosInstance, AxiosResponse } from 'axios';
⋮----
// baseURL per l'API del backend
⋮----
// Interceptor per aggiungere il token JWT a tutte le richieste
⋮----
// Tentativo di recuperare il token da localStorage
⋮----
// Se il token esiste, aggiungilo all'header Authorization
⋮----
/**
 * Funzione di utility per le richieste GET tipizzate.
 * @param url L'URL dell'endpoint.
 * @returns Una Promise con i dati della risposta.
 */
export const get = async <T = any, R = AxiosResponse<T>>(url: string): Promise<R> =>
⋮----
/**
 * Funzione di utility per le richieste POST tipizzate.
 * @param url L'URL dell'endpoint.
 * @param data I dati da inviare nel corpo della richiesta.
 * @returns Una Promise con i dati della risposta.
 */
export const post = async <T = any, D = any, R = AxiosResponse<T>>(url: string, data?: D): Promise<R> =>
⋮----
/**
 * Funzione di utility per le richieste PUT tipizzate.
 * @param url L'URL dell'endpoint.
 * @param data I dati da inviare nel corpo della richiesta.
 * @returns Una Promise con i dati della risposta.
 */
export const put = async <T = any, D = any, R = AxiosResponse<T>>(url: string, data?: D): Promise<R> =>
⋮----
/**
 * Funzione di utility per le richieste DELETE tipizzate.
 * @param url L'URL dell'endpoint.
 * @returns Una Promise con i dati della risposta.
 */
export const del = async <T = any, R = AxiosResponse<T>>(url: string): Promise<R> =>
</file>

<file path="src/types/auth.ts">
export interface LoginFormData {
  username: string;
  password: string;
}
⋮----
export interface AuthResponse {
  access_token: string;
  user: {
    id: number;
    username: string;
    nome_completo: string;
  };
}
⋮----
export interface CurrentUser {
  id: number;
  username: string;
  nome_completo: string | null;
}
⋮----
export interface AuthState {
  token: string | null;
  currentUser: CurrentUser | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}
⋮----
export type AuthAction =
  | { type: 'LOGIN_SUCCESS'; payload: { token: string; user: CurrentUser } }
  | { type: 'LOGIN_FAIL'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'LOADING' };
</file>

<file path="src/types/dependency-rules.ts">
// frontend/src/types/dependency-rules.ts
⋮----
/**
 * @interface ICondizioneRegola
 * Rappresenta una singola condizione associata a una regola di dipendenza complessa come ricevuta dal backend.
 */
export interface ICondizioneRegola {
  id_condizione: number;
  parametro_id: number;
  valore_parametro_id: number | null; // Nullable se il parametro non ha un valore specifico (es. condizione "è presente")
}
⋮----
valore_parametro_id: number | null; // Nullable se il parametro non ha un valore specifico (es. condizione "è presente")
⋮----
/**
 * @interface ICondizioneRegolaFormPayload
 * Rappresenta una singola condizione nel payload del form per la creazione/modifica di una regola.
 * Gli ID possono essere stringhe (per i nuovi elementi non ancora salvati) o numeri.
 */
export interface ICondizioneRegolaFormPayload {
    id_condizione?: number | string; // Opzionale per i nuovi elementi, può essere stringa temporanea
    parametro_id: number;
    valore_parametro_id: number | null;
}
⋮----
id_condizione?: number | string; // Opzionale per i nuovi elementi, può essere stringa temporanea
⋮----
/**
 * @interface IRisultatoRegola
 * Rappresenta un singolo risultato associato a una regola di dipendenza complessa come ricevuta dal backend.
 */
export interface IRisultatoRegola {
  id_risultato: number;
  parametro_attivo_id: number;
  valori_attivi_id: number[]; // Array di ID dei valori attivi (es. "mostra opzioni 1, 2, 3")
}
⋮----
valori_attivi_id: number[]; // Array di ID dei valori attivi (es. "mostra opzioni 1, 2, 3")
⋮----
/**
 * @interface IRisultatoRegolaFormPayload
 * Rappresenta un singolo risultato nel payload del form per la creazione/modifica di una regola.
 * Gli ID possono essere stringhe (per i nuovi elementi non ancora salvati) o numeri.
 */
export interface IRisultatoRegolaFormPayload {
    id_risultato?: number | string; // Opzionale per i nuovi elementi, può essere stringa temporanea
    parametro_attivo_id: number;
    valori_attivi_id: number[];
}
⋮----
id_risultato?: number | string; // Opzionale per i nuovi elementi, può essere stringa temporanea
⋮----
/**
 * @interface IRegolaDipendenzaComplessa
 * Rappresenta una regola di dipendenza complessa completa come ricevuta dal backend.
 */
export interface IRegolaDipendenzaComplessa {
  id_regola: number;
  nome_regola: string;
  condizioni: ICondizioneRegola[];
  risultati: IRisultatoRegola[];
}
⋮----
/**
 * @interface IRegolaDipendenzaComplessaFormPayload
 * Rappresenta il payload per la creazione o modifica di una regola di dipendenza complessa dal form.
 * Permette l'utilizzo di ID temporanei (stringhe) per i nuovi elementi non ancora salvati.
 */
export interface IRegolaDipendenzaComplessaFormPayload {
    id_regola?: number | string; // Opzionale per la creazione, può essere stringa temporanea
    nome_regola: string;
    condizioni: ICondizioneRegolaFormPayload[];
    risultati: IRisultatoRegolaFormPayload[];
}
⋮----
id_regola?: number | string; // Opzionale per la creazione, può essere stringa temporanea
</file>

<file path="src/types/parameters.ts">
export interface IParameter {
  id: number;
  nome: string;
  descrizione?: string;
  tipo: string;
  obbligatorio: boolean;
  valore_default?: string | number | boolean | null;
  data_creazione: string;
  data_ultima_modifica: string;
}
⋮----
export interface IParameterFormPayload {
  nome: string;
  descrizione?: string;
  tipo: string;
  obbligatorio: boolean;
  valore_default?: string | number | boolean | null;
}
</file>

<file path="src/types/users.ts">
export interface IUser {
  id: number;
  username: string;
  email: string;
  nome_completo: string;
  attivo: boolean;
  data_creazione: string;
  data_ultima_modifica: string;
}
⋮----
export interface IUserFormPayload {
  username: string;
  email: string;
  nome_completo: string;
  attivo: boolean;
  password?: string; // per creazione o modifica password
}
⋮----
password?: string; // per creazione o modifica password
</file>

<file path="src/types/value-parameters.ts">
export interface IValueParameter {
  id: number;
  parametro_id: number;
  valore: string | number | boolean | null;
  foto?: string;
  colore?: string;
  descrizione?: string;
  data_creazione: string;
  data_ultima_modifica: string;
}
⋮----
export interface IValueParameterFormPayload {
  parametro_id: number;
  valore: string | number | boolean | null;
  foto?: string;
  colore?: string;
  descrizione?: string;
}
</file>

<file path="src/App.css">
.App {
⋮----
.App-logo {
⋮----
.App-header {
⋮----
.App-link {
</file>

<file path="src/App.tsx">
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute'; // Importa il componente ProtectedRoute
import LoginPage from './pages/LoginPage'; // Importa la pagina di Login
import AuthLayout from './layouts/AuthLayout'; // Importa il layout di autenticazione
import AdminLayout from './layouts/AdminLayout'; // Importa il layout di amministrazione
import { AuthProvider } from './contexts/AuthContext'; // Importa AuthProvider
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'; // Importa TanStack Query
⋮----
import ParametersListPage from './pages/ParametersListPage';
import ParameterFormPage from './pages/ParameterFormPage';
import ValueParametersListPage from './pages/ValueParametersListPage'; // Importa la pagina della lista dei valori dei parametri
import ValueParameterFormPage from './pages/ValueParameterFormPage'; // Importa la pagina del form dei valori dei parametri
import UsersListPage from './pages/UsersListPage'; // Importa la pagina della lista degli utenti
import UserFormPage from './pages/UserFormPage'; // Importa la pagina del form degli utenti
⋮----
import DependencyRulesListPage from './pages/DependencyRulesListPage'; // Importa la pagina della lista delle regole di dipendenza
import DependencyRuleFormPage from './pages/DependencyRuleFormPage'; // Importa la pagina del form delle regole di dipendenza
⋮----
// Componenti di placeholder per le schermate future
const AdminDashboard = ()
⋮----
const NotFound = ()
⋮----
// Crea un'istanza di QueryClient con configurazioni di default
⋮----
staleTime: 1000 * 60 * 5, // 5 minuti
⋮----
// Componente App principale con la configurazione del routing e QueryClientProvider
function App()
⋮----
<AuthProvider> {/* Avvolge l'intera applicazione con AuthProvider */}
⋮----
<Route index element={<LoginPage />} /> {/* Rotta predefinita per la pagina di login */}
<Route path="login" element={<LoginPage />} /> {/* Rotta esplicita per la pagina di login */}
⋮----
{/* Proteggi la rotta /admin/* con ProtectedRoute, non è più necessario passare isAuthenticated */}
⋮----
<AdminLayout> {/* Avvolge le rotte di amministrazione con AdminLayout */}
⋮----
{/* Nuove rotte per le regole di dipendenza */}
⋮----
<Route path="*" element={<NotFound />} /> {/* Catch-all per le rotte non definite */}
</file>

<file path="src/env.d.ts">
/// <reference types="vite/client" />
⋮----
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  // aggiungi qui altre variabili d'ambiente che iniziano con VITE_
  // readonly VITE_ALTRA_VAR: string;
}
⋮----
// aggiungi qui altre variabili d'ambiente che iniziano con VITE_
// readonly VITE_ALTRA_VAR: string;
⋮----
interface ImportMeta {
  readonly env: ImportMetaEnv;
}
</file>

<file path="src/index.css">
@tailwind base;
@tailwind components;
@tailwind utilities;
⋮----
body {
⋮----
code {
</file>

<file path="src/logo.svg">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 841.9 595.3"><g fill="#61DAFB"><path d="M666.3 296.5c0-32.5-40.7-63.3-103.1-82.4 14.4-63.6 8-114.2-20.2-130.4-6.5-3.8-14.1-5.6-22.4-5.6v22.3c4.6 0 8.3.9 11.4 2.6 13.6 7.8 19.5 37.5 14.9 75.7-1.1 9.4-2.9 19.3-5.1 29.4-19.6-4.8-41-8.5-63.5-10.9-13.5-18.5-27.5-35.3-41.6-50 32.6-30.3 63.2-46.9 84-46.9V78c-27.5 0-63.5 19.6-99.9 53.6-36.4-33.8-72.4-53.2-99.9-53.2v22.3c20.7 0 51.4 16.5 84 46.6-14 14.7-28 31.4-41.3 49.9-22.6 2.4-44 6.1-63.6 11-2.3-10-4-19.7-5.2-29-4.7-38.2 1.1-67.9 14.6-75.8 3-1.8 6.9-2.6 11.5-2.6V78.5c-8.4 0-16 1.8-22.6 5.6-28.1 16.2-34.4 66.7-19.9 130.1-62.2 19.2-102.7 49.9-102.7 82.3 0 32.5 40.7 63.3 103.1 82.4-14.4 63.6-8 114.2 20.2 130.4 6.5 3.8 14.1 5.6 22.5 5.6 27.5 0 63.5-19.6 99.9-53.6 36.4 33.8 72.4 53.2 99.9 53.2 8.4 0 16-1.8 22.6-5.6 28.1-16.2 34.4-66.7 19.9-130.1 62-19.1 102.5-49.9 102.5-82.3zm-130.2-66.7c-3.7 12.9-8.3 26.2-13.5 39.5-4.1-8-8.4-16-13.1-24-4.6-8-9.5-15.8-14.4-23.4 14.2 2.1 27.9 4.7 41 7.9zm-45.8 106.5c-7.8 13.5-15.8 26.3-24.1 38.2-14.9 1.3-30 2-45.2 2-15.1 0-30.2-.7-45-1.9-8.3-11.9-16.4-24.6-24.2-38-7.6-13.1-14.5-26.4-20.8-39.8 6.2-13.4 13.2-26.8 20.7-39.9 7.8-13.5 15.8-26.3 24.1-38.2 14.9-1.3 30-2 45.2-2 15.1 0 30.2.7 45 1.9 8.3 11.9 16.4 24.6 24.2 38 7.6 13.1 14.5 26.4 20.8 39.8-6.3 13.4-13.2 26.8-20.7 39.9zm32.3-13c5.4 13.4 10 26.8 13.8 39.8-13.1 3.2-26.9 5.9-41.2 8 4.9-7.7 9.8-15.6 14.4-23.7 4.6-8 8.9-16.1 13-24.1zM421.2 430c-9.3-9.6-18.6-20.3-27.8-32 9 .4 18.2.7 27.5.7 9.4 0 18.7-.2 27.8-.7-9 11.7-18.3 22.4-27.5 32zm-74.4-58.9c-14.2-2.1-27.9-4.7-41-7.9 3.7-12.9 8.3-26.2 13.5-39.5 4.1 8 8.4 16 13.1 24 4.7 8 9.5 15.8 14.4 23.4zM420.7 163c9.3 9.6 18.6 20.3 27.8 32-9-.4-18.2-.7-27.5-.7-9.4 0-18.7.2-27.8.7 9-11.7 18.3-22.4 27.5-32zm-74 58.9c-4.9 7.7-9.8 15.6-14.4 23.7-4.6 8-8.9 16-13 24-5.4-13.4-10-26.8-13.8-39.8 13.1-3.1 26.9-5.8 41.2-7.9zm-90.5 125.2c-35.4-15.1-58.3-34.9-58.3-50.6 0-15.7 22.9-35.6 58.3-50.6 8.6-3.7 18-7 27.7-10.1 5.7 19.6 13.2 40 22.5 60.9-9.2 20.8-16.6 41.1-22.2 60.6-9.9-3.1-19.3-6.5-28-10.2zM310 490c-13.6-7.8-19.5-37.5-14.9-75.7 1.1-9.4 2.9-19.3 5.1-29.4 19.6 4.8 41 8.5 63.5 10.9 13.5 18.5 27.5 35.3 41.6 50-32.6 30.3-63.2 46.9-84 46.9-4.5-.1-8.3-1-11.3-2.7zm237.2-76.2c4.7 38.2-1.1 67.9-14.6 75.8-3 1.8-6.9 2.6-11.5 2.6-20.7 0-51.4-16.5-84-46.6 14-14.7 28-31.4 41.3-49.9 22.6-2.4 44-6.1 63.6-11 2.3 10.1 4.1 19.8 5.2 29.1zm38.5-66.7c-8.6 3.7-18 7-27.7 10.1-5.7-19.6-13.2-40-22.5-60.9 9.2-20.8 16.6-41.1 22.2-60.6 9.9 3.1 19.3 6.5 28.1 10.2 35.4 15.1 58.3 34.9 58.3 50.6-.1 15.7-23 35.6-58.4 50.6zM320.8 78.4z"/><circle cx="420.9" cy="296.5" r="45.7"/><path d="M520.5 78.1z"/></g></svg>
</file>

<file path="src/main.tsx">
import React from 'react';
import ReactDOM from 'react-dom/client';
⋮----
import App from './App';
// import reportWebVitals from './reportWebVitals';
⋮----
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals();
</file>

<file path="src/react-app-env.d.ts">
/// <reference types="react-scripts" />
</file>

<file path="src/setupTests.ts">
// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
</file>

<file path=".gitignore">
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


pnpm-lock.yaml
yarn.lock
package-lock.json
bun.lockb
</file>

<file path=".npmrc">
package-lock=true
</file>

<file path="eslint.config.mjs">
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
⋮----
export default defineConfig([
globalIgnores([
⋮----
extends: fixupConfigRules(
compat.extends(
⋮----
react: fixupPluginRules(react),
⋮----
import: fixupPluginRules(_import),
⋮----
"jsx-a11y": fixupPluginRules(jsxA11Y),
prettier: fixupPluginRules(prettier),
⋮----
...Object.fromEntries(
Object.entries(globals.browser).map(([key]) => [key, "off"]),
</file>

<file path="index.html">
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + HeroUI</title>
    <meta key="title" content="Vite + HeroUI" property="og:title" />
    <meta
      content="Make beautiful websites regardless of your design experience."
      property="og:description"
    />
    <meta
      content="Make beautiful websites regardless of your design experience."
      name="description"
    />
    <meta
      key="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <link href="/favicon.ico" rel="icon" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
</file>

<file path="LICENSE">
MIT License

Copyright (c) 2024 Next UI

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
</file>

<file path="package.json">
{
  "name": "vite-template",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint --fix",
    "preview": "vite preview"
  },
  "dependencies": {
    "@heroicons/react": "^2.2.0",
    "@heroui/button": "2.2.21",
    "@heroui/code": "2.2.16",
    "@heroui/dropdown": "2.3.21",
    "@heroui/input": "2.4.21",
    "@heroui/kbd": "2.2.17",
    "@heroui/link": "2.2.18",
    "@heroui/navbar": "2.2.19",
    "@heroui/react": "^2.7.10",
    "@heroui/snippet": "2.2.22",
    "@heroui/switch": "2.2.19",
    "@heroui/system": "2.4.17",
    "@heroui/theme": "2.4.17",
    "@heroui/use-theme": "2.1.9",
    "@hookform/resolvers": "^5.1.1",
    "@react-aria/visually-hidden": "3.8.24",
    "@react-types/shared": "3.30.0",
    "@tanstack/react-query": "^5.80.7",
    "axios": "^1.10.0",
    "clsx": "2.1.1",
    "framer-motion": "^11.15.0",
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "react-hook-form": "^7.58.1",
    "react-router-dom": "^6.23.0",
    "react-toastify": "^11.0.5",
    "tailwind-variants": "0.3.0",
    "tailwindcss": "3.4.16",
    "web-vitals": "^5.0.3",
    "zod": "^3.25.67"
  },
  "devDependencies": {
    "@eslint/compat": "1.2.8",
    "@eslint/eslintrc": "3.3.1",
    "@eslint/js": "9.25.1",
    "@types/node": "20.5.7",
    "@types/react": "18.3.3",
    "@types/react-dom": "18.3.0",
    "@typescript-eslint/eslint-plugin": "8.31.1",
    "@typescript-eslint/parser": "8.31.1",
    "@vitejs/plugin-react": "4.4.1",
    "autoprefixer": "10.4.21",
    "eslint": "9.25.1",
    "eslint-config-prettier": "9.1.0",
    "eslint-plugin-import": "2.31.0",
    "eslint-plugin-jsx-a11y": "6.10.2",
    "eslint-plugin-node": "11.1.0",
    "eslint-plugin-prettier": "5.2.1",
    "eslint-plugin-react": "7.37.5",
    "eslint-plugin-react-hooks": "5.2.0",
    "eslint-plugin-unused-imports": "4.1.4",
    "globals": "16.0.0",
    "postcss": "8.5.3",
    "prettier": "3.5.3",
    "typescript": "5.6.3",
    "vite": "5.2.0",
    "vite-tsconfig-paths": "4.3.2"
  }
}
</file>

<file path="postcss.config.js">

</file>

<file path="README.md">
# Vite & HeroUI Template

This is a template for creating applications using Vite and HeroUI (v2).

[Try it on CodeSandbox](https://githubbox.com/frontio-ai/vite-template)

## Technologies Used

- [Vite](https://vitejs.dev/guide/)
- [HeroUI](https://heroui.com)
- [Tailwind CSS](https://tailwindcss.com)
- [Tailwind Variants](https://tailwind-variants.org)
- [TypeScript](https://www.typescriptlang.org)
- [Framer Motion](https://www.framer.com/motion)

## How to Use

To clone the project, run the following command:

```bash
git clone https://github.com/frontio-ai/vite-template.git
```

### Install dependencies

You can use one of them `npm`, `yarn`, `pnpm`, `bun`, Example using `npm`:

```bash
npm install
```

### Run the development server

```bash
npm run dev
```

### Setup pnpm (optional)

If you are using `pnpm`, you need to add the following code to your `.npmrc` file:

```bash
public-hoist-pattern[]=*@heroui/*
```

After modifying the `.npmrc` file, you need to run `pnpm install` again to ensure that the dependencies are installed correctly.

## License

Licensed under the [MIT license](https://github.com/frontio-ai/vite-template/blob/main/LICENSE).
</file>

<file path="tailwind.config.js">
/** @type {import('tailwindcss').Config} */
⋮----
plugins: [heroui()],
</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "paths": {
      "@/*": ["./src/*"]
    },

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
</file>

<file path="tsconfig.node.json">
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true
  },
  "include": ["vite.config.ts"]
}
</file>

<file path="vercel.json">
{
  "rewrites": [
    { "source": "/(.*)", "destination": "/" }
  ]
}
</file>

<file path="vite.config.ts">
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
⋮----
// https://vitejs.dev/config/
</file>

</files>
