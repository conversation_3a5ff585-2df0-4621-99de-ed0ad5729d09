# Test delle Modifiche al Form di Dipendenza

## Problema Risolto
Il comportamento anomalo dove non sempre si vedevano tutti i dati nel form di modifica nelle sezioni condizione e risultati.

## Modifiche Implementate

### 1. Gestione del Timing di Reset (DependencyRuleFormPage.tsx)
- Aggiunto `setTimeout` di 100ms per assicurarsi che il reset del form avvenga dopo il rendering dei componenti
- Questo risolve la race condition tra il caricamento dei dati e il reset del form

### 2. Gestione Intelligente del Caricamento Iniziale (CondizioneFormItem.tsx e RisultatoFormItem.tsx)
- Aggiunto `isInitialLoadRef` per distinguere tra caricamento iniziale e azioni dell'utente
- I valori non vengono più resettati automaticamente durante il caricamento iniziale
- Reset dei valori solo quando l'utente cambia effettivamente parametri o tipi (non durante il caricamento)

### 3. Miglioramento della Cache delle Query (useDependencyRules.ts)
- Impostato `staleTime: 0` e `cacheTime: 0` per `useGetDependencyRule`
- Questo forza sempre un refetch dei dati freschi quando si accede al form di modifica

## Come Testare

### Test 1: Modifica Regola Esistente
1. Vai alla lista delle regole di dipendenza
2. Clicca su "Modifica" per una regola esistente con condizioni e risultati
3. Verifica che tutti i campi siano popolati correttamente
4. Se alcuni campi sono vuoti, aspetta 1-2 secondi (tempo di caricamento iniziale)
5. Tutti i campi dovrebbero essere visibili e popolati

### Test 2: Navigazione Avanti/Indietro
1. Modifica una regola
2. Torna alla lista senza salvare
3. Clicca nuovamente su "Modifica" per la stessa regola
4. I dati dovrebbero essere caricati correttamente al primo tentativo

### Test 3: Cambio Parametri Durante Modifica
1. Modifica una regola esistente
2. Cambia il parametro di una condizione
3. Verifica che i valori dipendenti si resettino solo dopo il cambio
4. I valori originali non dovrebbero essere persi durante il caricamento iniziale

## Comportamento Atteso
- **Prima**: I campi delle condizioni e risultati a volte apparivano vuoti al primo caricamento
- **Dopo**: Tutti i campi vengono sempre popolati correttamente, anche al primo caricamento

## Note Tecniche
- Il delay di 100ms nel reset è un compromesso per gestire il timing asincrono
- L'uso di `useRef` invece di `useState` per `isInitialLoad` evita re-render inutili
- La disabilitazione della cache assicura dati sempre freschi dal server