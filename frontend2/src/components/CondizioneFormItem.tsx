import React, { useEffect, useState, useRef } from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Button, Select, SelectItem, Input, Card, CardBody } from '@heroui/react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { useGetAllParametersForSelect } from '../hooks/useParametersForSelect';
import { TIPI_CONDIZIONE } from '../types/dependency-rules';
import { get } from '../services/api';

// Tipo per il form
type FormData = {
  nome_regola: string;
  descrizione?: string | null;
  attiva: boolean;
  logica_combinazione_condizioni: 'AND' | 'OR';
  condizioni: {
    id_parametro_condizionante: number;
    tipo_condizione: string;
    id_valore_condizione_predefinita: number | null;
    valore_condizione_libero: string | null;
    ordine_valutazione: number;
  }[];
  risultati: any[];
};

interface CondizioneFormItemProps {
  index: number;
  onRemove: () => void;
}

interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
}

const CondizioneFormItem: React.FC<CondizioneFormItemProps> = ({ index, onRemove }) => {
  const { control, watch, setValue, formState: { errors } } = useFormContext<FormData>();
  const { data: allParameters, isLoading: isLoadingParameters } = useGetAllParametersForSelect();
  
  const [valoriParametro, setValoriParametro] = useState<ValoreParametro[]>([]);
  const [isLoadingValori, setIsLoadingValori] = useState(false);
  const isInitialLoadRef = useRef(true);
  
  const selectedParametroId = watch(`condizioni.${index}.id_parametro_condizionante`);
  const selectedTipoCondizione = watch(`condizioni.${index}.tipo_condizione`);

  // Segna che il caricamento iniziale è completato dopo un breve delay
  useEffect(() => {
    const timer = setTimeout(() => {
      isInitialLoadRef.current = false;
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Carica i valori del parametro selezionato
  useEffect(() => {
    if (selectedParametroId) {
      setIsLoadingValori(true);
      get<ValoreParametro[]>(`/admin/parametri/${selectedParametroId}/valori-for-select`)
        .then(response => {
          setValoriParametro(response.data);
          setIsLoadingValori(false);
        })
        .catch(() => {
          setValoriParametro([]);
          setIsLoadingValori(false);
        });
    } else {
      setValoriParametro([]);
      // Non resettare automaticamente il valore se stiamo caricando dati esistenti
      const currentValue = watch(`condizioni.${index}.id_valore_condizione_predefinita`);
      if (currentValue && selectedParametroId === 0 && !isInitialLoadRef.current) {
        setValue(`condizioni.${index}.id_valore_condizione_predefinita`, null);
      }
    }
  }, [selectedParametroId, setValue, index, watch]);

  // Determina se mostrare il campo valore libero
  const showValoreLibero = selectedTipoCondizione && ['CONTAINS', 'GREATER_THAN', 'LESS_THAN', 'GREATER_THAN_OR_EQUAL', 'LESS_THAN_OR_EQUAL'].includes(selectedTipoCondizione);
  
  // Determina se nascondere completamente i campi valore (per HAS_VALUE)
  const hideValoreFields = selectedTipoCondizione === 'HAS_VALUE';

  const conditionError = errors?.condizioni?.[index] as any;

  return (
    <Card className="mb-4">
      <CardBody>
        <div className="flex justify-between items-start mb-4">
          <h4 className="text-lg font-semibold text-primary">Condizione {index + 1}</h4>
          <Button
            isIconOnly
            color="danger"
            variant="light"
            onPress={onRemove}
            aria-label="Rimuovi condizione"
            size="sm"
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Parametro Condizionante */}
          <Controller
            name={`condizioni.${index}.id_parametro_condizionante`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Parametro"
                placeholder="Seleziona un parametro"
                isLoading={isLoadingParameters}
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [String(field.value)] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  const newParametroId = selectedKey ? Number(selectedKey) : undefined;
                  const oldParametroId = field.value;
                  
                  field.onChange(newParametroId);
                  
                  // Reset dei valori dipendenti solo se è un cambio effettivo dell'utente
                  if (!isInitialLoadRef.current && oldParametroId !== newParametroId && oldParametroId !== 0) {
                    setValue(`condizioni.${index}.id_valore_condizione_predefinita`, null);
                    setValue(`condizioni.${index}.valore_condizione_libero`, null);
                  }
                }}
                isRequired
              >
                {(allParameters || []).map((param) => (
                  <SelectItem key={param.value}>
                    {param.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Tipo Condizione */}
          <Controller
            name={`condizioni.${index}.tipo_condizione`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Tipo di condizione"
                placeholder="Seleziona il tipo"
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [field.value] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0] as string;
                  const oldTipoCondizione = field.value;
                  
                  field.onChange(selectedKey);
                  
                  // Reset dei valori quando cambia il tipo, ma solo se non è il caricamento iniziale
                  if (!isInitialLoadRef.current && oldTipoCondizione !== selectedKey && oldTipoCondizione !== '') {
                    setValue(`condizioni.${index}.id_valore_condizione_predefinita`, null);
                    setValue(`condizioni.${index}.valore_condizione_libero`, null);
                  }
                }}
                isRequired
              >
                {TIPI_CONDIZIONE.map((tipo) => (
                  <SelectItem key={tipo.value}>
                    {tipo.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Valore Predefinito */}
          {!showValoreLibero && !hideValoreFields && (
            <Controller
              name={`condizioni.${index}.id_valore_condizione_predefinita`}
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  label="Valore"
                  placeholder="Seleziona un valore"
                  isLoading={isLoadingValori}
                  isDisabled={!selectedParametroId || isLoadingValori}
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                  selectedKeys={field.value ? [String(field.value)] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0];
                    field.onChange(selectedKey ? Number(selectedKey) : null);
                  }}
                >
                  {valoriParametro.map((valore) => (
                    <SelectItem key={valore.id_valore_parametro}>
                      {valore.testo_visualizzato_ui}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          )}

          {/* Valore Libero */}
          {showValoreLibero && !hideValoreFields && (
            <Controller
              name={`condizioni.${index}.valore_condizione_libero`}
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  value={field.value || ''}
                  label="Valore libero"
                  placeholder="Inserisci il valore"
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                />
              )}
            />
          )}

          {/* Messaggio informativo per HAS_VALUE */}
          {hideValoreFields && (
            <div className="col-span-1 md:col-span-2 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                <strong>Tipo condizione "Ha un valore":</strong> Questa condizione verifica solo che il parametro abbia un valore selezionato, indipendentemente da quale sia il valore specifico.
              </p>
            </div>
          )}

          {/* Ordine Valutazione */}
          <Controller
            name={`condizioni.${index}.ordine_valutazione`}
            control={control}
            render={({ field, fieldState }) => (
              <Input
                {...field}
                value={String(field.value || 0)}
                onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                label="Ordine di valutazione"
                type="number"
                min="0"
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
              />
            )}
          />
        </div>

        {/* Mostra errori generali della condizione */}
        {conditionError && typeof conditionError === 'object' && conditionError.message && (
          <div className="mt-2 text-danger text-sm">
            {conditionError.message}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default CondizioneFormItem;