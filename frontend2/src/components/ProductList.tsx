import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Card,
  CardHeader,
  CardBody,
  Chip,
  Tooltip,
  Image,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@heroui/react';
import { 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  PhotoIcon 
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { useDeleteProduct } from '../hooks/useProducts';
import { IProduct } from '../types/products';

interface ProductListProps {
  products: IProduct[];
  sistemaReady: boolean;
}

const ProductList: React.FC<ProductListProps> = ({ products, sistemaReady }) => {
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedProduct, setSelectedProduct] = React.useState<IProduct | null>(null);
  
  const deleteMutation = useDeleteProduct();

  const handleDelete = (product: IProduct) => {
    setSelectedProduct(product);
    onOpen();
  };

  const confirmDelete = () => {
    if (selectedProduct) {
      deleteMutation.mutate(selectedProduct.id, {
        onSuccess: (data) => {
          toast.success(data.message);
          onClose();
          setSelectedProduct(null);
        },
        onError: (error) => {
          toast.error(`Errore durante l'eliminazione: ${error.message}`);
        },
      });
    }
  };

  const renderPrerequisiti = (regole: IProduct['regole_associate']) => {
    if (regole.length === 0) {
      return <Chip size="sm" color="warning" variant="flat">Nessuna regola</Chip>;
    }

    const regola = regole[0]; // Prendiamo la prima regola per mostrare i prerequisiti
    const prerequisiti = Object.entries(regola.prerequisiti);
    
    return (
      <div className="flex flex-wrap gap-1">
        {prerequisiti.slice(0, 2).map(([nome, prerequisito]) => (
          <Chip key={nome} size="sm" color="primary" variant="flat">
            {prerequisito.nome_valore}
          </Chip>
        ))}
        {prerequisiti.length > 2 && (
          <Chip size="sm" color="default" variant="flat">
            +{prerequisiti.length - 2}
          </Chip>
        )}
      </div>
    );
  };

  const columns = [
    { key: 'foto', label: 'Foto' },
    { key: 'nome_prodotto', label: 'Nome Prodotto' },
    { key: 'prerequisiti', label: 'Prerequisiti' },
    { key: 'regole', label: 'Regole' },
    { key: 'ordine', label: 'Ordine' },
    { key: 'actions', label: 'Azioni' },
  ];

  const renderCell = (product: IProduct, columnKey: React.Key) => {
    switch (columnKey) {
      case 'foto':
        return product.foto ? (
          <Image
            src={`/api/${product.foto}`}
            alt={product.nome_prodotto}
            width={40}
            height={40}
            className="rounded-lg object-cover"
            fallback={<PhotoIcon className="h-8 w-8 text-gray-400" />}
          />
        ) : (
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <PhotoIcon className="h-6 w-6 text-gray-400" />
          </div>
        );

      case 'nome_prodotto':
        return (
          <div>
            <div className="font-semibold">{product.nome_prodotto}</div>
            {product.descrizione && (
              <div className="text-sm text-gray-500 truncate max-w-xs">
                {product.descrizione}
              </div>
            )}
            {product.colore && (
              <div className="flex items-center gap-2 mt-1">
                <div 
                  className="w-4 h-4 rounded border border-gray-300"
                  style={{ backgroundColor: product.colore }}
                />
                <span className="text-xs text-gray-500">{product.colore}</span>
              </div>
            )}
          </div>
        );

      case 'prerequisiti':
        return renderPrerequisiti(product.regole_associate);

      case 'regole':
        return (
          <div className="flex flex-col gap-1">
            <Chip 
              size="sm" 
              color={product.regole_associate.length > 0 ? "success" : "warning"}
              variant="flat"
            >
              {product.regole_associate.length} regole
            </Chip>
            {product.regole_associate.some(r => r.attiva) && (
              <Chip size="sm" color="success" variant="dot">
                Attive
              </Chip>
            )}
          </div>
        );

      case 'ordine':
        return (
          <Chip size="sm" color="default" variant="flat">
            {product.ordine_visualizzazione}
          </Chip>
        );

      case 'actions':
        return (
          <div className="flex items-center gap-2">
            <Tooltip content="Visualizza dettagli">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="primary"
                onPress={() => navigate(`/admin/products/${product.id}`)}
              >
                <EyeIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
            
            <Tooltip content="Modifica prodotto">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="warning"
                onPress={() => navigate(`/admin/products/${product.id}/edit`)}
                isDisabled={!sistemaReady}
              >
                <PencilIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
            
            <Tooltip content="Elimina prodotto" color="danger">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="danger"
                onPress={() => handleDelete(product)}
                isLoading={deleteMutation.isPending && selectedProduct?.id === product.id}
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
          </div>
        );

      default:
        return null;
    }
  };

  if (products.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <PhotoIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Nessun prodotto trovato
          </h3>
          <p className="text-gray-600 mb-4">
            {sistemaReady 
              ? "Inizia creando il tuo primo prodotto."
              : "Configura prima i prerequisiti del sistema."
            }
          </p>
          {sistemaReady && (
            <Button
              color="primary"
              onPress={() => navigate('/admin/products/new')}
            >
              Crea Primo Prodotto
            </Button>
          )}
        </CardBody>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Lista Prodotti</h2>
        </CardHeader>
        <CardBody>
          <Table aria-label="Tabella prodotti">
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn key={column.key}>
                  {column.label}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody items={products}>
              {(product) => (
                <TableRow key={product.id}>
                  {(columnKey) => (
                    <TableCell>
                      {renderCell(product, columnKey)}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Modal di Conferma Eliminazione */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>
            Conferma Eliminazione
          </ModalHeader>
          <ModalBody>
            <p>
              Sei sicuro di voler eliminare il prodotto <strong>"{selectedProduct?.nome_prodotto}"</strong>?
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Questa azione eliminerà anche tutte le regole di dipendenza associate e non può essere annullata.
            </p>
            {selectedProduct?.regole_associate && selectedProduct.regole_associate.length > 0 && (
              <div className="mt-3 p-3 bg-warning-50 border border-warning-200 rounded-lg">
                <p className="text-sm text-warning-800">
                  <strong>Attenzione:</strong> Verranno eliminate {selectedProduct.regole_associate.length} regole associate.
                </p>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              color="default"
              variant="light"
              onPress={onClose}
            >
              Annulla
            </Button>
            <Button
              color="danger"
              onPress={confirmDelete}
              isLoading={deleteMutation.isPending}
            >
              Elimina
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default ProductList;