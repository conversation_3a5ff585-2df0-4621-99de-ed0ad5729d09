import React, { useEffect, useState } from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Button, Select, SelectItem, Input, Card, CardBody } from '@heroui/react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { useGetAllParametersForSelect } from '../hooks/useParametersForSelect';
import { TIPI_EFFETTO } from '../types/dependency-rules';
import { get } from '../services/api';
// Tipo per il form che supporta selezione multipla
type FormData = {
  nome_regola: string;
  descrizione?: string | null;
  attiva: boolean;
  logica_combinazione_condizioni: 'AND' | 'OR';
  condizioni: any[];
  risultati: {
    id_parametro_effetto: number;
    tipo_effetto: string;
    id_valore_effetto_predefinito: number | null;
    id_valori_effetto_predefiniti: number[] | null;
    valore_effetto_libero: string | null;
  }[];
};

interface RisultatoFormItemProps {
  index: number;
  onRemove: () => void;
}

interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
}

const RisultatoFormItem: React.FC<RisultatoFormItemProps> = ({ index, onRemove }) => {
  const { control, watch, setValue, formState: { errors } } = useFormContext<FormData>();
  const { data: allParameters, isLoading: isLoadingParameters } = useGetAllParametersForSelect();
  
  const [valoriParametro, setValoriParametro] = useState<ValoreParametro[]>([]);
  const [isLoadingValori, setIsLoadingValori] = useState(false);
  
  const selectedParametroId = watch(`risultati.${index}.id_parametro_effetto`);
  const selectedTipoEffetto = watch(`risultati.${index}.tipo_effetto`);

  // Carica i valori del parametro selezionato
  useEffect(() => {
    if (selectedParametroId) {
      setIsLoadingValori(true);
      get<ValoreParametro[]>(`/admin/parametri/${selectedParametroId}/valori-for-select`)
        .then(response => {
          setValoriParametro(response.data);
          setIsLoadingValori(false);
        })
        .catch(() => {
          setValoriParametro([]);
          setIsLoadingValori(false);
        });
    } else {
      setValoriParametro([]);
      // Non resettare automaticamente i valori se stiamo caricando dati esistenti
      const currentSingleValue = watch(`risultati.${index}.id_valore_effetto_predefinito`);
      const currentMultipleValues = watch(`risultati.${index}.id_valori_effetto_predefiniti`);
      if ((currentSingleValue || currentMultipleValues) && selectedParametroId === 0) {
        setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
        setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
      }
    }
  }, [selectedParametroId, setValue, index, watch]);

  // Determina se mostrare il campo valore libero
  const showValoreLibero = selectedTipoEffetto && ['SET_VALUE'].includes(selectedTipoEffetto);
  
  // Determina se mostrare il campo valore predefinito (singolo)
  const showValorePredefinito = selectedTipoEffetto && ['SET_VALUE'].includes(selectedTipoEffetto);
  
  // Determina se mostrare il campo valori predefiniti (multiplo)
  const showValoriPredefiniti = selectedTipoEffetto && ['FILTER_VALUES'].includes(selectedTipoEffetto);

  const resultError = errors?.risultati?.[index] as any;

  return (
    <Card className="mb-4">
      <CardBody>
        <div className="flex justify-between items-start mb-4">
          <h4 className="text-lg font-semibold text-secondary">Risultato {index + 1}</h4>
          <Button
            isIconOnly
            color="danger"
            variant="light"
            onPress={onRemove}
            aria-label="Rimuovi risultato"
            size="sm"
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Parametro Effetto */}
          <Controller
            name={`risultati.${index}.id_parametro_effetto`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Parametro"
                placeholder="Seleziona un parametro"
                isLoading={isLoadingParameters}
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [String(field.value)] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  field.onChange(selectedKey ? Number(selectedKey) : undefined);
                  // Reset dei valori dipendenti
                  setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
                  setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
                  setValue(`risultati.${index}.valore_effetto_libero`, null);
                }}
                isRequired
              >
                {(allParameters || []).map((param) => (
                  <SelectItem key={param.value}>
                    {param.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Tipo Effetto */}
          <Controller
            name={`risultati.${index}.tipo_effetto`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Tipo di effetto"
                placeholder="Seleziona il tipo"
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [field.value] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0] as string;
                  field.onChange(selectedKey);
                  // Reset dei valori quando cambia il tipo
                  setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
                  setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
                  setValue(`risultati.${index}.valore_effetto_libero`, null);
                }}
                isRequired
              >
                {TIPI_EFFETTO.map((tipo) => (
                  <SelectItem key={tipo.value}>
                    {tipo.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Valore Predefinito (Singolo per SET_VALUE) */}
          {showValorePredefinito && !showValoreLibero && (
            <Controller
              name={`risultati.${index}.id_valore_effetto_predefinito`}
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  label="Valore"
                  placeholder="Seleziona un valore"
                  isLoading={isLoadingValori}
                  isDisabled={!selectedParametroId || isLoadingValori}
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                  selectedKeys={field.value ? [String(field.value)] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0];
                    field.onChange(selectedKey ? Number(selectedKey) : null);
                  }}
                >
                  {valoriParametro.map((valore) => (
                    <SelectItem key={valore.id_valore_parametro}>
                      {valore.testo_visualizzato_ui}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          )}

          {/* Valore Libero */}
          {showValoreLibero && (
            <Controller
              name={`risultati.${index}.valore_effetto_libero`}
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  value={field.value || ''}
                  label="Valore libero"
                  placeholder="Inserisci il valore"
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                />
              )}
            />
          )}
        </div>

        {/* Valori Predefiniti (Selezione Multipla per FILTER_VALUES) */}
        {showValoriPredefiniti && (
          <div className="mt-4">
            <Controller
              name={`risultati.${index}.id_valori_effetto_predefiniti`}
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  label="Valori da mostrare (selezione multipla)"
                  placeholder="Seleziona i valori da mantenere visibili"
                  isLoading={isLoadingValori}
                  isDisabled={!selectedParametroId || isLoadingValori}
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                  selectedKeys={field.value ? field.value.map(String) : []}
                  onSelectionChange={(keys) => {
                    const selectedKeys = Array.from(keys).map(Number);
                    field.onChange(selectedKeys.length > 0 ? selectedKeys : null);
                  }}
                  selectionMode="multiple"
                  isRequired
                  description="Seleziona uno o piu valori che rimarranno visibili quando si applica questa regola"
                >
                  {valoriParametro.map((valore) => (
                    <SelectItem key={valore.id_valore_parametro}>
                      {valore.testo_visualizzato_ui}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          </div>
        )}

        {/* Mostra errori generali del risultato */}
        {resultError && typeof resultError === 'object' && resultError.message && (
          <div className="mt-2 text-danger text-sm">
            {resultError.message}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default RisultatoFormItem;