import React, { useEffect, useState, useRef } from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Button, Select, SelectItem, Input, Card, CardBody } from '@heroui/react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { useGetAllParametersForSelect } from '../hooks/useParametersForSelect';
import { TIPI_EFFETTO } from '../types/dependency-rules';
import { get } from '../services/api';

// Tipo per il form che supporta selezione multipla
type FormData = {
  nome_regola: string;
  descrizione?: string | null;
  attiva: boolean;
  logica_combinazione_condizioni: 'AND' | 'OR';
  condizioni: any[];
  risultati: {
    id_parametro_effetto: number;
    tipo_effetto: string;
    id_valore_effetto_predefinito: number | null;
    id_valori_effetto_predefiniti: number[] | null;
    valore_effetto_libero: string | null;
  }[];
};

interface RisultatoFormItemProps {
  index: number;
  onRemove: () => void;
}

interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
}

const RisultatoFormItem: React.FC<RisultatoFormItemProps> = ({ index, onRemove }) => {
  const { control, watch, setValue, formState: { errors } } = useFormContext<FormData>();
  const { data: allParameters, isLoading: isLoadingParameters } = useGetAllParametersForSelect();
  
  const [valoriParametro, setValoriParametro] = useState<ValoreParametro[]>([]);
  const [isLoadingValori, setIsLoadingValori] = useState(false);
  const isInitialLoadRef = useRef(true);
  
  const selectedParametroId = watch(`risultati.${index}.id_parametro_effetto`);
  const selectedTipoEffetto = watch(`risultati.${index}.tipo_effetto`);

  // Segna che il caricamento iniziale è completato dopo un breve delay
  useEffect(() => {
    const timer = setTimeout(() => {
      isInitialLoadRef.current = false;
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Carica i valori del parametro selezionato
  useEffect(() => {
    if (selectedParametroId) {
      setIsLoadingValori(true);
      get<ValoreParametro[]>(`/admin/parametri/${selectedParametroId}/valori-for-select`)
        .then(response => {
          setValoriParametro(response.data);
          setIsLoadingValori(false);
        })
        .catch(() => {
          setValoriParametro([]);
          setIsLoadingValori(false);
        });
    } else {
      setValoriParametro([]);
      // Non resettare automaticamente i valori se stiamo caricando dati esistenti
      const currentSingleValue = watch(`risultati.${index}.id_valore_effetto_predefinito`);
      const currentMultipleValues = watch(`risultati.${index}.id_valori_effetto_predefiniti`);
      if ((currentSingleValue || currentMultipleValues) && selectedParametroId === 0 && !isInitialLoadRef.current) {
        setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
        setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
      }
    }
  }, [selectedParametroId, setValue, index, watch]);

  // Determina se mostrare il campo valore libero
  const showValoreLibero = selectedTipoEffetto && ['SET_VALUE'].includes(selectedTipoEffetto);
  
  // Determina se mostrare il campo valore predefinito (singolo)
  const showValorePredefinito = selectedTipoEffetto && ['SET_VALUE'].includes(selectedTipoEffetto);
  
  // Determina se mostrare il campo valori predefiniti (multiplo)
  const showValoriPredefiniti = selectedTipoEffetto && ['FILTER_VALUES'].includes(selectedTipoEffetto);

  const resultError = errors?.risultati?.[index] as any;

  return (
    <Card className="mb-4">
      <CardBody>
        <div className="flex justify-between items-start mb-4">
          <h4 className="text-lg font-semibold text-secondary">Risultato {index + 1}</h4>
          <Button
            isIconOnly
            color="danger"
            variant="light"
            onPress={onRemove}
            aria-label="Rimuovi risultato"
            size="sm"
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Parametro Effetto */}
          <Controller
            name={`risultati.${index}.id_parametro_effetto`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Parametro"
                placeholder="Seleziona un parametro"
                isLoading={isLoadingParameters}
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [String(field.value)] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  const newParametroId = selectedKey ? Number(selectedKey) : undefined;
                  const oldParametroId = field.value;
                  
                  field.onChange(newParametroId);
                  
                  // Reset dei valori dipendenti solo se è un cambio effettivo dell'utente
                  if (!isInitialLoadRef.current && oldParametroId !== newParametroId && oldParametroId !== 0) {
                    setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
                    setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
                    setValue(`risultati.${index}.valore_effetto_libero`, null);
                  }
                }}
                isRequired
              >
                {(allParameters || []).map((param) => (
                  <SelectItem key={param.value}>
                    {param.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Tipo Effetto */}
          <Controller
            name={`risultati.${index}.tipo_effetto`}
            control={control}
            render={({ field, fieldState }) => (
              <Select
                label="Tipo di effetto"
                placeholder="Seleziona il tipo"
                isInvalid={!!fieldState.error}
                errorMessage={fieldState.error?.message}
                selectedKeys={field.value ? [field.value] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0] as string;
                  const oldTipoEffetto = field.value;
                  
                  field.onChange(selectedKey);
                  
                  // Reset dei valori quando cambia il tipo, ma solo se non è il caricamento iniziale
                  if (!isInitialLoadRef.current && oldTipoEffetto !== selectedKey && oldTipoEffetto !== '') {
                    setValue(`risultati.${index}.id_valore_effetto_predefinito`, null);
                    setValue(`risultati.${index}.id_valori_effetto_predefiniti`, null);
                    setValue(`risultati.${index}.valore_effetto_libero`, null);
                  }
                }}
                isRequired
              >
                {TIPI_EFFETTO.map((tipo) => (
                  <SelectItem key={tipo.value}>
                    {tipo.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />

          {/* Valore Predefinito (Singolo per SET_VALUE) */}
          {showValorePredefinito && !showValoreLibero && (
            <Controller
              name={`risultati.${index}.id_valore_effetto_predefinito`}
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  label="Valore"
                  placeholder="Seleziona un valore"
                  isLoading={isLoadingValori}
                  isDisabled={!selectedParametroId || isLoadingValori}
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                  selectedKeys={field.value ? [String(field.value)] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0];
                    field.onChange(selectedKey ? Number(selectedKey) : null);
                  }}
                >
                  {valoriParametro.map((valore) => (
                    <SelectItem key={valore.id_valore_parametro}>
                      {valore.testo_visualizzato_ui}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          )}

          {/* Valore Libero */}
          {showValoreLibero && (
            <Controller
              name={`risultati.${index}.valore_effetto_libero`}
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  value={field.value || ''}
                  label="Valore libero"
                  placeholder="Inserisci il valore"
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                />
              )}
            />
          )}
        </div>

        {/* Valori Predefiniti (Selezione Multipla per FILTER_VALUES) */}
        {showValoriPredefiniti && (
          <div className="mt-4">
            <Controller
              name={`risultati.${index}.id_valori_effetto_predefiniti`}
              control={control}
              render={({ field, fieldState }) => {
                const currentSelectedValues = field.value || [];
                const allValuesSelected = valoriParametro.length > 0 && currentSelectedValues.length === valoriParametro.length;
                
                const handleSelectAll = () => {
                  const allIds = valoriParametro.map(v => v.id_valore_parametro);
                  field.onChange(allIds);
                };
                
                const handleDeselectAll = () => {
                  field.onChange(null);
                };
                
                const handleInvertSelection = () => {
                  const allIds = valoriParametro.map(v => v.id_valore_parametro);
                  const currentIds = field.value || [];
                  const invertedIds = allIds.filter(id => !currentIds.includes(id));
                  field.onChange(invertedIds.length > 0 ? invertedIds : null);
                };
                
                return (
                  <div className="space-y-3">
                    {/* Pulsanti di controllo */}
                    <div className="flex flex-wrap gap-2 items-center">
                      <span className="text-sm font-medium">Valori da mostrare:</span>
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        onPress={handleSelectAll}
                        isDisabled={isLoadingValori || valoriParametro.length === 0 || allValuesSelected}
                      >
                        Seleziona tutto
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        color="default"
                        onPress={handleDeselectAll}
                        isDisabled={isLoadingValori || currentSelectedValues.length === 0}
                      >
                        Deseleziona tutto
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        color="secondary"
                        onPress={handleInvertSelection}
                        isDisabled={isLoadingValori || valoriParametro.length === 0}
                      >
                        Inverti selezione
                      </Button>
                      {valoriParametro.length > 0 && (
                        <span className="text-xs text-gray-500">
                          ({currentSelectedValues.length}/{valoriParametro.length} selezionati)
                        </span>
                      )}
                    </div>
                    
                    {/* Select multiplo */}
                    <Select
                      label="Selezione multipla"
                      placeholder="Seleziona i valori da mantenere visibili"
                      isLoading={isLoadingValori}
                      isDisabled={!selectedParametroId || isLoadingValori}
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                      selectedKeys={field.value ? field.value.map(String) : []}
                      onSelectionChange={(keys) => {
                        const selectedKeys = Array.from(keys).map(Number);
                        field.onChange(selectedKeys.length > 0 ? selectedKeys : null);
                      }}
                      selectionMode="multiple"
                      isRequired
                      description="Seleziona uno o più valori che rimarranno visibili quando si applica questa regola"
                    >
                      {valoriParametro.map((valore) => (
                        <SelectItem key={valore.id_valore_parametro}>
                          {valore.testo_visualizzato_ui}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    {/* Anteprima valori selezionati */}
                    {currentSelectedValues.length > 0 && (
                      <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-700 mb-2">
                          Valori selezionati ({currentSelectedValues.length}):
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {currentSelectedValues.slice(0, 10).map((valoreId) => {
                            const valore = valoriParametro.find(v => v.id_valore_parametro === valoreId);
                            return valore ? (
                              <span
                                key={valoreId}
                                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {valore.testo_visualizzato_ui}
                              </span>
                            ) : null;
                          })}
                          {currentSelectedValues.length > 10 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                              +{currentSelectedValues.length - 10} altri...
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              }}
            />
          </div>
        )}

        {/* Mostra errori generali del risultato */}
        {resultError && typeof resultError === 'object' && resultError.message && (
          <div className="mt-2 text-danger text-sm">
            {resultError.message}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default RisultatoFormItem;