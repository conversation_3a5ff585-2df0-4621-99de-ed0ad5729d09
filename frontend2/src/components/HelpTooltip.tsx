import React from 'react';
import { Tooltip, Button } from '@heroui/react';
import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline';

interface HelpTooltipProps {
  content: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
}

const HelpTooltip: React.FC<HelpTooltipProps> = ({ 
  content, 
  placement = 'top',
  size = 'sm' 
}) => {
  return (
    <Tooltip 
      content={content} 
      placement={placement}
      classNames={{
        content: "max-w-xs text-sm bg-gray-900 text-white p-3 rounded-lg shadow-lg",
      }}
    >
      <Button
        isIconOnly
        variant="light"
        size={size}
        className="text-gray-400 hover:text-gray-600 min-w-unit-6 w-6 h-6"
      >
        <QuestionMarkCircleIcon className="h-4 w-4" />
      </Button>
    </Tooltip>
  );
};

export default HelpTooltip;