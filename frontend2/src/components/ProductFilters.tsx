import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Card,
  CardHeader,
  CardBody,
  Input,
  Select,
  SelectItem,
  Button,
  Switch,
  Chip,
} from '@heroui/react';
import { MagnifyingGlassIcon, XMarkIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { ProductFiltersSchema, ProductFiltersData } from '../schemas/productSchemas';
import { useGetProductPrerequisites } from '../hooks/useProducts';

interface ProductFiltersProps {
  onFiltersChange: (filters: ProductFiltersData) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({ 
  onFiltersChange, 
  isCollapsed = false,
  onToggleCollapse 
}) => {
  const { data: prerequisitesData } = useGetProductPrerequisites();
  
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isDirty }
  } = useForm<ProductFiltersData>({
    resolver: zodResolver(ProductFiltersSchema),
    defaultValues: {
      search: '',
      tecnica: undefined,
      tipologia_impianto: undefined,
      connessione: undefined,
      attiva: undefined,
    },
  });

  const watchedValues = watch();

  // Applica i filtri quando cambiano i valori
  // Usiamo useRef per evitare loop infiniti
  const prevFiltersRef = React.useRef<ProductFiltersData>({});
  
  React.useEffect(() => {
    // Confronta i valori precedenti con quelli attuali
    const prevFilters = prevFiltersRef.current;
    const filtersChanged = 
      prevFilters.search !== watchedValues.search ||
      prevFilters.tecnica !== watchedValues.tecnica ||
      prevFilters.tipologia_impianto !== watchedValues.tipologia_impianto ||
      prevFilters.connessione !== watchedValues.connessione ||
      prevFilters.attiva !== watchedValues.attiva;
    
    // Aggiorna solo se i filtri sono effettivamente cambiati
    if (filtersChanged) {
      prevFiltersRef.current = { ...watchedValues };
      onFiltersChange(watchedValues);
    }
  }, [watchedValues, onFiltersChange]);

  const handleReset = () => {
    reset();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (watchedValues.search) count++;
    if (watchedValues.tecnica) count++;
    if (watchedValues.tipologia_impianto) count++;
    if (watchedValues.connessione) count++;
    if (watchedValues.attiva !== undefined) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <FunnelIcon className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Filtri</h3>
            {activeFiltersCount > 0 && (
              <Chip size="sm" color="primary" variant="flat">
                {activeFiltersCount}
              </Chip>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {isDirty && (
              <Button
                size="sm"
                variant="light"
                color="default"
                onPress={handleReset}
                startContent={<XMarkIcon className="h-4 w-4" />}
              >
                Reset
              </Button>
            )}
            {onToggleCollapse && (
              <Button
                size="sm"
                variant="light"
                onPress={onToggleCollapse}
              >
                {isCollapsed ? 'Espandi' : 'Comprimi'}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardBody className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Ricerca */}
            <Controller
              name="search"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="Cerca prodotti"
                  placeholder="Nome prodotto..."
                  startContent={<MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />}
                  isClearable
                  onClear={() => field.onChange('')}
                />
              )}
            />

            {/* Filtro Tecnica */}
            {prerequisitesData?.prerequisiti.tecnica && (
              (() => {
                const tecnicaData = prerequisitesData.prerequisiti.tecnica;
                return (
                  <Controller
                    name="tecnica"
                    control={control}
                    render={({ field }) => (
                      <Select
                        label="Tecnica"
                        placeholder="Tutte le tecniche"
                        selectedKeys={field.value ? [`${field.value}`] : []}
                        onSelectionChange={(keys) => {
                          const keysArray = Array.from(keys);
                          if (keysArray.length === 0) {
                            field.onChange(undefined);
                          } else {
                            const selectedKey = keysArray[0];
                            const numericValue = parseInt(selectedKey as string, 10);
                            field.onChange(!isNaN(numericValue) ? numericValue : undefined);
                          }
                        }}
                      >
                        {tecnicaData.valori_disponibili.map((tecnica) => (
                          <SelectItem key={`${tecnica.id}`}>
                            {tecnica.nome}
                          </SelectItem>
                        ))}
                      </Select>
                    )}
                  />
                );
              })()
            )}

            {/* Filtro Tipologia Impianto */}
            {prerequisitesData?.prerequisiti.tipologia_impianto && (
              (() => {
                const tipologiaData = prerequisitesData.prerequisiti.tipologia_impianto;
                return (
                  <Controller
                    name="tipologia_impianto"
                    control={control}
                    render={({ field }) => (
                      <Select
                        label="Tipologia Impianto"
                        placeholder="Tutte le tipologie"
                        selectedKeys={field.value ? [`${field.value}`] : []}
                        onSelectionChange={(keys) => {
                          const keysArray = Array.from(keys);
                          if (keysArray.length === 0) {
                            field.onChange(undefined);
                          } else {
                            const selectedKey = keysArray[0];
                            const numericValue = parseInt(selectedKey as string, 10);
                            field.onChange(!isNaN(numericValue) ? numericValue : undefined);
                          }
                        }}
                      >
                        {tipologiaData.valori_disponibili.map((tipologia) => (
                          <SelectItem key={`${tipologia.id}`}>
                            {tipologia.nome}
                          </SelectItem>
                        ))}
                      </Select>
                    )}
                  />
                );
              })()
            )}

            {/* Filtro Connessione */}
            {prerequisitesData?.prerequisiti.connessione && (
              (() => {
                const connessioneData = prerequisitesData.prerequisiti.connessione;
                return (
                  <Controller
                    name="connessione"
                    control={control}
                    render={({ field }) => (
                      <Select
                        label="Connessione"
                        placeholder="Tutte le connessioni"
                        selectedKeys={field.value ? [`${field.value}`] : []}
                        onSelectionChange={(keys) => {
                          const keysArray = Array.from(keys);
                          if (keysArray.length === 0) {
                            field.onChange(undefined);
                          } else {
                            const selectedKey = keysArray[0];
                            const numericValue = parseInt(selectedKey as string, 10);
                            field.onChange(!isNaN(numericValue) ? numericValue : undefined);
                          }
                        }}
                      >
                        {connessioneData.valori_disponibili.map((connessione) => (
                          <SelectItem key={`${connessione.id}`}>
                            {connessione.nome}
                          </SelectItem>
                        ))}
                      </Select>
                    )}
                  />
                );
              })()
            )}

            {/* Filtro Regole Attive */}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium text-gray-700">
                Stato Regole
              </label>
              <Controller
                name="attiva"
                control={control}
                render={({ field }) => (
                  <div className="flex items-center gap-4">
                    <Switch
                      isSelected={field.value === true}
                      onValueChange={(checked) => {
                        if (field.value === true && !checked) {
                          field.onChange(undefined);
                        } else if (field.value === false && checked) {
                          field.onChange(true);
                        } else {
                          field.onChange(checked ? true : false);
                        }
                      }}
                    >
                      Solo con regole attive
                    </Switch>
                  </div>
                )}
              />
            </div>
          </div>

          {/* Filtri Attivi */}
          {activeFiltersCount > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-gray-600">Filtri attivi:</span>
                
                {watchedValues.search && (
                  <Chip
                    size="sm"
                    color="primary"
                    variant="flat"
                    onClose={() => reset({ ...watchedValues, search: '' })}
                  >
                    Ricerca: "{watchedValues.search}"
                  </Chip>
                )}
                
                {watchedValues.tecnica && prerequisitesData?.prerequisiti.tecnica && (
                  (() => {
                    const tecnicaData = prerequisitesData.prerequisiti.tecnica;
                    return (
                      <Chip
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClose={() => reset({ ...watchedValues, tecnica: undefined })}
                      >
                        Tecnica: {tecnicaData.valori_disponibili.find(t => t.id === watchedValues.tecnica)?.nome}
                      </Chip>
                    );
                  })()
                )}
                
                {watchedValues.tipologia_impianto && prerequisitesData?.prerequisiti.tipologia_impianto && (
                  (() => {
                    const tipologiaData = prerequisitesData.prerequisiti.tipologia_impianto;
                    return (
                      <Chip
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClose={() => reset({ ...watchedValues, tipologia_impianto: undefined })}
                      >
                        Tipologia: {tipologiaData.valori_disponibili.find(t => t.id === watchedValues.tipologia_impianto)?.nome}
                      </Chip>
                    );
                  })()
                )}
                
                {watchedValues.connessione && prerequisitesData?.prerequisiti.connessione && (
                  (() => {
                    const connessioneData = prerequisitesData.prerequisiti.connessione;
                    return (
                      <Chip
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClose={() => reset({ ...watchedValues, connessione: undefined })}
                      >
                        Connessione: {connessioneData.valori_disponibili.find(c => c.id === watchedValues.connessione)?.nome}
                      </Chip>
                    );
                  })()
                )}
                
                {watchedValues.attiva !== undefined && (
                  <Chip
                    size="sm"
                    color="primary"
                    variant="flat"
                    onClose={() => reset({ ...watchedValues, attiva: undefined })}
                  >
                    {watchedValues.attiva ? 'Con regole attive' : 'Con regole inattive'}
                  </Chip>
                )}
              </div>
            </div>
          )}
        </CardBody>
      )}
    </Card>
  );
};

export default ProductFilters;