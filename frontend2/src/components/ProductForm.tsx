import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Card,
  CardHeader,
  CardBody,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Spinner,
  Chip,
  Image,
} from '@heroui/react';
import { PhotoIcon, EyeIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { ProductFormSchema, ProductFormData } from '../schemas/productSchemas';
import { useGetProductPrerequisites, useCreateProduct, useUpdateProduct } from '../hooks/useProducts';
import { IProduct } from '../types/products';

interface ProductFormProps {
  product?: IProduct;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSuccess, onCancel }) => {
  const isEditing = !!product;
  
  const { data: prerequisitesData, isLoading: prerequisitesLoading } = useGetProductPrerequisites();
  const createMutation = useCreateProduct();
  const updateMutation = useUpdateProduct();
  
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<ProductFormData>({
    resolver: async (data, context, options) => {
      // Log dei dati prima della validazione Zod
      console.log('Data before Zod validation:', data);
      
      // Esegui la validazione standard con Zod
      const result = await zodResolver(ProductFormSchema)(data, context, options);
      
      // Log dei risultati della validazione
      console.log('Zod validation result:', result);
      
      return result;
    },
    defaultValues: {
      nome_prodotto: '',
      descrizione: '',
      colore: '',
      ordine_visualizzazione: 0,
      prerequisiti: {
        id_lavorazione: Number(0),
        id_tecniche: [], // Array vuoto per selezione multipla
        id_tipologia_impianto: Number(0),
        id_connessione: Number(0),
      },
    },
  });

  // Popola il form se stiamo modificando un prodotto esistente
  useEffect(() => {
    if (product && prerequisitesData) {
      // Estrai i prerequisiti dalla prima regola associata
      const regola = product.regole_associate[0];
      let prerequisiti = {
        id_lavorazione: Number(0),
        id_tecniche: [] as number[], // Array per multiple tecniche
        id_tipologia_impianto: Number(0),
        id_connessione: Number(0),
      };

      if (product.regole_associate && product.regole_associate.length > 0) {
        // Raccogliamo i prerequisiti da tutte le regole associate
        const tecniche_set = new Set<number>();
        let lavorazione_id: number | null = null;
        let tipologia_impianto_id: number | null = null;
        let connessione_id: number | null = null;
        
        product.regole_associate.forEach(regola => {
          Object.entries(regola.prerequisiti).forEach(([nome, prerequisito]) => {
            switch (nome) {
              case 'Lavorazioni':
                lavorazione_id = Number(prerequisito.id_valore);
                break;
              case 'Tecnica':
                tecniche_set.add(Number(prerequisito.id_valore));
                break;
              case 'Tipologia Impianto':
                tipologia_impianto_id = Number(prerequisito.id_valore);
                break;
              case 'Connessione':
                connessione_id = Number(prerequisito.id_valore);
                break;
            }
          });
        });
        
        // Assegna i valori raccolti
        if (lavorazione_id) prerequisiti.id_lavorazione = lavorazione_id;
        if (tipologia_impianto_id) prerequisiti.id_tipologia_impianto = tipologia_impianto_id;
        if (connessione_id) prerequisiti.id_connessione = connessione_id;
        prerequisiti.id_tecniche = Array.from(tecniche_set);
        
        console.log('Setting prerequisiti from multiple rules:', prerequisiti);
      }

      reset({
        nome_prodotto: product.nome_prodotto,
        descrizione: product.descrizione || '',
        colore: product.colore || '',
        ordine_visualizzazione: product.ordine_visualizzazione,
        prerequisiti,
      });
    }
  }, [product, prerequisitesData, reset]);

  // Imposta valori predefiniti quando i prerequisiti sono caricati
  useEffect(() => {
    if (prerequisitesData && !isEditing) {
      // Imposta la lavorazione predefinita se disponibile
      if (prerequisitesData.prerequisiti.lavorazione) {
        const lavorazioneId = Number(prerequisitesData.prerequisiti.lavorazione.valore_predefinito.id);
        console.log('Setting default lavorazione:', lavorazioneId, typeof lavorazioneId);
        setValue('prerequisiti.id_lavorazione', lavorazioneId);
      }
    }
  }, [prerequisitesData, isEditing, setValue]);

  const watchedValues = watch();

  const onSubmit = async (data: ProductFormData) => {
    try {
      // Debug: Log dei dati prima della sottomissione
      console.log('Form data before submission:', data);
      console.log('Prerequisiti types:', {
        id_lavorazione: typeof data.prerequisiti.id_lavorazione,
        id_tecnica: typeof data.prerequisiti.id_tecnica,
        id_tipologia_impianto: typeof data.prerequisiti.id_tipologia_impianto,
        id_connessione: typeof data.prerequisiti.id_connessione
      });
      
      // Prepara FormData per supportare upload file
      const formData = new FormData();
      
      formData.append('nome_prodotto', data.nome_prodotto);
      if (data.descrizione) formData.append('descrizione', data.descrizione);
      if (data.colore) formData.append('colore', data.colore);
      if (data.ordine_visualizzazione !== undefined) {
        formData.append('ordine_visualizzazione', data.ordine_visualizzazione.toString());
      }
      
      // Aggiungi prerequisiti come JSON
      formData.append('prerequisiti', JSON.stringify(data.prerequisiti));
      
      // Aggiungi file se presente
      if (data.foto && data.foto.length > 0) {
        formData.append('foto', data.foto[0]);
      }

      if (isEditing && product) {
        const response = await updateMutation.mutateAsync({
          id: product.id,
          payload: formData,
        });
        const tecniche_count = (response as any)?.tecniche_count || 1;
        toast.success(`Prodotto aggiornato con successo (${tecniche_count} regole create)`);
      } else {
        const response = await createMutation.mutateAsync(formData);
        const tecniche_count = (response as any)?.tecniche_count || 1;
        toast.success(`Prodotto creato con successo (${tecniche_count} regole create)`);
      }
      
      onSuccess?.();
    } catch (error) {
      toast.error(`Errore durante ${isEditing ? 'l\'aggiornamento' : 'la creazione'} del prodotto`);
    }
  };

  // Genera preview della regola che verrà creata
  const generateRulePreview = () => {
    if (!prerequisitesData || !watchedValues.prerequisiti) return null;

    const { prerequisiti } = prerequisitesData;
    const selected = watchedValues.prerequisiti;
    
    const conditions = [];
    
    // Lavorazione
    if (prerequisiti.lavorazione && selected.id_lavorazione) {
      conditions.push(`Lavorazione = "${prerequisiti.lavorazione.valore_predefinito.nome}"`);
    }
    
    // Tecnica (multiple)
    if (prerequisiti.tecnica && selected.id_tecniche && selected.id_tecniche.length > 0) {
      const tecniche = prerequisiti.tecnica.valori_disponibili
        .filter(v => selected.id_tecniche.includes(v.id))
        .map(v => v.nome);
      if (tecniche.length > 0) {
        if (tecniche.length === 1) {
          conditions.push(`Tecnica = "${tecniche[0]}"`);
        } else {
          conditions.push(`Tecnica IN [${tecniche.map(t => `"${t}"`).join(', ')}]`);
        }
      }
    }
    
    // Tipologia Impianto
    if (prerequisiti.tipologia_impianto && selected.id_tipologia_impianto) {
      const tipologia = prerequisiti.tipologia_impianto.valori_disponibili.find(v => v.id === selected.id_tipologia_impianto);
      if (tipologia) conditions.push(`Tipologia Impianto = "${tipologia.nome}"`);
    }
    
    // Connessione
    if (prerequisiti.connessione && selected.id_connessione) {
      const connessione = prerequisiti.connessione.valori_disponibili.find(v => v.id === selected.id_connessione);
      if (connessione) conditions.push(`Connessione = "${connessione.nome}"`);
    }

    return conditions;
  };

  if (prerequisitesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" label="Caricamento prerequisiti..." />
      </div>
    );
  }

  if (!prerequisitesData?.sistema_pronto) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <h3 className="text-lg font-semibold text-warning mb-2">
            Sistema Non Configurato
          </h3>
          <p className="text-gray-600 mb-4">
            Configura prima tutti i prerequisiti del sistema per poter creare prodotti.
          </p>
          {prerequisitesData?.prerequisiti_mancanti && (
            <div className="text-left max-w-md mx-auto">
              <p className="font-medium mb-2">Prerequisiti mancanti:</p>
              <ul className="list-disc list-inside text-sm text-gray-600">
                {prerequisitesData.prerequisiti_mancanti.map((prerequisito, index) => (
                  <li key={index}>{prerequisito}</li>
                ))}
              </ul>
            </div>
          )}
        </CardBody>
      </Card>
    );
  }

  const rulePreview = generateRulePreview();

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Informazioni Prodotto */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">
            {isEditing ? 'Modifica Prodotto' : 'Nuovo Prodotto'}
          </h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Controller
              name="nome_prodotto"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="Nome Prodotto"
                  placeholder="es. Diametro 4.5mm"
                  isRequired
                  isInvalid={!!errors.nome_prodotto}
                  errorMessage={errors.nome_prodotto?.message}
                />
              )}
            />

            <Controller
              name="colore"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="Colore"
                  placeholder="es. #FF5733 o rosso"
                  isInvalid={!!errors.colore}
                  errorMessage={errors.colore?.message}
                />
              )}
            />
          </div>

          <Controller
            name="descrizione"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                label="Descrizione"
                placeholder="Descrizione dettagliata del prodotto..."
                isInvalid={!!errors.descrizione}
                errorMessage={errors.descrizione?.message}
              />
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Controller
              name="ordine_visualizzazione"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  label="Ordine Visualizzazione"
                  placeholder="0"
                  value={field.value?.toString() || ''}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  isInvalid={!!errors.ordine_visualizzazione}
                  errorMessage={errors.ordine_visualizzazione?.message}
                />
              )}
            />

            <Controller
              name="foto"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Foto Prodotto
                  </label>
                  <input
                    {...field}
                    type="file"
                    accept="image/*"
                    onChange={(e) => onChange(e.target.files)}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                  />
                  {errors.foto && (
                    <p className="text-sm text-danger mt-1">{errors.foto.message}</p>
                  )}
                  {product?.foto && (
                    <div className="mt-2">
                      <Image
                        src={`/api/${product.foto}`}
                        alt="Foto attuale"
                        width={100}
                        height={100}
                        className="rounded-lg object-cover"
                        fallback={<PhotoIcon className="h-20 w-20 text-gray-400" />}
                      />
                    </div>
                  )}
                </div>
              )}
            />
          </div>
        </CardBody>
      </Card>

      {/* Prerequisiti */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Prerequisiti</h3>
          <p className="text-sm text-gray-600">
            Configura quando questo prodotto deve essere mostrato
          </p>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Lavorazione */}
            {prerequisitesData.prerequisiti.lavorazione && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lavorazione
                </label>
                <Chip color="primary" variant="flat">
                  {prerequisitesData.prerequisiti.lavorazione.valore_predefinito.nome}
                </Chip>
                <p className="text-xs text-gray-500 mt-1">
                  Valore fisso per questo sistema
                </p>
              </div>
            )}

            {/* Tecnica */}
            {prerequisitesData.prerequisiti.tecnica && (
              <Controller
                name="prerequisiti.id_tecniche"
                control={control}
                render={({ field }) => (
                  <Select
                    label="Tecnica"
                    placeholder="Seleziona una o più tecniche"
                    isRequired
                    selectionMode="multiple"
                    selectedKeys={field.value ? field.value.map(id => `${id}`) : []}
                    onSelectionChange={(keys) => {
                      const keysArray = Array.from(keys);
                      const numericValues = keysArray
                        .map(key => parseInt(key.toString(), 10))
                        .filter(val => !isNaN(val));
                      field.onChange(numericValues);
                    }}
                    isInvalid={!!errors.prerequisiti?.id_tecniche}
                    errorMessage={errors.prerequisiti?.id_tecniche?.message}
                  >
                    {prerequisitesData.prerequisiti.tecnica.valori_disponibili.map((tecnica) => (
                      <SelectItem key={`${tecnica.id}`}>
                        {tecnica.nome}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            )}

            {/* Tipologia Impianto */}
            {prerequisitesData.prerequisiti.tipologia_impianto && (
              <Controller
                name="prerequisiti.id_tipologia_impianto"
                control={control}
                render={({ field }) => (
                  <Select
                    label="Tipologia Impianto"
                    placeholder="Seleziona una tipologia"
                    isRequired
                    selectedKeys={field.value && field.value > 0 ? [`${field.value}`] : []}
                    onSelectionChange={(keys) => {
                      const keysArray = Array.from(keys);
                      if (keysArray.length === 0) {
                        field.onChange(0);
                      } else {
                        const selectedKey = keysArray[0];
                        const numericValue = parseInt(selectedKey.toString(), 10);
                        field.onChange(!isNaN(numericValue) ? numericValue : 0);
                      }
                    }}
                    isInvalid={!!errors.prerequisiti?.id_tipologia_impianto}
                    errorMessage={errors.prerequisiti?.id_tipologia_impianto?.message}
                  >
                    {prerequisitesData.prerequisiti.tipologia_impianto.valori_disponibili.map((tipologia) => (
                      <SelectItem key={`${tipologia.id}`}>
                        {tipologia.nome}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            )}

            {/* Connessione */}
            {prerequisitesData.prerequisiti.connessione && (
              <Controller
                name="prerequisiti.id_connessione"
                control={control}
                render={({ field }) => (
                  <Select
                    label="Connessione"
                    placeholder="Seleziona una connessione"
                    isRequired
                    selectedKeys={field.value && field.value > 0 ? [`${field.value}`] : []}
                    onSelectionChange={(keys) => {
                      const keysArray = Array.from(keys);
                      if (keysArray.length === 0) {
                        field.onChange(0);
                      } else {
                        const selectedKey = keysArray[0];
                        const numericValue = parseInt(selectedKey.toString(), 10);
                        field.onChange(!isNaN(numericValue) ? numericValue : 0);
                      }
                    }}
                    isInvalid={!!errors.prerequisiti?.id_connessione}
                    errorMessage={errors.prerequisiti?.id_connessione?.message}
                  >
                    {prerequisitesData.prerequisiti.connessione.valori_disponibili.map((connessione) => (
                      <SelectItem key={`${connessione.id}`}>
                        {connessione.nome}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            )}
          </div>
        </CardBody>
      </Card>

      {/* Preview Regola */}
      {rulePreview && rulePreview.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <EyeIcon className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Preview Regola</h3>
            </div>
          </CardHeader>
          <CardBody>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">
                {isEditing ? 'La regola verrà aggiornata con:' : 'Verrà creata una regola con:'}
              </p>
              <div className="space-y-2">
                <div className="font-mono text-sm">
                  <span className="text-blue-600">SE</span>{' '}
                  {rulePreview.join(' E ')}
                </div>
                <div className="font-mono text-sm">
                  <span className="text-green-600">ALLORA</span>{' '}
                  Filtra valori Diametro per mostrare solo "{watchedValues.nome_prodotto || 'Nuovo Prodotto'}"
                </div>
                {watchedValues.prerequisiti?.id_tecniche && watchedValues.prerequisiti.id_tecniche.length > 1 && (
                  <div className="text-xs text-blue-600 mt-2">
                    ℹ️ Verranno create {watchedValues.prerequisiti.id_tecniche.length} regole separate (una per ogni tecnica)
                  </div>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Azioni */}
      <div className="flex justify-end gap-4">
        {onCancel && (
          <Button
            color="default"
            variant="light"
            onPress={onCancel}
            isDisabled={isSubmitting}
          >
            Annulla
          </Button>
        )}
        <Button
          type="submit"
          color="primary"
          isLoading={isSubmitting}
          isDisabled={!prerequisitesData?.sistema_pronto}
        >
          {isEditing ? 'Aggiorna Prodotto' : 'Crea Prodotto'}
        </Button>
      </div>
    </form>
  );
};

export default ProductForm;