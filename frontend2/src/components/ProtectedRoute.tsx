import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext'; // Importa useAuth

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth(); // Ottiene lo stato di autenticazione dal contesto

  if (loading) {
    return <div>Caricamento autenticazione...</div>; // O uno spinner/componente di caricamento
  }

  if (!isAuthenticated) {
    // Reindirizza l'utente alla pagina di login se non autenticato
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;