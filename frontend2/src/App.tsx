import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute'; // Importa il componente ProtectedRoute
import LoginPage from './pages/LoginPage'; // Importa la pagina di Login
import AuthLayout from './layouts/AuthLayout'; // Importa il layout di autenticazione
import AdminLayout from './layouts/AdminLayout'; // Importa il layout di amministrazione
import { AuthProvider } from './contexts/AuthContext'; // Importa AuthProvider
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'; // Importa TanStack Query
import './App.css';

import ParametersListPage from './pages/ParametersListPage';
import ParameterFormPage from './pages/ParameterFormPage';
import ValueParametersListPage from './pages/ValueParametersListPage'; // Importa la pagina della lista dei valori dei parametri
import ValueParameterFormPage from './pages/ValueParameterFormPage'; // Importa la pagina del form dei valori dei parametri
import UsersListPage from './pages/UsersListPage'; // Importa la pagina della lista degli utenti
import UserFormPage from './pages/UserFormPage'; // Importa la pagina del form degli utenti

import DependencyRulesListPage from './pages/DependencyRulesListPage'; // Importa la pagina della lista delle regole di dipendenza
import DependencyRuleFormPage from './pages/DependencyRuleFormPage'; // Importa la pagina del form delle regole di dipendenza

import ProductManagementPage from './pages/ProductManagementPage'; // Importa la pagina di gestione prodotti
import ProductFormPage from './pages/ProductFormPage'; // Importa la pagina del form prodotti
import ProductDetailPage from './pages/ProductDetailPage'; // Importa la pagina dettaglio prodotto

import ClientiListPage from './pages/ClientiListPage'; // Importa la pagina della lista clienti
import ClienteFormPage from './pages/ClienteFormPage'; // Importa la pagina del form clienti
import ClientiTestPage from './pages/ClientiTestPage'; // Importa la pagina di test clienti
import ClienteResetPasswordPage from './pages/ClienteResetPasswordPage'; // Importa la pagina reset password


// Componenti di placeholder per le schermate future
const AdminDashboard = () => <div>Dashboard di Amministrazione</div>;
const NotFound = () => <div>404 - Pagina Non Trovata</div>;

// Crea un'istanza di QueryClient con configurazioni di default
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 1000 * 60 * 5, // 5 minuti
    },
  },
});

// Componente App principale con la configurazione del routing e QueryClientProvider
function App() {
  return (
    <Router>
      <QueryClientProvider client={queryClient}>
        <AuthProvider> {/* Avvolge l'intera applicazione con AuthProvider */}
          <Routes>
            <Route path="/" element={<AuthLayout />}>
              <Route index element={<LoginPage />} /> {/* Rotta predefinita per la pagina di login */}
              <Route path="login" element={<LoginPage />} /> {/* Rotta esplicita per la pagina di login */}
            </Route>
            {/* Proteggi la rotta /admin/* con ProtectedRoute, non è più necessario passare isAuthenticated */}
            <Route
              path="/admin/*"
              element={
                <ProtectedRoute>
                  <AdminLayout> {/* Avvolge le rotte di amministrazione con AdminLayout */}
                    <Routes>
                      <Route path="parameters" element={<ParametersListPage />} />
                      <Route path="parameters/new" element={<ParameterFormPage />} />
                      <Route path="parameters/:id" element={<ParameterFormPage />} />
                      <Route path="parameters/:parameterId/values" element={<ValueParametersListPage />} />
                      <Route path="parameters/:parameterId/values/new" element={<ValueParameterFormPage />} />
                      <Route path="parameters/:parameterId/values/:valueId" element={<ValueParameterFormPage />} />
                      <Route path="users" element={<UsersListPage />} />
                      <Route path="users/new" element={<UserFormPage />} />
                      <Route path="users/:id" element={<UserFormPage />} />
                      {/* Nuove rotte per le regole di dipendenza */}
                      <Route path="dependency-rules" element={<DependencyRulesListPage />} />
                      <Route path="dependency-rules/new" element={<DependencyRuleFormPage />} />
                      <Route path="dependency-rules/:id" element={<DependencyRuleFormPage />} />
                      {/* Nuove rotte per la gestione prodotti */}
                      <Route path="products" element={<ProductManagementPage />} />
                      <Route path="products/new" element={<ProductFormPage />} />
                      <Route path="products/:id" element={<ProductDetailPage />} />
                      <Route path="products/:id/edit" element={<ProductFormPage />} />
                      {/* Nuove rotte per la gestione clienti */}
                      <Route path="clienti" element={<ClientiListPage />} />
                      <Route path="clienti/test" element={<ClientiTestPage />} />
                      <Route path="clienti/new" element={<ClienteFormPage />} />
                      <Route path="clienti/:id" element={<ClienteFormPage />} />
                      <Route path="clienti/:id/edit" element={<ClienteFormPage />} />
                      <Route path="clienti/:id/reset-password" element={<ClienteResetPasswordPage />} />
                      <Route path="*" element={<AdminDashboard />} />
                    </Routes>
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<NotFound />} /> {/* Catch-all per le rotte non definite */}
          </Routes>
        </AuthProvider>
      </QueryClientProvider>
    </Router>
  );
}

export default App;
