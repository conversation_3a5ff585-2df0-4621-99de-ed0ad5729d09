import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, CardBody, Button, Spinner } from '@heroui/react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useGetProduct } from '../hooks/useProducts';
import ProductForm from '../components/ProductForm';
import Breadcrumb from '../components/Breadcrumb';
import HelpTooltip from '../components/HelpTooltip';

const ProductFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditing = !!id;

  const { 
    data: productData, 
    isLoading, 
    error 
  } = useGetProduct(id ? parseInt(id) : 0);

  const handleSuccess = () => {
    navigate('/admin/products');
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  if (isLoading && isEditing) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" label="Caricamento prodotto..." />
      </div>
    );
  }

  if (error && isEditing) {
    return (
      <Card className="max-w-md mx-auto">
        <CardBody className="text-center">
          <h3 className="text-lg font-semibold text-danger mb-2">
            Errore nel caricamento
          </h3>
          <p className="text-gray-600 mb-4">
            {(error as Error).message || 'Impossibile caricare il prodotto'}
          </p>
          <Button 
            color="primary" 
            onPress={() => navigate('/admin/products')}
          >
            Torna alla Lista
          </Button>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb
        items={[
          { label: 'Prodotti', href: '/admin/products' },
          { label: isEditing ? 'Modifica Prodotto' : 'Nuovo Prodotto', current: true },
        ]}
      />
      
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate('/admin/products')}
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Button>
        
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Modifica Prodotto' : 'Nuovo Prodotto'}
            </h1>
            <HelpTooltip 
              content={isEditing 
                ? "Modifica le proprietà del prodotto e i prerequisiti. Le regole verranno aggiornate automaticamente."
                : "Crea un nuovo prodotto e le regole verranno generate automaticamente in base ai prerequisiti."
              } 
            />
          </div>
          <p className="text-gray-600 mt-1">
            {isEditing 
              ? `Modifica il prodotto "${productData?.product.nome_prodotto}"`
              : 'Crea un nuovo prodotto con regole di dipendenza automatiche'
            }
          </p>
        </div>
      </div>

      {/* Form */}
      <ProductForm
        product={productData?.product}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default ProductFormPage;