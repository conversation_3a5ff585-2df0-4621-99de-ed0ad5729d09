import React, { useEffect, useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  Input,
  Button,
  Select,
  SelectItem,
  Switch,
  Textarea,
  Card,
  CardHeader,
  CardBody,
  Spinner,
  Image,
} from '@heroui/react';
import { ArrowLeftIcon, PhotoIcon, TrashIcon } from '@heroicons/react/24/outline';


import { IParameterFormPayload, IParameter } from '../types/parameters';
import { get, post, put } from '../services/api';


// Schema di validazione per i dati dell'utente - versione semplificata per il form
const parameterFormSchema = z.object({
  nome_parametro: z.string().min(1, 'Il nome del parametro è obbligatorio'),
  descrizione: z.string().optional(),
  tipo_controllo_ui: z.string().min(1, 'Il tipo di controllo UI è obbligatorio'),
  is_root: z.boolean(),
  attivo: z.boolean(),
  ordine_visualizzazione: z.number().optional(),
  foto: z.any().optional(), // Aggiungi il campo foto allo schema
});

// Schema di trasformazione per convertire i dati del form in payload API
const parameterTransformSchema = parameterFormSchema.transform((data) => {
  return {
    nome_parametro: data.nome_parametro,
    descrizione: data.descrizione,
    tipo_controllo_ui: data.tipo_controllo_ui,
    is_root: data.is_root,
    attivo: data.attivo,
    ordine_visualizzazione: data.ordine_visualizzazione,
    foto: data.foto, // Aggiungi foto alla trasformazione
  };
});

// Tipi inferiti dagli schemi Zod
type FormSchemaType = z.infer<typeof parameterFormSchema>;

const fetchParameter = async (id: string): Promise<IParameter> => {
  const response = await get(`/admin/parametri/${id}`);
  // Mappa i campi del backend ai campi dell'interfaccia IParameter aggiornata
  return {
    id_parametro: response.data.id_parametro,
    nome_parametro: response.data.nome_parametro,
    descrizione: response.data.descrizione,
    tipo_controllo_ui: response.data.tipo_controllo_ui,
    is_root: response.data.is_root,
    attivo: response.data.attivo,
    ordine_visualizzazione: response.data.ordine_visualizzazione,
    data_creazione: response.data.data_creazione,
    data_modifica: response.data.data_modifica, // Assumendo che il backend usi data_modifica
    foto: response.data.foto, // Mappa il campo foto
  };
};

const createParameter = async (data: FormData): Promise<void> => {
  await post('/admin/parametri', data);
};

const updateParameter = async (id: string, data: FormData): Promise<void> => {
  await put(`/admin/parametri/${id}`, data);
};

const ParameterFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [existingPhoto, setExistingPhoto] = useState<string | null>(null); // Stato per la foto esistente

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<FormSchemaType>({
    resolver: zodResolver(parameterFormSchema),
    defaultValues: {
      nome_parametro: '',
      descrizione: '',
      tipo_controllo_ui: '',
      is_root: false,
      attivo: true,
      ordine_visualizzazione: undefined,
    }
  });

  const { data, error, isLoading } = useQuery<IParameter, Error>({ // Usa IParameter per la query
    queryKey: ['parameter', id],
    queryFn: () => fetchParameter(id!),
    enabled: !!id,
  });

  useEffect(() => {
    if (data && !isLoading) {
      reset({
        nome_parametro: data.nome_parametro,
        descrizione: data.descrizione ?? '',
        tipo_controllo_ui: data.tipo_controllo_ui,
        is_root: data.is_root,
        attivo: data.attivo,
        ordine_visualizzazione: data.ordine_visualizzazione,
        // Non resettare il campo foto direttamente, gestiscilo separatamente
      });
      if (data.foto) {
        setExistingPhoto(data.foto);
      } else {
        setExistingPhoto(null);
      }
    }
  }, [data, isLoading, reset, setValue]);

  const mutation = useMutation<void, Error, FormData>({ // Cambia il tipo di payload a FormData
    mutationFn: (formData) => {
      return id ? updateParameter(id, formData) : createParameter(formData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
      toast.success(id ? 'Parametro aggiornato con successo!' : 'Parametro creato con successo!');
      setTimeout(() => {
        navigate('/admin/parameters');
      }, 1500);
    },
    onError: (err) => {
      toast.error(`Errore durante il salvataggio del parametro: ${err.message}`);
    },
  });

  const onSubmit = (formData: FormSchemaType) => {
    const dataToSend = new FormData();
    dataToSend.append('nome_parametro', String(formData.nome_parametro)); // Converti in stringa
    if (formData.descrizione) {
      dataToSend.append('descrizione', String(formData.descrizione)); // Converti in stringa
    }
    dataToSend.append('tipo_controllo_ui', String(formData.tipo_controllo_ui)); // Converti in stringa
    dataToSend.append('is_root', String(formData.is_root)); // Converti booleano in stringa
    dataToSend.append('attivo', String(formData.attivo)); // Converti booleano in stringa
    if (formData.ordine_visualizzazione !== undefined) {
      dataToSend.append('ordine_visualizzazione', String(formData.ordine_visualizzazione)); // Converti numero in stringa
    }

    if (formData.foto && formData.foto.length > 0) {
      dataToSend.append('foto', formData.foto[0]);
    }

    mutation.mutate(dataToSend);
  };

  const handleRemovePhoto = () => {
    setExistingPhoto(null);
    setValue('foto', undefined); // Rimuovi il valore dal form
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" label="Caricamento parametro..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card className="max-w-md mx-auto">
          <CardBody>
            <p className="text-danger">Errore nel caricamento del parametro: {error.message}</p>
            <Button 
              color="primary" 
              onPress={() => navigate('/admin/parameters')}
              className="mt-4"
            >
              Torna alla lista
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate('/admin/parameters')}
          aria-label="Torna indietro"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Button>
        <h1 className="text-3xl font-bold">
          {id ? 'Modifica Parametro' : 'Nuovo Parametro'}
        </h1>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Informazioni Generali */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Informazioni Generali</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nome Parametro */}
              <Controller
                name="nome_parametro"
                control={control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    label="Nome Parametro"
                    placeholder="Inserisci il nome del parametro"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    isRequired
                  />
                )}
              />

              {/* Tipo Controllo UI */}
              <Controller
                name="tipo_controllo_ui"
                control={control}
                render={({ field, fieldState }) => (
                  <Select
                    label="Tipo Controllo UI"
                    placeholder="Seleziona il tipo di controllo"
                    selectedKeys={field.value ? [field.value] : []}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0] as string;
                      field.onChange(selectedKey);
                    }}
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    isRequired
                  >
                    <SelectItem key="SELECT">Select (Dropdown)</SelectItem>
                    <SelectItem key="RADIO">Radio Button</SelectItem>
                    <SelectItem key="CHECKBOX_GROUP">Checkbox Group</SelectItem>
                    <SelectItem key="INPUT_TEXT">Input Testo</SelectItem>
                    <SelectItem key="INPUT_NUMBER">Input Numerico</SelectItem>
                    <SelectItem key="TEXTAREA">Area di Testo</SelectItem>
                  </Select>
                )}
              />

              {/* Ordine Visualizzazione */}
              <Controller
                name="ordine_visualizzazione"
                control={control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    value={field.value === undefined ? '' : String(field.value)}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === '' ? undefined : parseInt(value, 10));
                    }}
                    type="number"
                    label="Ordine Visualizzazione"
                    placeholder="Ordine di visualizzazione (opzionale)"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    description="Lasciare vuoto per assegnare automaticamente"
                  />
                )}
              />
            </div>

            {/* Descrizione */}
            <Controller
              name="descrizione"
              control={control}
              render={({ field, fieldState }) => (
                <Textarea
                  {...field}
                  value={field.value || ''}
                  label="Descrizione"
                  placeholder="Descrizione opzionale del parametro"
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                />
              )}
            />

            {/* Opzioni */}
            <div className="flex gap-6">
              <Controller
                name="is_root"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Parametro Root
                  </Switch>
                )}
              />

              <Controller
                name="attivo"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Parametro Attivo
                  </Switch>
                )}
              />
            </div>
          </CardBody>
        </Card>

        {/* Gestione Foto */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Immagine</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            {/* Foto Esistente */}
            {existingPhoto && (
              <div className="flex items-center gap-4 p-4 bg-default-50 rounded-lg">
                <Image
                  src={existingPhoto}
                  alt="Foto parametro"
                  width={100}
                  height={100}
                  className="rounded-md object-cover"
                />
                <div className="flex-1">
                  <p className="text-sm text-default-600">Immagine attuale</p>
                </div>
                <Button
                  isIconOnly
                  color="danger"
                  variant="light"
                  onPress={handleRemovePhoto}
                  aria-label="Rimuovi foto"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Upload Nuova Foto */}
            <Controller
              name="foto"
              control={control}
              render={({ field, fieldState }) => (
                <div className="space-y-2">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => field.onChange(e.target.files)}
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    startContent={<PhotoIcon className="h-4 w-4" />}
                    description="Formati supportati: JPG, PNG, GIF (max 5MB)"
                  />
                </div>
              )}
            />
          </CardBody>
        </Card>

        {/* Azioni */}
        <Card>
          <CardBody>
            <div className="flex justify-end gap-4">
              <Button
                variant="light"
                onPress={() => navigate('/admin/parameters')}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                isDisabled={isSubmitting}
              >
                {id ? 'Aggiorna Parametro' : 'Crea Parametro'}
              </Button>
            </div>
          </CardBody>
        </Card>
      </form>
    </div>
  );
};


export default ParameterFormPage;