import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  Button,
  Input,
  Card,
  CardBody,
  CardHeader,
  Spinner
} from '@heroui/react';
import { ArrowLeftIcon, KeyIcon } from '@heroicons/react/24/outline';
import { useCliente, useResetClientePassword } from '../hooks/useClienti';
import { resetPasswordSchema, ResetPasswordFormData } from '../schemas/clienteSchemas';

export default function ClienteResetPasswordPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const clienteId = id ? parseInt(id) : undefined;
  
  // Hooks
  const { data: cliente, isLoading: isLoadingCliente } = useCliente(clienteId!);
  const resetPassword = useResetClientePassword();
  
  // Form setup
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      new_password: '',
      confirm_password: '',
    }
  });
  
  // Submit handler
  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!clienteId) return;
    
    try {
      await resetPassword.mutateAsync({
        id: clienteId,
        newPassword: data.new_password
      });
      
      toast.success('Password resettata con successo');
      navigate('/admin/clienti');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Errore durante il reset della password';
      toast.error(errorMessage);
    }
  };
  
  if (isLoadingCliente) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }
  
  if (!cliente) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          Cliente non trovato
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate('/admin/clienti')}
        >
          <ArrowLeftIcon className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <KeyIcon className="h-6 w-6" />
            Reset Password Cliente
          </h1>
          <p className="text-gray-600">
            Reset della password per <strong>{cliente.denominazione}</strong>
          </p>
        </div>
      </div>
      
      {/* Info Cliente */}
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold">Informazioni Cliente</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Username:</span> {cliente.username}
            </div>
            <div>
              <span className="font-medium">Email:</span> {cliente.email}
            </div>
            <div>
              <span className="font-medium">Nome:</span> {cliente.nome_completo}
            </div>
            <div>
              <span className="font-medium">Stato:</span>{' '}
              <span className={cliente.attivo ? 'text-green-600' : 'text-red-600'}>
                {cliente.attivo ? 'Attivo' : 'Disattivo'}
              </span>
            </div>
          </div>
        </CardBody>
      </Card>
      
      {/* Form Reset Password */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Nuova Password</h3>
        </CardHeader>
        <CardBody>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <Controller
              name="new_password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="password"
                  label="Nuova Password"
                  placeholder="Inserisci la nuova password"
                  isRequired
                  isInvalid={!!errors.new_password}
                  errorMessage={errors.new_password?.message}
                  description="La password deve essere di almeno 6 caratteri"
                />
              )}
            />
            
            <Controller
              name="confirm_password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="password"
                  label="Conferma Password"
                  placeholder="Conferma la nuova password"
                  isRequired
                  isInvalid={!!errors.confirm_password}
                  errorMessage={errors.confirm_password?.message}
                />
              )}
            />
            
            {/* Azioni */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                variant="light"
                onPress={() => navigate('/admin/clienti')}
                isDisabled={isSubmitting}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                startContent={!isSubmitting && <KeyIcon className="h-4 w-4" />}
              >
                Reset Password
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>
      
      {/* Avviso */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start gap-2">
          <div className="text-yellow-600 mt-0.5">⚠️</div>
          <div className="text-sm text-yellow-800">
            <p className="font-medium">Attenzione:</p>
            <p>
              Il reset della password è un'azione irreversibile. Il cliente dovrà utilizzare 
              la nuova password per accedere al sistema. Assicurati di comunicare la nuova 
              password al cliente in modo sicuro.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}