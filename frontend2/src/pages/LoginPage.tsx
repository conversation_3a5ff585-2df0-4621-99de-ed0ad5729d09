import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input, Button } from '@heroui/react';
import { useNavigate } from 'react-router-dom';
import { isAxiosError } from 'axios';
import { LoginFormData } from '../types/auth';
import { useAuth } from '../contexts/AuthContext'; // Importa useAuth

// Schema di validazione Zod basato sull'interfaccia LoginFormData
const loginSchema = z.object({
  username: z.string().min(1, { message: 'Username è richiesto' }),
  password: z.string().min(1, { message: 'Password è richiesta' }),
});

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, loading, error, isAuthenticated } = useAuth(); // Usa il contesto di autenticazione
  const [localError, setLocalError] = useState<string | null>(null); // Errore locale per il form

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  // Reindirizza se già autenticato
  React.useEffect(() => {
    if (isAuthenticated) {
      navigate('/admin/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const onSubmit: SubmitHandler<LoginFormData> = async (data) => {
    setLocalError(null); // Resetta l'errore locale
    try {
      await login(data); // Delega la logica di login al contesto
      reset(); // Resetta il form dopo il login (se il login ha successo, il reindirizzamento avviene tramite useEffect)
    } catch (err) {
      if (isAxiosError(err)) {
        setLocalError(err.response?.data?.msg || 'Login fallito. Si prega di riprovare.');
      } else {
        setLocalError('Si è verificato un errore inaspettato.');
      }
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="px-8 py-6 mt-4 text-left bg-white shadow-lg rounded-lg">
        <h3 className="text-2xl font-bold text-center">Login</h3>
        <form onSubmit={handleSubmit(onSubmit)} className="mt-4">
          <div className="mb-4">
            <Input
              id="username"
              label="Username"
              type="text"
              {...register('username')}
              isInvalid={!!errors.username}
              errorMessage={errors.username?.message}
              disabled={loading}
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
            />
          </div>
          <div className="mb-6">
            <Input
              id="password"
              label="Password"
              type="password"
              {...register('password')}
              isInvalid={!!errors.password}
              errorMessage={errors.password?.message}
              disabled={loading}
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
            />
          </div>
          {(error || localError) && <p className="text-red-500 text-xs italic mb-4">{error || localError}</p>}
          <div className="flex items-baseline justify-between">
            <Button
              type="submit"
              disabled={loading}
              className="px-6 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"
            >
              {loading ? 'Accedendo...' : 'Accedi'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;