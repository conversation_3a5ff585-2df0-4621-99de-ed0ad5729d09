import React, { useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import {
  Card,
  CardHeader,
  CardBody,
  Button,
  Input,
  Textarea,
  Switch,
  Select,
  SelectItem,
  Spinner,
} from '@heroui/react';
import { PlusIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';

import {
  useGetDependencyRule,
  useCreateDependencyRule,
  useUpdateDependencyRule,
} from '../hooks/useDependencyRules';
import { useGetAllParametersForSelect } from '../hooks/useParametersForSelect';

// Importa gli schemi Zod
import {
  RegolaDipendenzaComplessaFormSchema,
} from '../schemas/dependencyRuleSchemas';
import { LOGICHE_COMBINAZIONE } from '../types/dependency-rules';

// Importa i componenti aggiornati
import CondizioneFormItem from '../components/CondizioneFormItem';
import RisultatoFormItem from '../components/RisultatoFormItem';

// Tipo personalizzato per il form che include i campi con valori di default
type FormData = {
  nome_regola: string;
  descrizione?: string | null;
  attiva: boolean;
  logica_combinazione_condizioni: 'AND' | 'OR';
  condizioni: {
    id_parametro_condizionante: number;
    tipo_condizione: string;
    id_valore_condizione_predefinita: number | null;
    valore_condizione_libero: string | null;
    ordine_valutazione: number;
  }[];
  risultati: {
    id_parametro_effetto: number;
    tipo_effetto: string;
    id_valore_effetto_predefinito: number | null;
    id_valori_effetto_predefiniti: number[] | null;
    valore_effetto_libero: string | null;
  }[];
};

const DependencyRuleFormPage: React.FC = () => {
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const ruleId = id ? parseInt(id, 10) : undefined;
  const isEditMode = !!ruleId;

  const { data: ruleData, isLoading: isLoadingRule, error: errorRule } = useGetDependencyRule(ruleId!);
  const { isLoading: isLoadingAllParameters } = useGetAllParametersForSelect();
  const createMutation = useCreateDependencyRule();
  const updateMutation = useUpdateDependencyRule();

  const methods = useForm<FormData>({
    resolver: zodResolver(RegolaDipendenzaComplessaFormSchema),
    defaultValues: {
      nome_regola: '',
      descrizione: '',
      attiva: true,
      logica_combinazione_condizioni: 'AND',
      condizioni: [{
        id_parametro_condizionante: 0,
        tipo_condizione: '',
        id_valore_condizione_predefinita: null,
        valore_condizione_libero: null,
        ordine_valutazione: 0,
      }],
      risultati: [{
        id_parametro_effetto: 0,
        tipo_effetto: '',
        id_valore_effetto_predefinito: null,
        id_valori_effetto_predefiniti: null,
        valore_effetto_libero: null,
      }],
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = methods;

  const {
    fields: condizioneFields,
    append: appendCondizione,
    remove: removeCondizione,
  } = useFieldArray({
    control,
    name: 'condizioni',
  });

  const {
    fields: risultatoFields,
    append: appendRisultato,
    remove: removeRisultato,
  } = useFieldArray({
    control,
    name: 'risultati',
  });

  useEffect(() => {
    if (isEditMode && ruleData) {
      // Raggruppa i risultati FILTER_VALUES per parametro
      const risultatiRaggruppati = ruleData.risultati.reduce((acc, res) => {
        if (res.tipo_effetto === 'FILTER_VALUES') {
          const existing = acc.find(r => 
            r.id_parametro_effetto === res.id_parametro_effetto && 
            r.tipo_effetto === 'FILTER_VALUES'
          );
          
          if (existing) {
            // Aggiungi il valore all'array esistente
            if (res.id_valore_effetto_predefinito) {
              existing.id_valori_effetto_predefiniti = existing.id_valori_effetto_predefiniti || [];
              existing.id_valori_effetto_predefiniti.push(res.id_valore_effetto_predefinito);
            }
          } else {
            // Crea nuovo gruppo
            acc.push({
              id_parametro_effetto: res.id_parametro_effetto,
              tipo_effetto: res.tipo_effetto,
              id_valore_effetto_predefinito: null,
              id_valori_effetto_predefiniti: res.id_valore_effetto_predefinito ? [res.id_valore_effetto_predefinito] : null,
              valore_effetto_libero: res.valore_effetto_libero,
            });
          }
        } else {
          // Risultato normale
          acc.push({
            id_parametro_effetto: res.id_parametro_effetto,
            tipo_effetto: res.tipo_effetto,
            id_valore_effetto_predefinito: res.id_valore_effetto_predefinito,
            id_valori_effetto_predefiniti: null,
            valore_effetto_libero: res.valore_effetto_libero,
          });
        }
        return acc;
      }, [] as any[]);

      // Usa setTimeout per assicurarsi che il reset avvenga dopo il rendering dei componenti
      setTimeout(() => {
        reset({
          nome_regola: ruleData.nome_regola,
          descrizione: ruleData.descrizione || '',
          attiva: ruleData.attiva,
          logica_combinazione_condizioni: ruleData.logica_combinazione_condizioni as 'AND' | 'OR',
          condizioni: ruleData.condizioni.map(cond => ({
            id_parametro_condizionante: cond.id_parametro_condizionante,
            tipo_condizione: cond.tipo_condizione,
            id_valore_condizione_predefinita: cond.id_valore_condizione_predefinita,
            valore_condizione_libero: cond.valore_condizione_libero,
            ordine_valutazione: cond.ordine_valutazione,
          })),
          risultati: risultatiRaggruppati,
        });
      }, 100);
    }
  }, [isEditMode, ruleData, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      // Trasforma i risultati: espande FILTER_VALUES multipli in risultati singoli
      const risultatiTrasformati = data.risultati
        .filter(res => res.id_parametro_effetto && res.tipo_effetto)
        .flatMap(res => {
          if (res.tipo_effetto === 'FILTER_VALUES' && res.id_valori_effetto_predefiniti) {
            // Crea un risultato per ogni valore selezionato
            return res.id_valori_effetto_predefiniti.map(valoreId => ({
              id_parametro_effetto: res.id_parametro_effetto,
              tipo_effetto: res.tipo_effetto,
              id_valore_effetto_predefinito: valoreId,
              valore_effetto_libero: null,
            }));
          } else {
            // Risultato normale (SET_VALUE, SHOW, HIDE, etc.)
            return [{
              id_parametro_effetto: res.id_parametro_effetto,
              tipo_effetto: res.tipo_effetto,
              id_valore_effetto_predefinito: res.id_valore_effetto_predefinito,
              valore_effetto_libero: res.valore_effetto_libero,
            }];
          }
        });

      const payload = {
        nome_regola: data.nome_regola,
        descrizione: data.descrizione || null,
        attiva: data.attiva,
        logica_combinazione_condizioni: data.logica_combinazione_condizioni,
        condizioni: data.condizioni.filter(cond => 
          cond.id_parametro_condizionante && cond.tipo_condizione
        ),
        risultati: risultatiTrasformati,
      };

      if (isEditMode && ruleId) {
        await updateMutation.mutateAsync({ id: ruleId, payload });
        toast.success('Regola aggiornata con successo!');
      } else {
        await createMutation.mutateAsync(payload);
        toast.success('Regola creata con successo!');
      }
      navigate('/admin/dependency-rules');
    } catch (err: any) {
      console.error('Errore durante il salvataggio:', err);
      toast.error(`Errore: ${err.response?.data?.message || err.message}`);
    }
  };

  // Funzioni per aggiungere nuove condizioni e risultati
  const addCondizione = () => {
    appendCondizione({
      id_parametro_condizionante: 0,
      tipo_condizione: '',
      id_valore_condizione_predefinita: null,
      valore_condizione_libero: null,
      ordine_valutazione: condizioneFields.length,
    });
  };

  const addRisultato = () => {
    appendRisultato({
      id_parametro_effetto: 0,
      tipo_effetto: '',
      id_valore_effetto_predefinito: null,
      id_valori_effetto_predefiniti: null,
      valore_effetto_libero: null,
    });
  };

  if (isLoadingRule || isLoadingAllParameters) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" label="Caricamento..." />
      </div>
    );
  }

  if (errorRule && isEditMode) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="max-w-md">
          <CardBody>
            <p className="text-danger">Errore nel caricamento della regola: {errorRule.message}</p>
            <Button 
              color="primary" 
              onPress={() => navigate('/admin/dependency-rules')}
              className="mt-4"
            >
              Torna alla lista
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }
 
  return (
    <FormProvider {...methods}>
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            isIconOnly
            variant="light"
            onPress={() => navigate('/admin/dependency-rules')}
            aria-label="Torna indietro"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold">
            {isEditMode ? 'Modifica Regola di Dipendenza' : 'Crea Nuova Regola di Dipendenza'}
          </h1>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Informazioni Generali */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Informazioni Generali</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Nome Regola */}
                <Controller
                  name="nome_regola"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Input
                      {...field}
                      label="Nome Regola"
                      placeholder="Inserisci il nome della regola"
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                      isRequired
                    />
                  )}
                />

                {/* Logica Combinazione */}
                <Controller
                  name="logica_combinazione_condizioni"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Select
                      label="Logica di combinazione"
                      placeholder="Seleziona la logica"
                      selectedKeys={field.value ? [field.value] : []}
                      onSelectionChange={(keys) => {
                        const selectedKey = Array.from(keys)[0] as string;
                        field.onChange(selectedKey);
                      }}
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    >
                      {LOGICHE_COMBINAZIONE.map((logica) => (
                        <SelectItem key={logica.value}>
                          {logica.label}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                />
              </div>

              {/* Descrizione */}
              <Controller
                name="descrizione"
                control={control}
                render={({ field, fieldState }) => (
                  <Textarea
                    {...field}
                    value={field.value || ''}
                    label="Descrizione"
                    placeholder="Descrizione opzionale della regola"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  />
                )}
              />

              {/* Attiva */}
              <Controller
                name="attiva"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Regola attiva
                  </Switch>
                )}
              />
            </CardBody>
          </Card>

          {/* Condizioni */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-primary">Condizioni</h2>
                <Button
                  color="primary"
                  variant="flat"
                  startContent={<PlusIcon className="h-4 w-4" />}
                  onPress={addCondizione}
                >
                  Aggiungi Condizione
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              {condizioneFields.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Nessuna condizione aggiunta. Clicca "Aggiungi Condizione" per iniziare.
                </div>
              ) : (
                <div className="space-y-4">
                  {condizioneFields.map((field, index) => (
                    <CondizioneFormItem
                      key={field.id}
                      index={index}
                      onRemove={() => removeCondizione(index)}
                    />
                  ))}
                </div>
              )}
              {errors.condizioni && (
                <p className="text-danger text-sm mt-2">{errors.condizioni.message}</p>
              )}
            </CardBody>
          </Card>

          {/* Risultati */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-secondary">Risultati</h2>
                <Button
                  color="secondary"
                  variant="flat"
                  startContent={<PlusIcon className="h-4 w-4" />}
                  onPress={addRisultato}
                >
                  Aggiungi Risultato
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              {risultatoFields.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Nessun risultato aggiunto. Clicca "Aggiungi Risultato" per iniziare.
                </div>
              ) : (
                <div className="space-y-4">
                  {risultatoFields.map((field, index) => (
                    <RisultatoFormItem
                      key={field.id}
                      index={index}
                      onRemove={() => removeRisultato(index)}
                    />
                  ))}
                </div>
              )}
              {errors.risultati && (
                <p className="text-danger text-sm mt-2">{errors.risultati.message}</p>
              )}
            </CardBody>
          </Card>

          {/* Azioni */}
          <Card>
            <CardBody>
              <div className="flex justify-end gap-4">
                <Button
                  variant="light"
                  onPress={() => navigate('/admin/dependency-rules')}
                >
                  Annulla
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  isLoading={isSubmitting}
                  isDisabled={isSubmitting}
                >
                  {isEditMode ? 'Aggiorna Regola' : 'Crea Regola'}
                </Button>
              </div>
            </CardBody>
          </Card>
        </form>
      </div>
    </FormProvider>
  );
};

export default DependencyRuleFormPage;