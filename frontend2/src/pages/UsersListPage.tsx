import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useNavigate } from 'react-router-dom';
import { del, get } from '../services/api';
import { IUser } from '../types/users';
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Button, getKeyValue } from '@heroui/react';
import { addToast } from '@heroui/toast';

const UsersListPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: users, isLoading, error } = useQuery<IUser[]>({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await get('/admin/utenti');
      return response.data.items;
    },
  });

  const deleteUserMutation = useMutation({
    mutationFn: (userId: number) => del(`/admin/utenti/${userId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      addToast({
        title: 'Successo',
        description: 'Utente eliminato con successo!',
        color: 'success',
      });
    },
    onError: (err: any) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'eliminazione dell'utente: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const handleDelete = (id_utente: number) => {
    if (window.confirm('Sei sicuro di voler eliminare questo utente?')) {
      deleteUserMutation.mutate(id_utente);
    }
  };

  if (isLoading) return <div>Caricamento utenti...</div>;
  if (error) return <div>Errore durante il caricamento degli utenti: {error.message}</div>;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Gestione Utenti</h1>
      <div className="mb-4">
        <Link to="/admin/users/new">
          <Button color="primary">Aggiungi Nuovo Utente</Button>
        </Link>
      </div>
      <Table aria-label="Tabella utenti">
        <TableHeader columns={[
          { key: 'id_utente', label: 'ID' },
          { key: 'username', label: 'Username' },
          { key: 'email', label: 'Email' },
          { key: 'nome_completo', label: 'Nome Completo' },
          { key: 'attivo', label: 'Attivo' },
          { key: 'actions', label: 'Azioni' },
        ]}>
          {(column) => (
            <TableColumn key={column.key}>{column.label}</TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={users || []}
          isLoading={isLoading}
          loadingContent="Caricamento utenti..."
          emptyContent="Nessun utente trovato."
        >
          {(user) => (
            <TableRow key={user.id_utente}>
              {(columnKey) => (
                <TableCell>
                  {columnKey === 'actions' ? (
                    <>
                      <Button
                        onPress={() => navigate(`/admin/users/${user.id_utente}`)}
                        color="warning"
                        size="sm"
                        className="mr-2"
                      >
                        Modifica
                      </Button>
                      <Button
                        onPress={() => handleDelete(user.id_utente)}
                        color="danger"
                        size="sm"
                      >
                        Elimina
                      </Button>
                    </>
                  ) : columnKey === 'attivo' ? (
                    user.attivo ? 'Sì' : 'No'
                  ) : (
                    getKeyValue(user, columnKey)
                  )}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default UsersListPage;