import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { get, post, put } from '../services/api';
import { IUserFormPayload, IUser } from '../types/users';
import { Input, Button, Checkbox, addToast } from '@heroui/react';

// Estende IUserFormPayload per includere il campo confirmPassword per il form
interface IUserFormInput extends IUserFormPayload {
  confirmPassword?: string;
}

// Schema di validazione per i dati dell'utente
const userSchema = z.object({
  username: z.string().min(1, 'Username è richiesto'),
  email: z.string().email('Email non valida'),
  nome_completo: z.string().nullable().optional(),
  attivo: z.boolean(),
  password: z.string().optional(), // La password è opzionale qui, gestita separatamente per la modifica
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.password && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: 'Le password non corrispondono',
  path: ['confirmPassword'],
});

const UserFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEditMode = !!id;

  const { handleSubmit, reset, control, formState: { errors } } = useForm<IUserFormInput>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: '',
      email: '',
      nome_completo: '',
      attivo: true,
      password: '',
      confirmPassword: '',
    },
  });

  const { data: user, isLoading: isLoadingUser } = useQuery<IUser>({
    queryKey: ['user', id],
    queryFn: async () => {
      const response = await get(`/admin/utenti/${id}`);
      return response.data;
    },
    enabled: isEditMode, // Esegui la query solo in modalità modifica
  });

  useEffect(() => {
    if (isEditMode && user) {
      const resetValues = {
        username: user.username,
        email: user.email,
        nome_completo: user.nome_completo || '', // Assicurati che sia una stringa vuota se null
        attivo: user.attivo,
        // Non resettiamo i campi password in modalità modifica per mantenerli vuoti di default
        password: '',
        confirmPassword: '',
      };
      reset(resetValues);
    }
  }, [isEditMode, user, reset]);

  const createUserMutation = useMutation({
    mutationFn: (payload: IUserFormPayload) => post('/admin/utenti', payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      addToast({
        title: 'Utente creato',
        description: 'Utente creato con successo!',
        color: 'success',
      });
      navigate('/admin/utenti');
    },
    onError: (err: any) => {
      addToast({
        title: 'Errore',
        description: `Errore durante la creazione dell'utente: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const updateUserMutation = useMutation({
    mutationFn: (payload: IUserFormPayload) => put(`/admin/utenti/${id}`, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      addToast({
        title: 'Utente aggiornato',
        description: 'Utente aggiornato con successo!',
        color: 'success',
      });
      navigate('/admin/utenti');
    },
    onError: (err: any) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'aggiornamento dell'utente: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const onSubmit = (data: IUserFormInput) => {
    const payload: IUserFormPayload = {
      username: data.username,
      email: data.email,
      nome_completo: data.nome_completo === '' ? null : data.nome_completo, // Invia null se il campo è vuoto
      attivo: data.attivo,
    };

    if (data.password) {
      payload.password = data.password;
    }

    if (isEditMode) {
      updateUserMutation.mutate(payload);
    } else {
      createUserMutation.mutate(payload);
    }
  };

  if (isLoadingUser) return <div className="flex justify-center items-center h-screen">Caricamento dati utente...</div>;
 
   return (
     <div className="container mx-auto p-4">
       <h1 className="text-2xl font-bold mb-6">{isEditMode ? 'Modifica Utente' : 'Crea Nuovo Utente'}</h1>
 
       <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
           <Controller
             name="username"
             control={control}
             render={({ field }) => (
               <Input
                 type="text"
                 id="username"
                 label="Username"
                 labelPlacement="outside"
                 fullWidth
                 {...field}
                 isInvalid={!!errors.username}
                 errorMessage={errors.username?.message}
               />
             )}
           />
           <Controller
             name="email"
             control={control}
             render={({ field }) => (
               <Input
                 type="email"
                 id="email"
                 label="Email"
                 labelPlacement="outside"
                 fullWidth
                 {...field}
                 isInvalid={!!errors.email}
                 errorMessage={errors.email?.message}
               />
             )}
           />
           <Controller
             name="nome_completo"
             control={control}
             render={({ field }) => (
               <Input
                 type="text"
                 id="nome_completo"
                 label="Nome Completo"
                 labelPlacement="outside"
                 fullWidth
                 {...field}
                 value={field.value || ''} // Assicurati che il valore sia sempre una stringa
                 isInvalid={!!errors.nome_completo}
                 errorMessage={errors.nome_completo?.message}
               />
             )}
           />
           <Controller
             name="password"
             control={control}
             render={({ field }) => (
               <Input
                 type="password"
                 id="password"
                 label="Password (lascia vuoto per non modificare)"
                 labelPlacement="outside"
                 fullWidth
                 {...field}
                 isInvalid={!!errors.password}
                 errorMessage={errors.password?.message}
               />
             )}
           />
           <Controller
             name="confirmPassword"
             control={control}
             render={({ field }) => (
               <Input
                 type="password"
                 id="confirmPassword"
                 label="Conferma Password"
                 labelPlacement="outside"
                 fullWidth
                 {...field}
                 isInvalid={!!errors.confirmPassword}
                 errorMessage={errors.confirmPassword?.message}
               />
             )}
           />
           <div className="flex flex-col gap-4">
             <Controller
               name="attivo"
               control={control}
               render={({ field }) => (
                 <Checkbox
                   id="attivo"
                   isSelected={field.value}
                   onValueChange={field.onChange}
                 >
                   Attivo
                 </Checkbox>
               )}
             />
             {errors.attivo && <span className="text-danger text-sm">{errors.attivo.message}</span>}
           </div>
         </div>
         <div className="flex gap-4 justify-end mt-6">
           <Button type="submit" color="primary">
             {isEditMode ? 'Aggiorna Utente' : 'Crea Utente'}
           </Button>
           <Button type="button" onClick={() => navigate('/admin/users')} variant="bordered">
             Annulla
           </Button>
         </div>
       </form>
     </div>
  );
};

export default UserFormPage;