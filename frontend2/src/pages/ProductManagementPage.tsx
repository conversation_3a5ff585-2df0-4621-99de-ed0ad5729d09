import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>, 
  CardHeader, 
  CardBody, 
  Button,
  Spinner,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  useDisclosure
} from '@heroui/react';
import { PlusIcon, ExclamationTriangleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { useGetProducts, useGetProductPrerequisites } from '../hooks/useProducts';
import ProductList from '../components/ProductList';
import ProductQuickCreateModal from '../components/ProductQuickCreateModal';
import ProductFilters from '../components/ProductFilters';
import { ProductFiltersData } from '../schemas/productSchemas';
import { IProduct } from '../types/products';

const ProductManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { isOpen: isQuickCreateOpen, onOpen: onQuickCreateOpen, onClose: onQuickCreateClose } = useDisclosure();
  const [filters, setFilters] = React.useState<ProductFiltersData>({});
  const [filteredProducts, setFilteredProducts] = React.useState<IProduct[]>([]);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = React.useState(false);
  
  const { data: productsData, isLoading: productsLoading, error: productsError, refetch } = useGetProducts();
  const { data: prerequisitesData, isLoading: prerequisitesLoading } = useGetProductPrerequisites();

  const handleQuickCreateSuccess = () => {
    refetch(); // Ricarica la lista prodotti
  };

  // Applica i filtri ai prodotti
  // Usiamo useRef per evitare loop infiniti
  const prevFiltersRef = React.useRef<ProductFiltersData>({});
  const prevProductsRef = React.useRef<IProduct[] | null>(null);
  
  React.useEffect(() => {
    if (!productsData?.products) {
      setFilteredProducts([]);
      return;
    }
    
    // Verifica se i prodotti o i filtri sono cambiati
    const productsChanged = prevProductsRef.current !== productsData.products;
    const filtersChanged = 
      prevFiltersRef.current.search !== filters.search ||
      prevFiltersRef.current.tecnica !== filters.tecnica ||
      prevFiltersRef.current.tipologia_impianto !== filters.tipologia_impianto ||
      prevFiltersRef.current.connessione !== filters.connessione ||
      prevFiltersRef.current.attiva !== filters.attiva;
    
    // Aggiorna solo se necessario
    if (productsChanged || filtersChanged) {
      prevProductsRef.current = productsData.products;
      prevFiltersRef.current = { ...filters };
      
      let filtered = [...productsData.products];

    // Filtro per ricerca testuale
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(product => 
        product.nome_prodotto.toLowerCase().includes(searchLower) ||
        (product.descrizione && product.descrizione.toLowerCase().includes(searchLower))
      );
    }

    // Filtro per tecnica
    if (filters.tecnica) {
      filtered = filtered.filter(product =>
        product.regole_associate.some(regola =>
          Object.values(regola.prerequisiti).some(prerequisito =>
            prerequisito.id_valore === filters.tecnica
          )
        )
      );
    }

    // Filtro per tipologia impianto
    if (filters.tipologia_impianto) {
      filtered = filtered.filter(product =>
        product.regole_associate.some(regola =>
          Object.values(regola.prerequisiti).some(prerequisito =>
            prerequisito.id_valore === filters.tipologia_impianto
          )
        )
      );
    }

    // Filtro per connessione
    if (filters.connessione) {
      filtered = filtered.filter(product =>
        product.regole_associate.some(regola =>
          Object.values(regola.prerequisiti).some(prerequisito =>
            prerequisito.id_valore === filters.connessione
          )
        )
      );
    }

    // Filtro per regole attive
    if (filters.attiva !== undefined) {
      if (filters.attiva) {
        filtered = filtered.filter(product =>
          product.regole_associate.some(regola => regola.attiva)
        );
      } else {
        filtered = filtered.filter(product =>
          product.regole_associate.length === 0 || 
          product.regole_associate.every(regola => !regola.attiva)
        );
      }
      }

      setFilteredProducts(filtered);
    }
  }, [productsData?.products, filters]);

  if (productsLoading || prerequisitesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" label="Caricamento..." />
      </div>
    );
  }

  if (productsError) {
    return (
      <Card className="max-w-md mx-auto">
        <CardBody className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-danger mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-danger mb-2">Errore nel caricamento</h3>
          <p className="text-gray-600 mb-4">
            {(productsError as Error).message || 'Impossibile caricare i prodotti'}
          </p>
          <Button 
            color="primary" 
            onPress={() => window.location.reload()}
          >
            Riprova
          </Button>
        </CardBody>
      </Card>
    );
  }

  // Verifica se il sistema è pronto
  const sistemaReady = prerequisitesData?.sistema_pronto ?? false;
  const prerequisitiMancanti = prerequisitesData?.prerequisiti_mancanti ?? [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestione Prodotti</h1>
          <p className="text-gray-600 mt-1">
            Gestisci i prodotti (Diametri) e le loro regole di dipendenza
          </p>
        </div>
        
        <Dropdown>
          <DropdownTrigger>
            <Button
              color="primary"
              endContent={<ChevronDownIcon className="h-4 w-4" />}
              isDisabled={!sistemaReady}
            >
              <PlusIcon className="h-5 w-5" />
              Nuovo Prodotto
            </Button>
          </DropdownTrigger>
          <DropdownMenu aria-label="Opzioni creazione prodotto">
            <DropdownItem
              key="quick"
              description="Creazione rapida in modal"
              onPress={onQuickCreateOpen}
            >
              Creazione Rapida
            </DropdownItem>
            <DropdownItem
              key="full"
              description="Pagina completa con più opzioni"
              onPress={() => navigate('/admin/products/new')}
            >
              Creazione Completa
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>

      {/* Stato del Sistema */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Stato del Sistema</h2>
        </CardHeader>
        <CardBody>
          <div className="flex items-center gap-4">
            <Chip 
              color={sistemaReady ? "success" : "warning"}
              variant="flat"
            >
              {sistemaReady ? "Sistema Pronto" : "Configurazione Incompleta"}
            </Chip>
            
            {prerequisitesData?.parametro_diametro && (
              <Chip color="primary" variant="flat">
                Parametro Diametro: {prerequisitesData.parametro_diametro.nome_parametro}
              </Chip>
            )}
          </div>
          
          {prerequisitiMancanti.length > 0 && (
            <div className="mt-4 p-4 bg-warning-50 border border-warning-200 rounded-lg">
              <h3 className="font-medium text-warning-800 mb-2">
                Prerequisiti Mancanti:
              </h3>
              <ul className="list-disc list-inside text-warning-700 space-y-1">
                {prerequisitiMancanti.map((prerequisito, index) => (
                  <li key={index}>{prerequisito}</li>
                ))}
              </ul>
              <p className="text-sm text-warning-600 mt-2">
                Configura questi parametri prima di creare prodotti.
              </p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Filtri */}
      {productsData && (
        <ProductFilters
          onFiltersChange={setFilters}
          isCollapsed={isFiltersCollapsed}
          onToggleCollapse={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
        />
      )}

      {/* Statistiche */}
      {productsData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-primary">
                {productsData.total_products}
              </div>
              <div className="text-gray-600">Prodotti Totali</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-info">
                {filteredProducts.length}
              </div>
              <div className="text-gray-600">Prodotti Filtrati</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-success">
                {productsData.products.filter(p => p.regole_associate.some(r => r.attiva)).length}
              </div>
              <div className="text-gray-600">Con Regole Attive</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-warning">
                {productsData.products.filter(p => p.regole_associate.length === 0).length}
              </div>
              <div className="text-gray-600">Senza Regole</div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Lista Prodotti */}
      {productsData && (
        <ProductList 
          products={filteredProducts}
          sistemaReady={sistemaReady}
        />
      )}

      {/* Modal Creazione Rapida */}
      <ProductQuickCreateModal
        isOpen={isQuickCreateOpen}
        onClose={onQuickCreateClose}
        onSuccess={handleQuickCreateSuccess}
      />
    </div>
  );
};

export default ProductManagementPage;