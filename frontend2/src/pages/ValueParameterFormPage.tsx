import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  Input,
  Button,
  Textarea,
  Card,
  CardHeader,
  CardBody,
  Spinner,
  Image,
} from '@heroui/react';
import { ArrowLeftIcon, PhotoIcon, TrashIcon, SwatchIcon } from '@heroicons/react/24/outline';
import { get, post, put } from '../services/api';

import { IValueParameterFormPayload, IValueParameter } from '../types/value-parameters';

// Schema Zod aggiornato per la validazione del form
const valueParameterSchema = z.object({
  testo_visualizzato_ui: z.string({
    required_error: 'Il testo visualizzato è obbligatorio'
  }).min(1, 'Il testo visualizzato è obbligatorio').max(255, 'Il testo non può superare i 255 caratteri'),
  
  ordine_visualizzazione: z.number().int().min(0, 'L\'ordine deve essere un numero positivo'),
  
  colore: z.string().max(50, 'Il colore non può superare i 50 caratteri').optional(),
  
  descrizione: z.string().max(1000, 'La descrizione non può superare i 1000 caratteri').optional(),
  
  foto: z.any().optional(), // Gestito separatamente per l'upload di file
});

// Tipo per il form
type ValueParameterFormData = {
  testo_visualizzato_ui: string;
  ordine_visualizzazione: number;
  colore?: string;
  descrizione?: string;
  foto?: FileList;
};

const fetchValueParameter = async (valueId: string): Promise<any> => {
  const response = await get(`/admin/valoriparametro/${valueId}`);
  return response.data;
};

const saveValueParameter = async (data: FormData, valueId?: string): Promise<void> => {
  if (valueId) {
    await put(`/admin/valoriparametro/${valueId}`, data);
  } else {
    await post(`/admin/valoriparametro`, data);
  }
};

const ValueParameterFormPage: React.FC = () => {
  const { parameterId, valueId } = useParams<{ parameterId: string; valueId?: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [existingPhoto, setExistingPhoto] = useState<string | null>(null); // Stato per la foto esistente

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ValueParameterFormData>({
    resolver: zodResolver(valueParameterSchema),
    defaultValues: {
      testo_visualizzato_ui: '',
      ordine_visualizzazione: 0,
      colore: '',
      descrizione: '',
    }
  });

  const { data, error, isLoading } = useQuery<any, Error>({
    queryKey: ['valueParameter', parameterId, valueId],
    queryFn: () => fetchValueParameter(valueId!),
    enabled: !!valueId, // Abilita solo se l'ID del valore è presente per la modifica
  });

  useEffect(() => {
    if (data && !isLoading) {
      reset({
        testo_visualizzato_ui: data.testo_visualizzato_ui || '',
        ordine_visualizzazione: data.ordine_visualizzazione || 0,
        colore: data.colore || '',
        descrizione: data.descrizione || '',
      });
      if (data.foto) {
        setExistingPhoto(data.foto);
      } else {
        setExistingPhoto(null);
      }
    }
  }, [data, isLoading, reset]);

  const mutation = useMutation<void, Error, ValueParameterFormData>({
    mutationFn: async (formData) => {
      if (!parameterId) throw new Error('ID Parametro non presente.');

      const dataToSend = new FormData();
      dataToSend.append('testo_visualizzato_ui', formData.testo_visualizzato_ui);
      dataToSend.append('ordine_visualizzazione', String(formData.ordine_visualizzazione));
      if (formData.colore) dataToSend.append('colore', formData.colore);
      if (formData.descrizione) dataToSend.append('descrizione', formData.descrizione);
      
      // Aggiungi l'id_parametro solo per la creazione (POST)
      if (!valueId) {
        dataToSend.append('id_parametro', parameterId);
      }

      // Aggiungi la logica per l'upload del file
      if (formData.foto && formData.foto.length > 0) {
        dataToSend.append('foto', formData.foto[0]);
      } else if (valueId && !formData.foto) {
        // Se si sta modificando e non viene fornita una nuova foto,
        // e la foto esistente è stata rimossa (come da form di modifica che pulisce il campo),
        // potremmo voler inviare un segnale per rimuoverla lato backend.
        // Per ora, non inviamo nulla se il campo è vuoto.
        // Potrebbe essere implementata una logica `set_foto_null: true` nel payload se necessario.
      }
      return saveValueParameter(dataToSend, valueId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['valueParameters', parameterId] });
      toast.success(valueId ? 'Valore parametro aggiornato con successo!' : 'Valore parametro creato con successo!');
      setTimeout(() => {
        navigate(`/admin/parameters/${parameterId}/values`);
      }, 1500);
    },
    onError: (err) => {
      toast.error(`Errore durante il salvataggio del valore: ${err.message}`);
    },
  });

  const onSubmit = (formData: ValueParameterFormData) => {
    mutation.mutate(formData);
  };

  const handleRemovePhoto = () => {
    setExistingPhoto(null);
    setValue('foto', undefined); // Rimuovi il valore dal form
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" label="Caricamento valore parametro..." />
      </div>
    );
  }

  if (valueId && error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card className="max-w-md mx-auto">
          <CardBody>
            <p className="text-danger">Errore nel caricamento del valore: {error.message}</p>
            <Button 
              color="primary" 
              onPress={() => navigate(`/admin/parameters/${parameterId}/values`)}
              className="mt-4"
            >
              Torna alla lista
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (!parameterId) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card className="max-w-md mx-auto">
          <CardBody>
            <p className="text-danger">ID del parametro non specificato.</p>
            <Button 
              color="primary" 
              onPress={() => navigate('/admin/parameters')}
              className="mt-4"
            >
              Torna ai parametri
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate(`/admin/parameters/${parameterId}/values`)}
          aria-label="Torna indietro"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">
            {valueId ? 'Modifica Valore Parametro' : 'Nuovo Valore Parametro'}
          </h1>
          <p className="text-gray-600">Parametro ID: {parameterId}</p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Informazioni Generali */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Informazioni Generali</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Testo Visualizzato */}
              <Controller
                name="testo_visualizzato_ui"
                control={control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    label="Testo Visualizzato"
                    placeholder="Inserisci il testo da visualizzare"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    isRequired
                  />
                )}
              />

              {/* Ordine Visualizzazione */}
              <Controller
                name="ordine_visualizzazione"
                control={control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    value={String(field.value)}
                    onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                    label="Ordine di Visualizzazione"
                    type="number"
                    min="0"
                    placeholder="0"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    description="Ordine in cui verrà mostrato questo valore (0 = primo)"
                  />
                )}
              />
            </div>

            {/* Colore */}
            <Controller
              name="colore"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  value={field.value || ''}
                  label="Colore"
                  placeholder="es. #FF0000 o red"
                  startContent={<SwatchIcon className="h-4 w-4" />}
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                  description="Colore associato al valore (opzionale)"
                />
              )}
            />

            {/* Descrizione */}
            <Controller
              name="descrizione"
              control={control}
              render={({ field, fieldState }) => (
                <Textarea
                  {...field}
                  value={field.value || ''}
                  label="Descrizione"
                  placeholder="Descrizione opzionale del valore"
                  isInvalid={!!fieldState.error}
                  errorMessage={fieldState.error?.message}
                />
              )}
            />
          </CardBody>
        </Card>

        {/* Gestione Immagine */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Immagine</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            {/* Foto Esistente */}
            {existingPhoto && (
              <div className="flex items-center gap-4 p-4 bg-default-50 rounded-lg">
                <Image
                  src={existingPhoto}
                  alt="Foto valore parametro"
                  width={100}
                  height={100}
                  className="rounded-md object-cover"
                />
                <div className="flex-1">
                  <p className="text-sm text-default-600">Immagine attuale</p>
                </div>
                <Button
                  isIconOnly
                  color="danger"
                  variant="light"
                  onPress={handleRemovePhoto}
                  aria-label="Rimuovi foto"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Upload Nuova Foto */}
            <Controller
              name="foto"
              control={control}
              render={({ field, fieldState }) => (
                <div className="space-y-2">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => field.onChange(e.target.files)}
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    startContent={<PhotoIcon className="h-4 w-4" />}
                    description="Formati supportati: JPG, PNG, GIF (max 5MB)"
                  />
                </div>
              )}
            />
          </CardBody>
        </Card>

        {/* Azioni */}
        <Card>
          <CardBody>
            <div className="flex justify-end gap-4">
              <Button
                variant="light"
                onPress={() => navigate(`/admin/parameters/${parameterId}/values`)}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                isDisabled={isSubmitting}
              >
                {valueId ? 'Aggiorna Valore' : 'Crea Valore'}
              </Button>
            </div>
          </CardBody>
        </Card>
      </form>
    </div>
  );
};

export default ValueParameterFormPage;