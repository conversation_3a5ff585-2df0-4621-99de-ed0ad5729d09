import React, { useState, useMemo } from 'react';
import { 
  Button, 
  Input, 
  Select, 
  SelectItem, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Tooltip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Pagination,
  Spinner,
  Card,
  CardBody
} from '@heroui/react';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  UserIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useClienti, useClientiOperations, useClientiStats } from '../hooks/useClienti';
import { ClientiFilters, ClienteListItem } from '../types/clienti';
import { formatDate } from '../utils/dateUtils';

const ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100];

export default function ClientiListPage() {
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  // State per filtri
  const [filters, setFilters] = useState<ClientiFilters>({
    search: '',
    stato: '',
    citta: '',
    sort_by: 'data_creazione',
    sort_order: 'desc',
    page: 1,
    per_page: 25
  });
  
  // State per eliminazione
  const [clienteToDelete, setClienteToDelete] = useState<ClienteListItem | null>(null);
  
  // Hooks
  const { data: clientiData, isLoading, error } = useClienti(filters);
  const { data: stats } = useClientiStats();
  const { delete: deleteCliente, toggleStatus } = useClientiOperations();
  
  // Dati derivati
  const clienti = clientiData?.clienti || [];
  const pagination = clientiData?.pagination;
  
  // Handlers
  const handleFilterChange = (key: keyof ClientiFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset page quando cambiano altri filtri
    }));
  };
  
  const handleSearch = (value: string) => {
    handleFilterChange('search', value);
  };
  
  const handleDeleteClick = (cliente: ClienteListItem) => {
    setClienteToDelete(cliente);
    onOpen();
  };
  
  const handleDeleteConfirm = async () => {
    if (!clienteToDelete) return;
    
    try {
      await deleteCliente.mutateAsync(clienteToDelete.id_cliente);
      toast.success(`Cliente ${clienteToDelete.denominazione} eliminato con successo`);
      onClose();
      setClienteToDelete(null);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Errore durante l\'eliminazione');
    }
  };
  
  const handleToggleStatus = async (cliente: ClienteListItem) => {
    try {
      await toggleStatus.mutateAsync(cliente.id_cliente);
      const action = cliente.attivo ? 'disattivato' : 'attivato';
      toast.success(`Cliente ${cliente.denominazione} ${action} con successo`);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Errore durante il cambio stato');
    }
  };
  
  // Colonne della tabella
  const columns = [
    { key: 'denominazione', label: 'Cliente' },
    { key: 'email', label: 'Email' },
    { key: 'citta', label: 'Città' },
    { key: 'telefono', label: 'Telefono' },
    { key: 'attivo', label: 'Stato' },
    { key: 'data_creazione', label: 'Registrato' },
    { key: 'ultimo_accesso', label: 'Ultimo accesso' },
    { key: 'actions', label: 'Azioni' }
  ];
  
  // Render cell
  const renderCell = (cliente: ClienteListItem, columnKey: string) => {
    switch (columnKey) {
      case 'denominazione':
        return (
          <div className="flex flex-col">
            <span className="font-medium">{cliente.denominazione}</span>
            <span className="text-sm text-gray-500">@{cliente.username}</span>
          </div>
        );
      case 'attivo':
        return (
          <Chip 
            color={cliente.attivo ? 'success' : 'danger'} 
            variant="flat"
            size="sm"
          >
            {cliente.attivo ? 'Attivo' : 'Disattivo'}
          </Chip>
        );
      case 'data_creazione':
        return <span className="text-sm">{formatDate(cliente.data_creazione)}</span>;
      case 'ultimo_accesso':
        return (
          <span className="text-sm">
            {cliente.ultimo_accesso ? formatDate(cliente.ultimo_accesso) : 'Mai'}
          </span>
        );
      case 'actions':
        return (
          <Dropdown>
            <DropdownTrigger>
              <Button isIconOnly size="sm" variant="light">
                <EllipsisVerticalIcon className="h-4 w-4" />
              </Button>
            </DropdownTrigger>
            <DropdownMenu>
              <DropdownItem
                key="view"
                startContent={<EyeIcon className="h-4 w-4" />}
                onPress={() => navigate(`/admin/clienti/${cliente.id_cliente}`)}
              >
                Visualizza
              </DropdownItem>
              <DropdownItem
                key="edit"
                startContent={<PencilIcon className="h-4 w-4" />}
                onPress={() => navigate(`/admin/clienti/${cliente.id_cliente}/edit`)}
              >
                Modifica
              </DropdownItem>
              <DropdownItem
                key="toggle"
                startContent={<UserIcon className="h-4 w-4" />}
                onPress={() => handleToggleStatus(cliente)}
              >
                {cliente.attivo ? 'Disattiva' : 'Attiva'}
              </DropdownItem>
              <DropdownItem
                key="reset-password"
                startContent={<KeyIcon className="h-4 w-4" />}
                onPress={() => navigate(`/admin/clienti/${cliente.id_cliente}/reset-password`)}
              >
                Reset Password
              </DropdownItem>
              <DropdownItem
                key="delete"
                className="text-danger"
                color="danger"
                startContent={<TrashIcon className="h-4 w-4" />}
                onPress={() => handleDeleteClick(cliente)}
              >
                Elimina
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        );
      default:
        return cliente[columnKey as keyof ClienteListItem] || '-';
    }
  };
  
  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          Errore nel caricamento dei clienti: {error.message}
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Gestione Clienti</h1>
          <p className="text-gray-600">Gestisci i clienti del sistema</p>
        </div>
        <Button
          color="primary"
          startContent={<PlusIcon className="h-4 w-4" />}
          onPress={() => navigate('/admin/clienti/new')}
        >
          Nuovo Cliente
        </Button>
      </div>
      
      {/* Statistiche */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total_clienti}</div>
              <div className="text-sm text-gray-600">Totale Clienti</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.clienti_attivi}</div>
              <div className="text-sm text-gray-600">Clienti Attivi</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.nuovi_clienti_ultimo_mese}</div>
              <div className="text-sm text-gray-600">Nuovi (30gg)</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.percentuale_attivi}%</div>
              <div className="text-sm text-gray-600">% Attivi</div>
            </CardBody>
          </Card>
        </div>
      )}
      
      {/* Filtri */}
      <Card>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Cerca clienti..."
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
              startContent={<MagnifyingGlassIcon className="h-4 w-4" />}
              isClearable
              onClear={() => handleSearch('')}
            />
            
            <Select
              placeholder="Stato"
              selectedKeys={filters.stato ? [filters.stato] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                handleFilterChange('stato', value || '');
              }}
            >
              <SelectItem key="">Tutti</SelectItem>
              <SelectItem key="attivo">Attivi</SelectItem>
              <SelectItem key="disattivo">Disattivi</SelectItem>
            </Select>
            
            <Input
              placeholder="Città"
              value={filters.citta || ''}
              onChange={(e) => handleFilterChange('citta', e.target.value)}
              isClearable
              onClear={() => handleFilterChange('citta', '')}
            />
            
            <Select
              placeholder="Ordina per"
              selectedKeys={filters.sort_by ? [filters.sort_by] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                handleFilterChange('sort_by', value);
              }}
            >
              <SelectItem key="data_creazione">Data registrazione</SelectItem>
              <SelectItem key="nome">Nome</SelectItem>
              <SelectItem key="cognome">Cognome</SelectItem>
              <SelectItem key="email">Email</SelectItem>
              <SelectItem key="citta">Città</SelectItem>
              <SelectItem key="ultimo_accesso">Ultimo accesso</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>
      
      {/* Tabella */}
      <Card>
        <CardBody className="p-0">
          <Table 
            aria-label="Tabella clienti"
            isHeaderSticky
            classNames={{
              wrapper: "max-h-[600px]",
            }}
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn key={column.key}>
                  {column.label}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody 
              items={clienti}
              isLoading={isLoading}
              loadingContent={<Spinner />}
              emptyContent="Nessun cliente trovato"
            >
              {(cliente) => (
                <TableRow key={cliente.id_cliente}>
                  {(columnKey) => (
                    <TableCell>
                      {renderCell(cliente, columnKey as string)}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
      
      {/* Paginazione */}
      {pagination && pagination.pages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Mostrando {((pagination.page - 1) * pagination.per_page) + 1} - {Math.min(pagination.page * pagination.per_page, pagination.total)} di {pagination.total} clienti
          </div>
          <Pagination
            total={pagination.pages}
            page={pagination.page}
            onChange={(page) => handleFilterChange('page', page)}
            showControls
          />
        </div>
      )}
      
      {/* Modal conferma eliminazione */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>Conferma eliminazione</ModalHeader>
          <ModalBody>
            <p>
              Sei sicuro di voler eliminare il cliente <strong>{clienteToDelete?.denominazione}</strong>?
            </p>
            <p className="text-sm text-gray-600">
              Questa azione non può essere annullata.
            </p>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Annulla
            </Button>
            <Button 
              color="danger" 
              onPress={handleDeleteConfirm}
              isLoading={deleteCliente.isPending}
            >
              Elimina
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}