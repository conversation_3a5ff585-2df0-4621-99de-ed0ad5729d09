import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useGetDependencyRules, useDeleteDependencyRule, useDuplicateDependencyRule } from '../hooks/useDependencyRules';
import { toast } from 'react-toastify';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Card,
  CardHeader,
  CardBody,
  Chip,
  Spinner,
  Tooltip,
} from '@heroui/react';
import { PlusIcon, PencilIcon, TrashIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';

const DependencyRulesListPage: React.FC = () => {
  const navigate = useNavigate();

  const { data, error, isLoading } = useGetDependencyRules();
  const deleteMutation = useDeleteDependencyRule();
  const duplicateMutation = useDuplicateDependencyRule();

  React.useEffect(() => {
    if (deleteMutation.isSuccess) {
      toast.success('Regola eliminata con successo');
    }
    if (deleteMutation.isError) {
      toast.error(`Errore durante l'eliminazione della regola: ${(deleteMutation.error as Error).message}`);
    }
  }, [deleteMutation.isSuccess, deleteMutation.isError, deleteMutation.error]);

  React.useEffect(() => {
    if (duplicateMutation.isSuccess) {
      toast.success(`Regola duplicata con successo: ${duplicateMutation.data?.nome_regola}`);
    }
    if (duplicateMutation.isError) {
      toast.error(`Errore durante la duplicazione della regola: ${(duplicateMutation.error as Error).message}`);
    }
  }, [duplicateMutation.isSuccess, duplicateMutation.isError, duplicateMutation.error, duplicateMutation.data]);

  const handleDelete = (id: number) => {
    if (window.confirm('Sei sicuro di voler eliminare questa regola di dipendenza complessa?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleDuplicate = (id: number, nomeRegola: string) => {
    if (window.confirm(`Sei sicuro di voler duplicare la regola "${nomeRegola}"?`)) {
      duplicateMutation.mutate(id);
    }
  };

  const columns = [
    { key: 'id', label: 'ID' },
    { key: 'nome_regola', label: 'Nome Regola' },
    { key: 'descrizione', label: 'Descrizione' },
    { key: 'attiva', label: 'Stato' },
    { key: 'condizioni_count', label: 'Condizioni' },
    { key: 'risultati_count', label: 'Risultati' },
    { key: 'logica_combinazione_condizioni', label: 'Logica' },
    { key: 'actions', label: 'Azioni' },
  ];

  const renderCell = (rule: any, columnKey: string) => {
    switch (columnKey) {
      case 'id':
        return <span className="text-bold text-sm">{rule.id}</span>;
      
      case 'nome_regola':
        return (
          <div className="flex flex-col">
            <p className="text-bold text-sm">{rule.nome_regola}</p>
          </div>
        );
      
      case 'descrizione':
        return (
          <div className="max-w-xs">
            {rule.descrizione ? (
              <Tooltip content={rule.descrizione}>
                <p className="text-sm truncate">{rule.descrizione}</p>
              </Tooltip>
            ) : (
              <span className="text-gray-400 text-sm">-</span>
            )}
          </div>
        );
      
      case 'attiva':
        return (
          <Chip
            color={rule.attiva ? 'success' : 'danger'}
            size="sm"
            variant="flat"
          >
            {rule.attiva ? 'Attiva' : 'Inattiva'}
          </Chip>
        );
      
      case 'condizioni_count':
        return (
          <Chip color="primary" size="sm" variant="flat">
            {rule.condizioni?.length || 0}
          </Chip>
        );
      
      case 'risultati_count':
        return (
          <Chip color="secondary" size="sm" variant="flat">
            {rule.risultati?.length || 0}
          </Chip>
        );
      
      case 'logica_combinazione_condizioni':
        return (
          <Chip
            color={rule.logica_combinazione_condizioni === 'AND' ? 'warning' : 'default'}
            size="sm"
            variant="flat"
          >
            {rule.logica_combinazione_condizioni || 'AND'}
          </Chip>
        );
      
      case 'actions':
        return (
          <div className="flex items-center gap-2">
            <Tooltip content="Modifica regola">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="warning"
                onPress={() => navigate(`/admin/dependency-rules/${rule.id}`)}
              >
                <PencilIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
            <Tooltip content="Duplica regola">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="primary"
                onPress={() => handleDuplicate(rule.id, rule.nome_regola)}
                isLoading={duplicateMutation.isPending}
              >
                <DocumentDuplicateIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
            <Tooltip content="Elimina regola" color="danger">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="danger"
                onPress={() => handleDelete(rule.id)}
                isLoading={deleteMutation.isPending}
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            </Tooltip>
          </div>
        );
      
      default:
        return <span className="text-sm">{(rule as any)[columnKey] || '-'}</span>;
    }
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card className="max-w-md mx-auto">
          <CardBody>
            <p className="text-danger">Errore nel caricamento delle regole: {error.message}</p>
            <Button 
              color="primary" 
              onPress={() => window.location.reload()}
              className="mt-4"
            >
              Riprova
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-center w-full">
            <div>
              <h1 className="text-2xl font-bold">Regole di Dipendenza</h1>
              <p className="text-gray-600">Gestisci le regole di dipendenza complesse</p>
            </div>
            <Button 
              color="primary" 
              startContent={<PlusIcon className="h-4 w-4" />}
              onPress={() => navigate('/admin/dependency-rules/new')}
            >
              Crea Nuova Regola
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Table */}
      <Card>
        <CardBody className="p-0">
          <Table
            aria-label="Tabella delle regole di dipendenza"
            className="min-w-full"
            removeWrapper
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn 
                  key={column.key}
                  className={column.key === 'actions' ? 'text-center' : ''}
                >
                  {column.label}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody
              items={data || []}
              isLoading={isLoading}
              loadingContent={
                <div className="flex justify-center items-center py-8">
                  <Spinner size="lg" label="Caricamento regole..." />
                </div>
              }
              emptyContent={
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">Nessuna regola di dipendenza trovata</p>
                  <Button 
                    color="primary" 
                    variant="flat"
                    startContent={<PlusIcon className="h-4 w-4" />}
                    onPress={() => navigate('/admin/dependency-rules/new')}
                  >
                    Crea la prima regola
                  </Button>
                </div>
              }
            >
              {(rule) => (
                <TableRow key={rule.id}>
                  {(columnKey) => (
                    <TableCell>
                      {renderCell(rule, columnKey as string)}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
    </div>
  );
};

export default DependencyRulesListPage;