import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  addToast,
} from '@heroui/react';

import { IParameter } from '../types/parameters';
import { get, del, put } from '../services/api';

const fetchParameters = async (): Promise<IParameter[]> => {
  const response = await get('/admin/parametri');
  return response.data;
};

const deleteParameter = async (id_parametro: number): Promise<void> => {
  await del(`/admin/parametri/${id_parametro}`);
};

const reorderParameters = async (reorderData: { id_parametro: number; ordine_visualizzazione: number }[]): Promise<void> => {
  await put(`/admin/parametri/reorder`, reorderData);
};

const normalizeParametersOrder = async (): Promise<void> => {
  await put(`/admin/parametri/normalize-order`);
};

const ParametersListPage: React.FC = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data, isLoading } = useQuery<IParameter[], Error>({
    queryKey: ['parameters'],
    queryFn: fetchParameters,
  });

  const mutation = useMutation<void, Error, number>({
    mutationFn: deleteParameter,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
      addToast({
        title: 'Successo',
        description: 'Parametro eliminato con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'eliminazione del parametro: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const reorderMutation = useMutation<void, Error, { id_parametro: number; ordine_visualizzazione: number }[]>({
    mutationFn: reorderParameters,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
      addToast({
        title: 'Successo',
        description: 'Ordine aggiornato con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'aggiornamento dell'ordine: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const normalizeMutation = useMutation<void, Error, void>({
    mutationFn: normalizeParametersOrder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
      addToast({
        title: 'Successo',
        description: 'Ordini normalizzati con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante la normalizzazione: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const handleDelete = (id_parametro: number) => {
    if (window.confirm('Sei sicuro di voler eliminare questo parametro?')) {
      mutation.mutate(id_parametro);
    }
  };

  const moveUp = (index: number) => {
    if (!data || data.length === 0 || index <= 0 || index >= data.length) {
      return;
    }
    
    const currentItem = data[index];
    const previousItem = data[index - 1];
    
    if (!currentItem || !previousItem) {
      return;
    }
    
    const currentOrder = currentItem.ordine_visualizzazione;
    const previousOrder = previousItem.ordine_visualizzazione;
    
    if (currentOrder === null || currentOrder === undefined || previousOrder === null || previousOrder === undefined) {
      return;
    }
    
    // Se gli ordini sono uguali, normalizza prima
    if (currentOrder === previousOrder) {
      normalizeMutation.mutate();
      return;
    }
    
    // Prepara i dati per l'aggiornamento scambiando gli ordini
    const reorderData = [
      { id_parametro: currentItem.id_parametro, ordine_visualizzazione: previousOrder },
      { id_parametro: previousItem.id_parametro, ordine_visualizzazione: currentOrder }
    ];
    
    reorderMutation.mutate(reorderData);
  };

  const moveDown = (index: number) => {
    if (!data || data.length === 0 || index < 0 || index >= data.length - 1) {
      return;
    }
    
    const currentItem = data[index];
    const nextItem = data[index + 1];
    
    if (!currentItem || !nextItem) {
      return;
    }
    
    const currentOrder = currentItem.ordine_visualizzazione;
    const nextOrder = nextItem.ordine_visualizzazione;
    
    if (currentOrder === null || currentOrder === undefined || nextOrder === null || nextOrder === undefined) {
      return;
    }
    
    // Se gli ordini sono uguali, normalizza prima
    if (currentOrder === nextOrder) {
      normalizeMutation.mutate();
      return;
    }
    
    // Prepara i dati per l'aggiornamento scambiando gli ordini
    const reorderData = [
      { id_parametro: currentItem.id_parametro, ordine_visualizzazione: nextOrder },
      { id_parametro: nextItem.id_parametro, ordine_visualizzazione: currentOrder }
    ];
    
    reorderMutation.mutate(reorderData);
  };

  const columns = [
    { key: 'ordine_visualizzazione', label: 'Ordine' },
    { key: 'id_parametro', label: 'ID' },
    { key: 'nome_parametro', label: 'Nome' },
    { key: 'tipo_controllo_ui', label: 'Tipo Controllo UI' },
    { key: 'descrizione', label: 'Descrizione' },
    { key: 'attivo', label: 'Attivo' },
    { key: 'reorder', label: 'Riordina' },
    { key: 'actions', label: 'Azioni' },
  ];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Lista Parametri</h1>
      <div className="flex justify-end gap-2 mb-4">
        <Button 
          color="secondary" 
          variant="bordered"
          onPress={() => normalizeMutation.mutate()}
          isLoading={normalizeMutation.isPending}
          isDisabled={normalizeMutation.isPending}
        >
          Normalizza Ordini
        </Button>
        <Button color="primary" onPress={() => navigate('/admin/parameters/new')}>
          Nuovo Parametro
        </Button>
      </div>
      <Table
        aria-label="Tabella dei parametri"
        selectionMode="single"
        className="min-w-full"
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn key={column.key}>{column.label}</TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data || []}
          isLoading={isLoading}
          loadingContent={
            <div className="text-center py-4">Caricamento parametri...</div>
          }
          emptyContent={
            <div className="text-center py-4">Nessun parametro trovato.</div>
          }
        >
          {(param) => {
            const index = data?.findIndex(item => item.id_parametro === param.id_parametro) ?? -1;
            return (
              <TableRow key={param.id_parametro}>
                {(columnKey) => (
                  <TableCell>
                    {columnKey === 'actions' ? (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          color="warning"
                          onPress={() => navigate(`/admin/parameters/${param.id_parametro}`)}
                        >
                          Modifica
                        </Button>
                        <Button
                          size="sm"
                          color="default"
                          onPress={() => navigate(`/admin/parameters/${param.id_parametro}/values`)}
                        >
                          Valori
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          onPress={() => handleDelete(param.id_parametro)}
                        >
                          Elimina
                        </Button>
                      </div>
                    ) : columnKey === 'reorder' ? (
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="light"
                          isIconOnly
                          onPress={() => moveUp(index)}
                          isDisabled={index === 0 || reorderMutation.isPending || normalizeMutation.isPending}
                        >
                          ↑
                        </Button>
                        <Button
                          size="sm"
                          variant="light"
                          isIconOnly
                          onPress={() => moveDown(index)}
                          isDisabled={index === (data?.length || 0) - 1 || reorderMutation.isPending || normalizeMutation.isPending}
                        >
                          ↓
                        </Button>
                      </div>
                    ) : columnKey === 'attivo' ? (
                      param.attivo ? 'Sì' : 'No'
                    ) : (
                      (param as any)[columnKey] || '-'
                    )}
                  </TableCell>
                )}
              </TableRow>
            );
          }}
        </TableBody>
      </Table>
    </div>
  );
};

export default ParametersListPage;