import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  CardHeader,
  CardBody,
  Button,
  Spinner,
  Chip,
  Image,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Divider,
} from '@heroui/react';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  TrashIcon,
  PhotoIcon,
  SparklesIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { useGetProduct, useDeleteProduct } from '../hooks/useProducts';
import Breadcrumb from '../components/Breadcrumb';
import HelpTooltip from '../components/HelpTooltip';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const { 
    data: productData, 
    isLoading, 
    error 
  } = useGetProduct(id ? parseInt(id) : 0);
  
  const deleteMutation = useDeleteProduct();

  const handleDelete = () => {
    if (!productData?.product) return;
    
    if (window.confirm(`Sei sicuro di voler eliminare il prodotto "${productData.product.nome_prodotto}"?`)) {
      deleteMutation.mutate(productData.product.id, {
        onSuccess: (data) => {
          toast.success(data.message);
          navigate('/admin/products');
        },
        onError: (error) => {
          toast.error(`Errore durante l'eliminazione: ${error.message}`);
        },
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" label="Caricamento prodotto..." />
      </div>
    );
  }

  if (error || !productData?.product) {
    return (
      <Card className="max-w-md mx-auto">
        <CardBody className="text-center">
          <h3 className="text-lg font-semibold text-danger mb-2">
            Prodotto non trovato
          </h3>
          <p className="text-gray-600 mb-4">
            {(error as Error)?.message || 'Il prodotto richiesto non esiste'}
          </p>
          <Button 
            color="primary" 
            onPress={() => navigate('/admin/products')}
          >
            Torna alla Lista
          </Button>
        </CardBody>
      </Card>
    );
  }

  const product = productData.product;

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb
        items={[
          { label: 'Prodotti', href: '/admin/products' },
          { label: product.nome_prodotto, current: true },
        ]}
      />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            isIconOnly
            variant="light"
            onPress={() => navigate('/admin/products')}
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {product.nome_prodotto}
            </h1>
            <p className="text-gray-600 mt-1">
              Dettagli prodotto e regole associate
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            color="warning"
            variant="light"
            startContent={<PencilIcon className="h-4 w-4" />}
            onPress={() => navigate(`/admin/products/${product.id}/edit`)}
          >
            Modifica
          </Button>
          <Button
            color="danger"
            variant="light"
            startContent={<TrashIcon className="h-4 w-4" />}
            onPress={handleDelete}
            isLoading={deleteMutation.isPending}
          >
            Elimina
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informazioni Prodotto */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <Cog6ToothIcon className="h-5 w-5" />
                Informazioni Prodotto
              </h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Nome Prodotto</label>
                  <p className="text-lg font-semibold">{product.nome_prodotto}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Ordine Visualizzazione</label>
                  <p className="text-lg">{product.ordine_visualizzazione}</p>
                </div>
              </div>

              {product.descrizione && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Descrizione</label>
                  <p className="text-gray-800 mt-1">{product.descrizione}</p>
                </div>
              )}

              {product.colore && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Colore</label>
                  <div className="flex items-center gap-2 mt-1">
                    <div 
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: product.colore }}
                    />
                    <span>{product.colore}</span>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <label className="font-medium">Data Creazione</label>
                  <p>{product.data_creazione ? new Date(product.data_creazione).toLocaleString('it-IT') : 'N/A'}</p>
                </div>
                <div>
                  <label className="font-medium">Ultima Modifica</label>
                  <p>{product.data_modifica ? new Date(product.data_modifica).toLocaleString('it-IT') : 'N/A'}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Regole Associate */}
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <SparklesIcon className="h-5 w-5" />
                Regole di Dipendenza
                <Chip size="sm" color="primary" variant="flat">
                  {product.regole_associate.length}
                </Chip>
              </h2>
            </CardHeader>
            <CardBody>
              {product.regole_associate.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <SparklesIcon className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                  <p>Nessuna regola associata</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {product.regole_associate.map((regola, index) => (
                    <div key={regola.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold">{regola.nome_regola}</h3>
                        <Chip 
                          size="sm" 
                          color={regola.attiva ? "success" : "warning"}
                          variant="flat"
                        >
                          {regola.attiva ? "Attiva" : "Inattiva"}
                        </Chip>
                      </div>
                      
                      {regola.descrizione && (
                        <p className="text-sm text-gray-600 mb-3">{regola.descrizione}</p>
                      )}

                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm font-medium text-gray-700 mb-2">Prerequisiti:</p>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(regola.prerequisiti).map(([nome, prerequisito]) => (
                            <Chip key={nome} size="sm" color="primary" variant="flat">
                              {nome}: {prerequisito.nome_valore}
                            </Chip>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Foto Prodotto */}
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold">Foto Prodotto</h2>
            </CardHeader>
            <CardBody>
              {product.foto ? (
                <Image
                  src={`/api/${product.foto}`}
                  alt={product.nome_prodotto}
                  className="w-full rounded-lg object-cover"
                  fallback={
                    <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                      <PhotoIcon className="h-16 w-16 text-gray-400" />
                    </div>
                  }
                />
              ) : (
                <div className="w-full h-48 bg-gray-100 rounded-lg flex flex-col items-center justify-center text-gray-500">
                  <PhotoIcon className="h-16 w-16 mb-2" />
                  <p className="text-sm">Nessuna foto</p>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Statistiche */}
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold">Statistiche</h2>
            </CardHeader>
            <CardBody className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Regole Totali</span>
                <Chip size="sm" color="primary" variant="flat">
                  {product.regole_associate.length}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Regole Attive</span>
                <Chip size="sm" color="success" variant="flat">
                  {product.regole_associate.filter(r => r.attiva).length}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Prerequisiti</span>
                <Chip size="sm" color="default" variant="flat">
                  {product.regole_associate.length > 0 
                    ? Object.keys(product.regole_associate[0].prerequisiti).length
                    : 0
                  }
                </Chip>
              </div>

              <Divider />
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">ID Prodotto</span>
                <span className="text-sm font-mono">{product.id}</span>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;