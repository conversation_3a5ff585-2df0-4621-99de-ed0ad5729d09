import React, { useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  Switch,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Spinner
} from '@heroui/react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useCliente, useClientiOperations } from '../hooks/useClienti';
import { 
  clienteCreateSchema, 
  clienteUpdateSchema, 
  ClienteCreateFormData, 
  ClienteUpdateFormData,
  cleanFormData 
} from '../schemas/clienteSchemas';
import { MODALITA_PAGAMENTO_OPTIONS, PROVINCE_ITALIANE } from '../types/clienti';

export default function ClienteFormPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const clienteId = id ? parseInt(id) : undefined;
  
  // Hooks
  const { data: cliente, isLoading: isLoadingCliente } = useCliente(clienteId!);
  const { create, update } = useClientiOperations();
  
  // Form setup
  const schema = isEditMode ? clienteUpdateSchema : clienteCreateSchema;
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch
  } = useForm<ClienteCreateFormData | ClienteUpdateFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      nome: '',
      cognome: '',
      ragione_sociale: '',
      partita_iva: '',
      codice_fiscale: '',
      telefono: '',
      cellulare: '',
      indirizzo: '',
      citta: '',
      cap: '',
      provincia: '',
      nazione: 'Italia',
      iban: '',
      modalita_pagamento: '',
      codice_sdi: '',
      indirizzo_pec: '',
      note: '',
      attivo: true,
    }
  });
  
  // Popolamento form in modalità edit
  useEffect(() => {
    if (isEditMode && cliente) {
      reset({
        username: cliente.username,
        email: cliente.email,
        nome: cliente.nome,
        cognome: cliente.cognome,
        ragione_sociale: cliente.ragione_sociale || '',
        partita_iva: cliente.partita_iva || '',
        codice_fiscale: cliente.codice_fiscale || '',
        telefono: cliente.telefono || '',
        cellulare: cliente.cellulare || '',
        indirizzo: cliente.indirizzo || '',
        citta: cliente.citta || '',
        cap: cliente.cap || '',
        provincia: cliente.provincia || '',
        nazione: cliente.nazione || 'Italia',
        iban: cliente.iban || '',
        modalita_pagamento: cliente.modalita_pagamento || '',
        codice_sdi: cliente.codice_sdi || '',
        indirizzo_pec: cliente.indirizzo_pec || '',
        note: cliente.note || '',
        attivo: cliente.attivo,
      });
    }
  }, [cliente, isEditMode, reset]);
  
  // Submit handler
  const onSubmit = async (data: ClienteCreateFormData | ClienteUpdateFormData) => {
    try {
      const cleanedData = cleanFormData(data);
      
      if (isEditMode && clienteId) {
        await update.mutateAsync({ 
          id: clienteId, 
          data: cleanedData as ClienteUpdateFormData 
        });
        toast.success('Cliente aggiornato con successo');
      } else {
        await create.mutateAsync(cleanedData as ClienteCreateFormData);
        toast.success('Cliente creato con successo');
      }
      
      navigate('/admin/clienti');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Errore durante il salvataggio';
      toast.error(errorMessage);
      
      // Gestione errori di validazione dal backend
      if (error.response?.data?.errors) {
        console.error('Errori di validazione:', error.response.data.errors);
      }
    }
  };
  
  if (isEditMode && isLoadingCliente) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate('/admin/clienti')}
        >
          <ArrowLeftIcon className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {isEditMode ? 'Modifica Cliente' : 'Nuovo Cliente'}
          </h1>
          <p className="text-gray-600">
            {isEditMode 
              ? `Modifica i dati del cliente ${cliente?.denominazione}`
              : 'Inserisci i dati del nuovo cliente'
            }
          </p>
        </div>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Dati di accesso */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Dati di Accesso</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="username"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Username"
                    placeholder="Inserisci username"
                    isRequired
                    isInvalid={!!errors.username}
                    errorMessage={errors.username?.message}
                  />
                )}
              />
              
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="email"
                    label="Email"
                    placeholder="Inserisci email"
                    isRequired
                    isInvalid={!!errors.email}
                    errorMessage={errors.email?.message}
                  />
                )}
              />
            </div>
            
            {!isEditMode && (
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="password"
                    label="Password"
                    placeholder="Inserisci password"
                    isRequired
                    isInvalid={!!errors.password}
                    errorMessage={errors.password?.message}
                  />
                )}
              />
            )}
            
            <Controller
              name="attivo"
              control={control}
              render={({ field }) => (
                <Switch
                  isSelected={field.value}
                  onValueChange={field.onChange}
                >
                  Account attivo
                </Switch>
              )}
            />
          </CardBody>
        </Card>
        
        {/* Dati anagrafici */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Dati Anagrafici</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="nome"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Nome"
                    placeholder="Inserisci nome"
                    isRequired
                    isInvalid={!!errors.nome}
                    errorMessage={errors.nome?.message}
                  />
                )}
              />
              
              <Controller
                name="cognome"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Cognome"
                    placeholder="Inserisci cognome"
                    isRequired
                    isInvalid={!!errors.cognome}
                    errorMessage={errors.cognome?.message}
                  />
                )}
              />
            </div>
            
            <Controller
              name="ragione_sociale"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="Ragione Sociale"
                  placeholder="Inserisci ragione sociale (opzionale)"
                  isInvalid={!!errors.ragione_sociale}
                  errorMessage={errors.ragione_sociale?.message}
                />
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="partita_iva"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Partita IVA"
                    placeholder="12345678901"
                    maxLength={11}
                    isInvalid={!!errors.partita_iva}
                    errorMessage={errors.partita_iva?.message}
                  />
                )}
              />
              
              <Controller
                name="codice_fiscale"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Codice Fiscale"
                    placeholder="****************"
                    maxLength={16}
                    isInvalid={!!errors.codice_fiscale}
                    errorMessage={errors.codice_fiscale?.message}
                    onValueChange={(value) => field.onChange(value.toUpperCase())}
                  />
                )}
              />
            </div>
          </CardBody>
        </Card>
        
        {/* Contatti */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Contatti</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="telefono"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Telefono"
                    placeholder="02-1234567"
                    isInvalid={!!errors.telefono}
                    errorMessage={errors.telefono?.message}
                  />
                )}
              />
              
              <Controller
                name="cellulare"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Cellulare"
                    placeholder="333-1234567"
                    isInvalid={!!errors.cellulare}
                    errorMessage={errors.cellulare?.message}
                  />
                )}
              />
            </div>
            
            <Controller
              name="indirizzo"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="Indirizzo"
                  placeholder="Via Roma 123"
                  isInvalid={!!errors.indirizzo}
                  errorMessage={errors.indirizzo?.message}
                />
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Controller
                name="citta"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Città"
                    placeholder="Milano"
                    isInvalid={!!errors.citta}
                    errorMessage={errors.citta?.message}
                  />
                )}
              />
              
              <Controller
                name="cap"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="CAP"
                    placeholder="20100"
                    maxLength={5}
                    isInvalid={!!errors.cap}
                    errorMessage={errors.cap?.message}
                  />
                )}
              />
              
              <Controller
                name="provincia"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label="Provincia"
                    placeholder="Seleziona"
                    selectedKeys={field.value ? [field.value] : []}
                    onSelectionChange={(keys) => {
                      const value = Array.from(keys)[0] as string;
                      field.onChange(value || '');
                    }}
                    isInvalid={!!errors.provincia}
                    errorMessage={errors.provincia?.message}
                  >
                    {PROVINCE_ITALIANE.map((provincia) => (
                      <SelectItem key={provincia} value={provincia}>
                        {provincia}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
              
              <Controller
                name="nazione"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Nazione"
                    placeholder="Italia"
                    isInvalid={!!errors.nazione}
                    errorMessage={errors.nazione?.message}
                  />
                )}
              />
            </div>
          </CardBody>
        </Card>
        
        {/* Dati bancari e pagamento */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Dati Bancari e Pagamento</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <Controller
              name="iban"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label="IBAN"
                  placeholder="IT60 X054 2811 1010 0000 0123 456"
                  isInvalid={!!errors.iban}
                  errorMessage={errors.iban?.message}
                  onValueChange={(value) => field.onChange(value.toUpperCase())}
                />
              )}
            />
            
            <Controller
              name="modalita_pagamento"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  label="Modalità di Pagamento"
                  placeholder="Seleziona modalità"
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0] as string;
                    field.onChange(value || '');
                  }}
                  isInvalid={!!errors.modalita_pagamento}
                  errorMessage={errors.modalita_pagamento?.message}
                >
                  {MODALITA_PAGAMENTO_OPTIONS.map((modalita) => (
                    <SelectItem key={modalita} value={modalita}>
                      {modalita}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          </CardBody>
        </Card>
        
        {/* Fatturazione elettronica */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Fatturazione Elettronica</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="codice_sdi"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Codice SDI"
                    placeholder="ABCDEFG"
                    maxLength={7}
                    isInvalid={!!errors.codice_sdi}
                    errorMessage={errors.codice_sdi?.message}
                    onValueChange={(value) => field.onChange(value.toUpperCase())}
                  />
                )}
              />
              
              <Controller
                name="indirizzo_pec"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="email"
                    label="Indirizzo PEC"
                    placeholder="<EMAIL>"
                    isInvalid={!!errors.indirizzo_pec}
                    errorMessage={errors.indirizzo_pec?.message}
                  />
                )}
              />
            </div>
          </CardBody>
        </Card>
        
        {/* Note */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Note</h3>
          </CardHeader>
          <CardBody>
            <Controller
              name="note"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  label="Note"
                  placeholder="Note aggiuntive sul cliente..."
                  maxRows={4}
                  isInvalid={!!errors.note}
                  errorMessage={errors.note?.message}
                />
              )}
            />
          </CardBody>
        </Card>
        
        {/* Azioni */}
        <div className="flex justify-end gap-4">
          <Button
            variant="light"
            onPress={() => navigate('/admin/clienti')}
            isDisabled={isSubmitting}
          >
            Annulla
          </Button>
          <Button
            type="submit"
            color="primary"
            isLoading={isSubmitting}
          >
            {isEditMode ? 'Aggiorna Cliente' : 'Crea Cliente'}
          </Button>
        </div>
      </form>
    </div>
  );
}