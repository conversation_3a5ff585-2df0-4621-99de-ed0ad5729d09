import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  addToast,
} from '@heroui/react';
import { IValueParameter } from '../types/value-parameters';
import { IParameter } from '../types/parameters';
import { get, del, put, getParameterById } from '../services/api';

const fetchValueParameters = async (parameterId: string | undefined): Promise<IValueParameter[]> => {
  if (!parameterId) {
    throw new Error('ID del parametro non fornito');
  }
  const response = await get(`/admin/parametri/${parameterId}/valori-for-select`);
  
  return response.data.map((item: any) => ({
    id: item.id_valore_parametro,
    valore: item.testo_visualizzato_ui,
    parametro_id: parseInt(parameterId), 
    ordine_visualizzazione: item.ordine_visualizzazione,
    data_creazione: item.data_creazione || new Date().toISOString(),
    data_ultima_modifica: item.data_modifica || new Date().toISOString(),
    foto: item.foto,
    colore: item.colore,
    descrizione: item.descrizione,
  }));
};

const deleteValueParameter = async (valueId: number): Promise<void> => {
  await del(`/admin/valoriparametro/${valueId}`);
};

const reorderValueParameters = async (parameterId: string, reorderData: { id_valore_parametro: number; ordine_visualizzazione: number }[]): Promise<void> => {
  await put(`/admin/parametri/${parameterId}/valori/reorder`, reorderData);
};

const normalizeValueParametersOrder = async (parameterId: string): Promise<void> => {
  await put(`/admin/parametri/${parameterId}/valori/normalize-order`);
};

const ValueParametersListPage: React.FC = () => {
  const { parameterId } = useParams<{ parameterId: string }>();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Nuova query per recuperare i dettagli del parametro
  const { data: parameterData, isLoading: isLoadingParameter, error: parameterError } = useQuery<IParameter, Error>({
    queryKey: ['parameter', parameterId],
    queryFn: () => getParameterById(Number(parameterId)).then(res => res.data),
    enabled: !!parameterId,
  });

  const { data, error, isLoading } = useQuery<IValueParameter[], Error>({
    queryKey: ['valueParameters', parameterId],
    queryFn: () => fetchValueParameters(parameterId),
    enabled: !!parameterId, // Abilita la query solo se parameterId è presente
  });


  const mutation = useMutation<void, Error, number>({
    mutationFn: deleteValueParameter,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['valueParameters', parameterId] });
      addToast({
        title: 'Successo',
        description: 'Valore parametro eliminato con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'eliminazione del valore del parametro: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const reorderMutation = useMutation<void, Error, { id_valore_parametro: number; ordine_visualizzazione: number }[]>({
    mutationFn: (reorderData) => reorderValueParameters(parameterId!, reorderData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['valueParameters', parameterId] });
      addToast({
        title: 'Successo',
        description: 'Ordine aggiornato con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante l'aggiornamento dell'ordine: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const normalizeMutation = useMutation<void, Error, void>({
    mutationFn: () => normalizeValueParametersOrder(parameterId!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['valueParameters', parameterId] });
      addToast({
        title: 'Successo',
        description: 'Ordini normalizzati con successo',
        color: 'success',
      });
    },
    onError: (err) => {
      addToast({
        title: 'Errore',
        description: `Errore durante la normalizzazione: ${err.message}`,
        color: 'danger',
      });
    },
  });

  const handleDelete = (valueId: number) => {
    if (window.confirm('Sei sicuro di voler eliminare questo valore di parametro?')) {
      if (parameterId) {
        mutation.mutate(valueId);
      }
    }
  };

  const moveUp = (index: number) => {
    if (!data || data.length === 0 || index <= 0 || index >= data.length) {
      return;
    }
    
    const currentItem = data[index];
    const previousItem = data[index - 1];
    
    if (!currentItem || !previousItem) {
      return;
    }
    
    const currentOrder = currentItem.ordine_visualizzazione;
    const previousOrder = previousItem.ordine_visualizzazione;
    
    if (currentOrder === null || currentOrder === undefined || previousOrder === null || previousOrder === undefined) {
      return;
    }
    
    // Se gli ordini sono uguali, normalizza prima
    if (currentOrder === previousOrder) {
      normalizeMutation.mutate();
      return;
    }
    
    // Prepara i dati per l'aggiornamento scambiando gli ordini
    const reorderData = [
      { id_valore_parametro: currentItem.id, ordine_visualizzazione: previousOrder },
      { id_valore_parametro: previousItem.id, ordine_visualizzazione: currentOrder }
    ];
    
    reorderMutation.mutate(reorderData);
  };

  const moveDown = (index: number) => {
    if (!data || data.length === 0 || index < 0 || index >= data.length - 1) {
      return;
    }
    
    const currentItem = data[index];
    const nextItem = data[index + 1];
    
    if (!currentItem || !nextItem) {
      return;
    }
    
    const currentOrder = currentItem.ordine_visualizzazione;
    const nextOrder = nextItem.ordine_visualizzazione;
    
    if (currentOrder === null || currentOrder === undefined || nextOrder === null || nextOrder === undefined) {
      return;
    }
    
    // Se gli ordini sono uguali, normalizza prima
    if (currentOrder === nextOrder) {
      normalizeMutation.mutate();
      return;
    }
    
    // Prepara i dati per l'aggiornamento scambiando gli ordini
    const reorderData = [
      { id_valore_parametro: currentItem.id, ordine_visualizzazione: nextOrder },
      { id_valore_parametro: nextItem.id, ordine_visualizzazione: currentOrder }
    ];
    
    reorderMutation.mutate(reorderData);
  };

  if (isLoading || isLoadingParameter) return <div>Caricamento valori parametri...</div>;
  if (error) return <div>Errore durante il caricamento dei valori dei parametri: {error.message}</div>;
  if (parameterError) return <div>Errore durante il caricamento del parametro: {parameterError.message}</div>;
  if (!parameterId) return <div>ID del parametro non specificato.</div>;

  const columns = [
    { key: 'ordine_visualizzazione', label: 'Ordine' },
    { key: 'id', label: 'ID' },
    { key: 'valore', label: 'Valore' },
    { key: 'foto', label: 'Foto' },
    { key: 'colore', label: 'Colore' },
    { key: 'descrizione', label: 'Descrizione' },
    { key: 'reorder', label: 'Riordina' },
    { key: 'actions', label: 'Azioni' },
  ];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Valori per Parametro: {parameterData?.nome_parametro || parameterId}</h1>
      <div className="flex justify-end gap-2 mb-4">
        <Button 
          color="secondary" 
          variant="bordered"
          onPress={() => normalizeMutation.mutate()}
          isLoading={normalizeMutation.isPending}
          isDisabled={normalizeMutation.isPending}
        >
          Normalizza Ordini
        </Button>
        <Button color="primary" onPress={() => navigate(`/admin/parameters/${parameterId}/values/new`)}>
          Nuovo Valore Parametro
        </Button>
      </div>
      <Table
        aria-label="Tabella dei valori dei parametri"
        selectionMode="single"
        className="min-w-full"
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn key={column.key}>{column.label}</TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data || []}
          isLoading={isLoading}
          loadingContent={
            <div className="text-center py-4">Caricamento valori parametri...</div>
          }
          emptyContent={
            <div className="text-center py-4">Nessun valore parametro trovato per questo parametro.</div>
          }
        >
          {(valueParam) => {
            const index = data?.findIndex(item => item.id === valueParam.id) ?? -1;
            return (
              <TableRow key={valueParam.id}>
                {(columnKey) => (
                  <TableCell>
                    {columnKey === 'actions' ? (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          color="warning"
                          onPress={() => navigate(`/admin/parameters/${parameterId}/values/${valueParam.id}`)}
                        >
                          Modifica
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          onPress={() => handleDelete(valueParam.id)}
                        >
                          Elimina
                        </Button>
                      </div>
                    ) : columnKey === 'reorder' ? (
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="light"
                          isIconOnly
                          onPress={() => moveUp(index)}
                          isDisabled={index === 0 || reorderMutation.isPending || normalizeMutation.isPending}
                          aria-label="Sposta su"
                        >
                          ↑
                        </Button>
                        <Button
                          size="sm"
                          variant="light"
                          isIconOnly
                          onPress={() => moveDown(index)}
                          isDisabled={index === (data?.length || 0) - 1 || reorderMutation.isPending || normalizeMutation.isPending}
                          aria-label="Sposta giù"
                        >
                          ↓
                        </Button>
                      </div>
                    ) : columnKey === 'foto' ? (
                      valueParam.foto ? 'Sì' : 'No'
                    ) : (
                      (valueParam as any)[columnKey] || '-'
                    )}
                  </TableCell>
                )}
              </TableRow>
            );
          }}
        </TableBody>
      </Table>
    </div>
  );
};

export default ValueParametersListPage;