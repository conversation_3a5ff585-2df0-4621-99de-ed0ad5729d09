import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// baseURL per l'API del backend
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://dea3d-backend.prismanet.com/api';

const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    // Rimosso Content-Type predefinito per permettere ad Axios di gestirlo automaticamente con FormData
  },
});

// Variabile per tenere traccia se una richiesta di refresh è già in corso
let isRefreshing = false;
// Coda di richieste in attesa del nuovo token
let failedQueue: { resolve: (value?: unknown) => void; reject: (reason?: any) => void; }[] = [];

const processQueue = (error: AxiosError | null, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Funzione per inizializzare gli interceptor con la funzione di logout
export const setupInterceptors = (logoutFn: () => void) => {
  // Interceptor per aggiungere il token JWT a tutte le richieste
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('jwt_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Interceptor per gestire le risposte e il refresh del token
  api.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config;

      if (error.response?.status === 401 && (error.response.data as any)?.msg === 'Token has expired' && originalRequest && originalRequest.url !== '/auth/login' && originalRequest.url !== '/auth/refresh') {
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return api(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        isRefreshing = true;

        try {
          const refreshToken = localStorage.getItem('refresh_token');
          if (!refreshToken) {
            console.error('No refresh token found, logging out.');
            logoutFn(); // Effettua il logout
            return Promise.reject(error);
          }

          const response = await axios.post<{ access_token: string }>(`${API_BASE_URL}/auth/refresh`, {}, {
            headers: {
              Authorization: `Bearer ${refreshToken}`
            }
          });

          const newAccessToken = response.data.access_token;
          localStorage.setItem('jwt_token', newAccessToken);

          isRefreshing = false;
          processQueue(null, newAccessToken);

          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          return api(originalRequest);

        } catch (refreshError: any) {
          isRefreshing = false;
          processQueue(refreshError);

          console.error('Token refresh failed:', refreshError);
          logoutFn(); // Effettua il logout
          return Promise.reject(refreshError);
        }
      }

      return Promise.reject(error);
    }
  );
};

// Le funzioni di utility per le richieste API e l'istanza 'api' devono essere esportate
// al di fuori della funzione setupInterceptors.

/**
 * Funzione di utility per le richieste GET tipizzate.
 * @param url L'URL dell'endpoint.
 * @returns Una Promise con i dati della risposta.
 */
export const get = async <T = any, R = AxiosResponse<T>>(url: string): Promise<R> => {
  return api.get<T, R>(url);
};

/**
 * Funzione di utility per le richieste POST tipizzate.
 * @param url L'URL dell'endpoint.
 * @param data I dati da inviare nel corpo della richiesta.
 * @returns Una Promise con i dati della risposta.
 */
export const post = async <T = any, D = any, R = AxiosResponse<T>>(url: string, data?: D): Promise<R> => {
  return api.post<T, R>(url, data);
};

/**
 * Funzione di utility per le richieste PUT tipizzate.
 * @param url L'URL dell'endpoint.
 * @param data I dati da inviare nel corpo della richiesta.
 * @returns Una Promise con i dati della risposta.
 */
export const put = async <T = any, D = any, R = AxiosResponse<T>>(url: string, data?: D): Promise<R> => {
  return api.put<T, R>(url, data);
};

/**
 * Funzione di utility per le richieste DELETE tipizzate.
 * @param url L'URL dell'endpoint.
 * @returns Una Promise con i dati della risposta.
 */
export const del = async <T = any, R = AxiosResponse<T>>(url: string): Promise<R> => {
  return api.delete<T, R>(url);
};

export default api;

/**
 * Funzione di utility per le richieste GET tipizzate per un singolo parametro.
 * @param id L'ID del parametro da recuperare.
 * @returns Una Promise con i dati del parametro.
 */
export const getParameterById = async <T = any, R = AxiosResponse<T>>(id: number): Promise<R> => {
  return api.get<T, R>(`/admin/parametri/${id}`);
};

// API endpoints per clienti
export const clientiApi = {
  // Lista clienti con filtri
  getClienti: (params?: Record<string, any>) => 
    api.get('/admin/clienti', { params }),
  
  // Dettagli cliente singolo
  getCliente: (id: number) => 
    api.get(`/admin/clienti/${id}`),
  
  // Crea nuovo cliente
  createCliente: (data: any) => 
    api.post('/admin/clienti', data),
  
  // Aggiorna cliente
  updateCliente: (id: number, data: any) => 
    api.put(`/admin/clienti/${id}`, data),
  
  // Elimina cliente
  deleteCliente: (id: number) => 
    api.delete(`/admin/clienti/${id}`),
  
  // Toggle status cliente
  toggleClienteStatus: (id: number) => 
    api.put(`/admin/clienti/${id}/toggle-status`),
  
  // Reset password cliente
  resetClientePassword: (id: number, newPassword: string) => 
    api.put(`/admin/clienti/${id}/reset-password`, { new_password: newPassword }),
  
  // Statistiche clienti
  getClientiStats: () => 
    api.get('/admin/clienti/stats'),
};
