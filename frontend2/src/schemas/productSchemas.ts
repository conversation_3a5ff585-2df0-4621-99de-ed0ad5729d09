import { z } from 'zod';

// Schema per la creazione/modifica di un prodotto
export const ProductFormSchema = z.object({
  nome_prodotto: z
    .string()
    .min(1, 'Il nome del prodotto è obbligatorio')
    .max(255, 'Il nome del prodotto non può superare i 255 caratteri'),
  
  descrizione: z
    .string()
    .max(1000, 'La descrizione non può superare i 1000 caratteri')
    .optional(),
  
  colore: z
    .string()
    .max(50, 'Il colore non può superare i 50 caratteri')
    .optional(),
  
  ordine_visualizzazione: z
    .number()
    .int('L\'ordine deve essere un numero intero')
    .min(0, 'L\'ordine non può essere negativo')
    .optional(),
  
  prerequisiti: z.object({
    id_lavorazione: z
      .union([z.string(), z.number()])
      .transform(val => {
        console.log('Zod transform id_lavorazione (before):', val, typeof val);
        const num = Number(val);
        console.log('Zod transform id_lavorazione (after):', num, typeof num);
        return num;
      })
      .pipe(
        z.number()
          .int('L\'ID lavorazione deve essere un numero intero')
          .min(0, 'ID lavorazione non valido')
      ),
    
    id_tecniche: z
      .array(z.number().int().min(1))
      .min(1, 'Seleziona almeno una tecnica')
      .refine((arr) => arr.every(id => id > 0), 'Tutti gli ID tecnica devono essere validi'),
    
    id_tipologia_impianto: z
      .union([z.string(), z.number()])
      .transform(val => {
        console.log('Zod transform id_tipologia_impianto (before):', val, typeof val);
        const num = Number(val);
        console.log('Zod transform id_tipologia_impianto (after):', num, typeof num);
        return num;
      })
      .pipe(
        z.number()
          .int('L\'ID tipologia impianto deve essere un numero intero')
          .min(1, 'Seleziona una tipologia impianto')
      ),
    
    id_connessione: z
      .union([z.string(), z.number()])
      .transform(val => {
        console.log('Zod transform id_connessione (before):', val, typeof val);
        const num = Number(val);
        console.log('Zod transform id_connessione (after):', num, typeof num);
        return num;
      })
      .pipe(
        z.number()
          .int('L\'ID connessione deve essere un numero intero')
          .min(1, 'Seleziona una connessione')
      ),
  }).refine((data) => {
    // Validazione personalizzata: tutti i prerequisiti devono essere selezionati
    console.log('Zod refine check:', data);
    return data.id_tecniche.length > 0 && data.id_tipologia_impianto > 0 && data.id_connessione > 0;
  }, {
    message: 'Tutti i prerequisiti devono essere selezionati',
    path: ['id_tecniche'], // Mostra l'errore sul primo campo
  }),
  
  foto: z
    .any()
    .optional()
    .refine((files) => {
      if (!files || files.length === 0) return true; // Foto opzionale
      const file = files[0];
      return file.size <= 5 * 1024 * 1024; // Max 5MB
    }, 'La foto non può superare i 5MB')
    .refine((files) => {
      if (!files || files.length === 0) return true;
      const file = files[0];
      return ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type);
    }, 'Formato foto non supportato. Usa JPG, PNG o WebP'),
});

// Schema per i filtri della lista prodotti
export const ProductFiltersSchema = z.object({
  search: z.string().optional(),
  tecnica: z.number().optional(),
  tipologia_impianto: z.number().optional(),
  connessione: z.number().optional(),
  attiva: z.boolean().optional(),
});

// Tipi derivati dagli schemi
export type ProductFormData = z.infer<typeof ProductFormSchema>;
export type ProductFiltersData = z.infer<typeof ProductFiltersSchema>;