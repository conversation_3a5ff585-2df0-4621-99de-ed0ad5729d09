import { z } from 'zod';

// Regex per validazioni italiane
const PARTITA_IVA_REGEX = /^\d{11}$/;
const CODICE_FISCALE_REGEX = /^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/;
const CAP_REGEX = /^\d{5}$/;
const IBAN_REGEX = /^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/;
const CODICE_SDI_REGEX = /^[A-Z0-9]{7}$/;

// Schema base per campi comuni
const baseClienteFields = {
  nome: z.string()
    .min(2, 'Il nome deve essere di almeno 2 caratteri')
    .max(100, 'Il nome non può superare i 100 caratteri'),
  
  cognome: z.string()
    .min(2, 'Il cognome deve essere di almeno 2 caratteri')
    .max(100, 'Il cognome non può superare i 100 caratteri'),
  
  ragione_sociale: z.string()
    .max(255, 'La ragione sociale non può superare i 255 caratteri')
    .optional()
    .or(z.literal('')),
  
  partita_iva: z.string()
    .regex(PARTITA_IVA_REGEX, 'La partita IVA deve essere di 11 cifre')
    .optional()
    .or(z.literal('')),
  
  codice_fiscale: z.string()
    .regex(CODICE_FISCALE_REGEX, 'Formato codice fiscale non valido')
    .optional()
    .or(z.literal('')),
  
  telefono: z.string()
    .max(20, 'Il telefono non può superare i 20 caratteri')
    .optional()
    .or(z.literal('')),
  
  cellulare: z.string()
    .max(20, 'Il cellulare non può superare i 20 caratteri')
    .optional()
    .or(z.literal('')),
  
  indirizzo: z.string()
    .max(255, 'L\'indirizzo non può superare i 255 caratteri')
    .optional()
    .or(z.literal('')),
  
  citta: z.string()
    .max(100, 'La città non può superare i 100 caratteri')
    .optional()
    .or(z.literal('')),
  
  cap: z.string()
    .regex(CAP_REGEX, 'Il CAP deve essere di 5 cifre')
    .optional()
    .or(z.literal('')),
  
  provincia: z.string()
    .max(5, 'La provincia non può superare i 5 caratteri')
    .optional()
    .or(z.literal('')),
  
  nazione: z.string()
    .max(100, 'La nazione non può superare i 100 caratteri')
    .default('Italia'),
  
  iban: z.string()
    .regex(IBAN_REGEX, 'Formato IBAN non valido')
    .min(15, 'IBAN troppo corto')
    .max(34, 'IBAN troppo lungo')
    .optional()
    .or(z.literal('')),
  
  modalita_pagamento: z.string()
    .max(100, 'La modalità di pagamento non può superare i 100 caratteri')
    .optional()
    .or(z.literal('')),
  
  codice_sdi: z.string()
    .regex(CODICE_SDI_REGEX, 'Il codice SDI deve essere di 7 caratteri alfanumerici')
    .optional()
    .or(z.literal('')),
  
  indirizzo_pec: z.string()
    .email('Formato email PEC non valido')
    .optional()
    .or(z.literal('')),
  
  note: z.string()
    .max(1000, 'Le note non possono superare i 1000 caratteri')
    .optional()
    .or(z.literal('')),
  
  attivo: z.boolean().default(true),
};

// Schema per creazione cliente
export const clienteCreateSchema = z.object({
  username: z.string()
    .min(3, 'L\'username deve essere di almeno 3 caratteri')
    .max(80, 'L\'username non può superare gli 80 caratteri')
    .regex(/^[a-zA-Z0-9._-]+$/, 'L\'username può contenere solo lettere, numeri, punti, underscore e trattini'),
  
  email: z.string()
    .email('Formato email non valido')
    .max(120, 'L\'email non può superare i 120 caratteri'),
  
  password: z.string()
    .min(6, 'La password deve essere di almeno 6 caratteri')
    .max(255, 'La password non può superare i 255 caratteri'),
  
  ...baseClienteFields,
}).refine((data) => {
  // Se è presente la ragione sociale, non è obbligatorio avere nome/cognome completi
  // Se non c'è ragione sociale, nome e cognome sono obbligatori
  if (!data.ragione_sociale || data.ragione_sociale.trim() === '') {
    return data.nome.trim() !== '' && data.cognome.trim() !== '';
  }
  return true;
}, {
  message: 'Nome e cognome sono obbligatori se non è specificata la ragione sociale',
  path: ['nome'],
});

// Schema per aggiornamento cliente (tutti i campi opzionali tranne validazioni)
export const clienteUpdateSchema = z.object({
  username: z.string()
    .min(3, 'L\'username deve essere di almeno 3 caratteri')
    .max(80, 'L\'username non può superare gli 80 caratteri')
    .regex(/^[a-zA-Z0-9._-]+$/, 'L\'username può contenere solo lettere, numeri, punti, underscore e trattini')
    .optional(),
  
  email: z.string()
    .email('Formato email non valido')
    .max(120, 'L\'email non può superare i 120 caratteri')
    .optional(),
  
  nome: z.string()
    .min(2, 'Il nome deve essere di almeno 2 caratteri')
    .max(100, 'Il nome non può superare i 100 caratteri')
    .optional(),
  
  cognome: z.string()
    .min(2, 'Il cognome deve essere di almeno 2 caratteri')
    .max(100, 'Il cognome non può superare i 100 caratteri')
    .optional(),
  
  ragione_sociale: baseClienteFields.ragione_sociale,
  partita_iva: baseClienteFields.partita_iva,
  codice_fiscale: baseClienteFields.codice_fiscale,
  telefono: baseClienteFields.telefono,
  cellulare: baseClienteFields.cellulare,
  indirizzo: baseClienteFields.indirizzo,
  citta: baseClienteFields.citta,
  cap: baseClienteFields.cap,
  provincia: baseClienteFields.provincia,
  nazione: baseClienteFields.nazione,
  iban: baseClienteFields.iban,
  modalita_pagamento: baseClienteFields.modalita_pagamento,
  codice_sdi: baseClienteFields.codice_sdi,
  indirizzo_pec: baseClienteFields.indirizzo_pec,
  note: baseClienteFields.note,
  attivo: baseClienteFields.attivo,
});

// Schema per reset password
export const resetPasswordSchema = z.object({
  new_password: z.string()
    .min(6, 'La password deve essere di almeno 6 caratteri')
    .max(255, 'La password non può superare i 255 caratteri'),
  
  confirm_password: z.string()
    .min(6, 'Conferma la password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: 'Le password non coincidono',
  path: ['confirm_password'],
});

// Schema per filtri di ricerca
export const clientiFiltersSchema = z.object({
  search: z.string().optional(),
  stato: z.enum(['attivo', 'disattivo', '']).optional(),
  citta: z.string().optional(),
  sort_by: z.enum(['nome', 'cognome', 'email', 'citta', 'data_creazione', 'ultimo_accesso']).default('data_creazione'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  per_page: z.number().min(1).max(100).default(25),
});

// Tipi derivati dagli schemi
export type ClienteCreateFormData = z.infer<typeof clienteCreateSchema>;
export type ClienteUpdateFormData = z.infer<typeof clienteUpdateSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ClientiFiltersFormData = z.infer<typeof clientiFiltersSchema>;

// Funzioni di utilità per la validazione
export const validatePartitaIva = (value: string): boolean => {
  if (!value) return true; // Opzionale
  return PARTITA_IVA_REGEX.test(value);
};

export const validateCodiceFiscale = (value: string): boolean => {
  if (!value) return true; // Opzionale
  return CODICE_FISCALE_REGEX.test(value.toUpperCase());
};

export const validateCap = (value: string): boolean => {
  if (!value) return true; // Opzionale
  return CAP_REGEX.test(value);
};

export const validateIban = (value: string): boolean => {
  if (!value) return true; // Opzionale
  return IBAN_REGEX.test(value.toUpperCase()) && value.length >= 15 && value.length <= 34;
};

export const validateCodiceSdi = (value: string): boolean => {
  if (!value) return true; // Opzionale
  return CODICE_SDI_REGEX.test(value.toUpperCase());
};

// Funzione per pulire i dati del form (rimuove stringhe vuote)
export const cleanFormData = <T extends Record<string, any>>(data: T): T => {
  const cleaned = { ...data };
  
  Object.keys(cleaned).forEach(key => {
    if (cleaned[key] === '') {
      cleaned[key] = undefined;
    }
  });
  
  return cleaned;
};