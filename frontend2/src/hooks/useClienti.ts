import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { clientiApi } from '../services/api';
import { 
  Cliente, 
  ClienteCreate, 
  ClienteUpdate, 
  ClientiFilters, 
  ClientiResponse, 
  ClienteStats,
  ApiResponse 
} from '../types/clienti';

// Query keys
export const clientiQueryKeys = {
  all: ['clienti'] as const,
  lists: () => [...clientiQueryKeys.all, 'list'] as const,
  list: (filters: ClientiFilters) => [...clientiQueryKeys.lists(), filters] as const,
  details: () => [...clientiQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...clientiQueryKeys.details(), id] as const,
  stats: () => [...clientiQueryKeys.all, 'stats'] as const,
};

// Hook per lista clienti con filtri
export function useClienti(filters: ClientiFilters = {}) {
  return useQuery({
    queryKey: clientiQueryKeys.list(filters),
    queryFn: async (): Promise<ClientiResponse> => {
      const params = new URLSearchParams();
      
      if (filters.search) params.append('search', filters.search);
      if (filters.stato) params.append('stato', filters.stato);
      if (filters.citta) params.append('citta', filters.citta);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.per_page) params.append('per_page', filters.per_page.toString());
      
      const response = await clientiApi.getClienti(Object.fromEntries(params));
      return response.data;
    },
    staleTime: 30000, // 30 secondi
  });
}

// Hook per dettagli cliente singolo
export function useCliente(id: number) {
  return useQuery({
    queryKey: clientiQueryKeys.detail(id),
    queryFn: async (): Promise<Cliente> => {
      const response = await clientiApi.getCliente(id);
      return response.data.cliente;
    },
    enabled: !!id,
  });
}

// Hook per statistiche clienti
export function useClientiStats() {
  return useQuery({
    queryKey: clientiQueryKeys.stats(),
    queryFn: async (): Promise<ClienteStats> => {
      const response = await clientiApi.getClientiStats();
      return response.data.stats;
    },
    staleTime: 60000, // 1 minuto
  });
}

// Hook per creare cliente
export function useCreateCliente() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: ClienteCreate): Promise<Cliente> => {
      const response = await clientiApi.createCliente(data);
      return response.data.cliente;
    },
    onSuccess: () => {
      // Invalida tutte le query dei clienti
      queryClient.invalidateQueries({ queryKey: clientiQueryKeys.all });
    },
  });
}

// Hook per aggiornare cliente
export function useUpdateCliente() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: ClienteUpdate }): Promise<Cliente> => {
      const response = await clientiApi.updateCliente(id, data);
      return response.data.cliente;
    },
    onSuccess: (data) => {
      // Invalida le query correlate
      queryClient.invalidateQueries({ queryKey: clientiQueryKeys.all });
      queryClient.setQueryData(clientiQueryKeys.detail(data.id_cliente), data);
    },
  });
}

// Hook per eliminare cliente
export function useDeleteCliente() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await clientiApi.deleteCliente(id);
    },
    onSuccess: () => {
      // Invalida tutte le query dei clienti
      queryClient.invalidateQueries({ queryKey: clientiQueryKeys.all });
    },
  });
}

// Hook per toggle status cliente
export function useToggleClienteStatus() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number): Promise<{ cliente: Partial<Cliente>; message: string }> => {
      const response = await clientiApi.toggleClienteStatus(id);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalida le query correlate
      queryClient.invalidateQueries({ queryKey: clientiQueryKeys.all });
      
      // Aggiorna la cache del dettaglio se presente
      const clienteId = data.cliente.id_cliente;
      if (clienteId) {
        queryClient.setQueryData(
          clientiQueryKeys.detail(clienteId),
          (oldData: Cliente | undefined) => 
            oldData ? { ...oldData, attivo: data.cliente.attivo! } : undefined
        );
      }
    },
  });
}

// Hook per reset password cliente
export function useResetClientePassword() {
  return useMutation({
    mutationFn: async ({ id, newPassword }: { id: number; newPassword: string }): Promise<void> => {
      await clientiApi.resetClientePassword(id, newPassword);
    },
  });
}

// Hook combinato per operazioni multiple
export function useClientiOperations() {
  const createCliente = useCreateCliente();
  const updateCliente = useUpdateCliente();
  const deleteCliente = useDeleteCliente();
  const toggleStatus = useToggleClienteStatus();
  const resetPassword = useResetClientePassword();
  
  return {
    create: createCliente,
    update: updateCliente,
    delete: deleteCliente,
    toggleStatus,
    resetPassword,
    isLoading: 
      createCliente.isPending || 
      updateCliente.isPending || 
      deleteCliente.isPending || 
      toggleStatus.isPending || 
      resetPassword.isPending,
  };
}