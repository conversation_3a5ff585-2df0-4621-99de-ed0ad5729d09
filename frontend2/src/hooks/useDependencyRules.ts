import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { get, post, put, del } from '../services/api';
import { IRegolaDipendenzaComplessa, IRegolaDipendenzaComplessaFormPayload } from '../types/dependency-rules';
import { AxiosError } from 'axios';

// Hook per ottenere tutte le regole di dipendenza complesse
export const useGetDependencyRules = () => {
  return useQuery<IRegolaDipendenzaComplessa[], Error>({
    queryKey: ['dependencyRules'],
    queryFn: async () => {
      const response = await get<IRegolaDipendenzaComplessa[]>('/admin/dependency-rules');
      return response.data;
    },
  });
};

// Hook per ottenere una singola regola di dipendenza complessa
export const useGetDependencyRule = (id: number) => {
  return useQuery<IRegolaDipendenzaComplessa, Error>({
    queryKey: ['dependencyRule', id],
    queryFn: async () => {
      const response = await get<IRegolaDipendenzaComplessa>(`/admin/dependency-rules/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 0, // Sempre considera i dati stale per forzare il refetch
    cacheTime: 0, // Non mantenere cache per evitare dati obsoleti
  });
};

// Hook per creare una nuova regola di dipendenza complessa
export const useCreateDependencyRule = () => {
  const queryClient = useQueryClient();
  return useMutation<{ message: string; id: number }, AxiosError, IRegolaDipendenzaComplessaFormPayload>({
    mutationFn: async (payload) => {
      const response = await post<{ message: string; id: number }>('/admin/dependency-rules', payload);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dependencyRules'] });
    },
  });
};

// Hook per aggiornare una regola di dipendenza complessa esistente
export const useUpdateDependencyRule = () => {
  const queryClient = useQueryClient();
  return useMutation<{ message: string }, AxiosError, { id: number; payload: IRegolaDipendenzaComplessaFormPayload }>({
    mutationFn: async ({ id, payload }) => {
      const response = await put<{ message: string }>(`/admin/dependency-rules/${id}`, payload);
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['dependencyRules'] });
      queryClient.invalidateQueries({ queryKey: ['dependencyRule', variables.id] });
    },
  });
};

// Hook per eliminare una regola di dipendenza complessa
export const useDeleteDependencyRule = () => {
  const queryClient = useQueryClient();
  return useMutation<{ message: string }, AxiosError, number>({
    mutationFn: async (id) => {
      const response = await del<{ message: string }>(`/admin/dependency-rules/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dependencyRules'] });
    },
  });
};

// Hook per duplicare una regola di dipendenza complessa
export const useDuplicateDependencyRule = () => {
  const queryClient = useQueryClient();
  return useMutation<{ message: string; id: number; nome_regola: string }, AxiosError, number>({
    mutationFn: async (id) => {
      const response = await post<{ message: string; id: number; nome_regola: string }>(`/admin/dependency-rules/${id}/duplicate`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dependencyRules'] });
    },
  });
};
