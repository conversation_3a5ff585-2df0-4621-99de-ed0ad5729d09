import { useQuery } from '@tanstack/react-query';
import { get } from '../services/api';

interface ISelectOption {
  value: number;
  label: string;
}

// Hook per ottenere tutti i parametri per le select (id, nome)
export const useGetAllParametersForSelect = () => {
  return useQuery<ISelectOption[], Error>({
    queryKey: ['allParametersForSelect'],
    queryFn: async () => {
      const response = await get<{ id_parametro: number; nome_parametro: string }[]>('/admin/parametri/all-for-select');
      return response.data.map(param => ({
        value: param.id_parametro,
        label: param.nome_parametro,
      }));
    },
  });
};

// Hook per ottenere i valori di un parametro specifico per le select
export const useGetParameterValuesForSelect = (parameterId: number | undefined) => {
  return useQuery<ISelectOption[], Error>({
    queryKey: ['parameterValuesForSelect', parameterId],
    queryFn: async () => {
      if (!parameterId) return [];
      const response = await get<{ id_valore_parametro: number; testo_visualizzato_ui: string }[]>(`/admin/parametri/${parameterId}/valori-for-select`);
      return response.data.map(val => ({
        value: val.id_valore_parametro,
        label: val.testo_visualizzato_ui,
      }));
    },
    enabled: !!parameterId, // Abilita la query solo se parameterId è presente
  });
};

// Hook per ottenere i parametri attivi (non is_root) per le select
export const useGetActiveParametersForSelect = () => {
  return useQuery<ISelectOption[], Error>({
    queryKey: ['activeParametersForSelect'],
    queryFn: async () => {
      const response = await get<{ id_parametro: number; nome_parametro: string }[]>('/admin/parametri/all-for-select');
      return response.data.map(param => ({
        value: param.id_parametro,
        label: param.nome_parametro,
      }));
    },
  });
};