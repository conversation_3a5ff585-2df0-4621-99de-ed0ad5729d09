import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { get, post, put, del } from '../services/api';
import { 
  IProductsResponse, 
  IProductFormPayload, 
  IProductCreateResponse, 
  IProductPrerequisites,
  IProductDetailResponse,
  IProductDeleteResponse
} from '../types/products';
import { AxiosError } from 'axios';

// Hook per ottenere tutti i prodotti
export const useGetProducts = () => {
  return useQuery<IProductsResponse, Error>({
    queryKey: ['products'],
    queryFn: async () => {
      const response = await get<IProductsResponse>('/admin/products');
      return response.data;
    },
  });
};

// Hook per ottenere un singolo prodotto
export const useGetProduct = (id: number) => {
  return useQuery<IProductDetailResponse, Error>({
    queryKey: ['product', id],
    queryFn: async () => {
      const response = await get<IProductDetailResponse>(`/admin/products/${id}`);
      return response.data;
    },
    enabled: !!id,
  });
};

// Hook per ottenere i prerequisiti per la creazione di prodotti
export const useGetProductPrerequisites = () => {
  return useQuery<IProductPrerequisites, Error>({
    queryKey: ['productPrerequisites'],
    queryFn: async () => {
      const response = await get<IProductPrerequisites>('/admin/products/prerequisites');
      return response.data;
    },
    staleTime: 1000 * 60 * 10, // Cache per 10 minuti
  });
};

// Hook per creare un nuovo prodotto
export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation<IProductCreateResponse, AxiosError, FormData | IProductFormPayload>({
    mutationFn: async (payload) => {
      const response = await post<IProductCreateResponse>('/admin/products', payload);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['productPrerequisites'] });
    },
  });
};

// Hook per aggiornare un prodotto esistente
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation<{ message: string; product: any }, AxiosError, { id: number; payload: FormData | IProductFormPayload }>({
    mutationFn: async ({ id, payload }) => {
      const response = await put<{ message: string; product: any }>(`/admin/products/${id}`, payload);
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', variables.id] });
    },
  });
};

// Hook per eliminare un prodotto
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  return useMutation<IProductDeleteResponse, AxiosError, number>({
    mutationFn: async (id) => {
      const response = await del<IProductDeleteResponse>(`/admin/products/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};