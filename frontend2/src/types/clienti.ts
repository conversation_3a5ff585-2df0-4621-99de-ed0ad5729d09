// Tipi per la gestione clienti

export interface Cliente {
  id_cliente: number;
  username: string;
  email: string;
  nome: string;
  cognome: string;
  nome_completo: string;
  denominazione: string;
  ragione_sociale?: string;
  partita_iva?: string;
  codice_fiscale?: string;
  telefono?: string;
  cellulare?: string;
  indirizzo?: string;
  citta?: string;
  cap?: string;
  provincia?: string;
  nazione: string;
  iban?: string;
  modalita_pagamento?: string;
  codice_sdi?: string;
  indirizzo_pec?: string;
  note?: string;
  attivo: boolean;
  data_creazione: string;
  data_ultima_modifica: string;
  ultimo_accesso?: string;
}

export interface ClienteCreate {
  username: string;
  email: string;
  password: string;
  nome: string;
  cognome: string;
  ragione_sociale?: string;
  partita_iva?: string;
  codice_fiscale?: string;
  telefono?: string;
  cellulare?: string;
  indirizzo?: string;
  citta?: string;
  cap?: string;
  provincia?: string;
  nazione?: string;
  iban?: string;
  modalita_pagamento?: string;
  codice_sdi?: string;
  indirizzo_pec?: string;
  note?: string;
  attivo?: boolean;
}

export interface ClienteUpdate {
  username?: string;
  email?: string;
  nome?: string;
  cognome?: string;
  ragione_sociale?: string;
  partita_iva?: string;
  codice_fiscale?: string;
  telefono?: string;
  cellulare?: string;
  indirizzo?: string;
  citta?: string;
  cap?: string;
  provincia?: string;
  nazione?: string;
  iban?: string;
  modalita_pagamento?: string;
  codice_sdi?: string;
  indirizzo_pec?: string;
  note?: string;
  attivo?: boolean;
}

export interface ClienteListItem {
  id_cliente: number;
  username: string;
  email: string;
  nome_completo: string;
  denominazione: string;
  citta?: string;
  telefono?: string;
  attivo: boolean;
  data_creazione: string;
  ultimo_accesso?: string;
}

export interface ClientiFilters {
  search?: string;
  stato?: 'attivo' | 'disattivo' | '';
  citta?: string;
  sort_by?: 'nome' | 'cognome' | 'email' | 'citta' | 'data_creazione' | 'ultimo_accesso';
  sort_order?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

export interface ClientiResponse {
  clienti: ClienteListItem[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  filters: {
    search: string;
    stato: string;
    citta: string;
    sort_by: string;
    sort_order: string;
  };
}

export interface ClienteStats {
  total_clienti: number;
  clienti_attivi: number;
  clienti_disattivi: number;
  nuovi_clienti_ultimo_mese: number;
  accessi_ultima_settimana: number;
  percentuale_attivi: number;
}

export interface ApiResponse<T> {
  message?: string;
  cliente?: T;
  clienti?: T[];
  stats?: ClienteStats;
  errors?: Record<string, string[]>;
}

// Opzioni per modalità di pagamento
export const MODALITA_PAGAMENTO_OPTIONS = [
  'Bonifico bancario',
  'RID/SDD',
  'Contanti',
  'Assegno',
  'Carta di credito',
  'PayPal',
  'Altro'
] as const;

// Province italiane (selezione comune)
export const PROVINCE_ITALIANE = [
  'AG', 'AL', 'AN', 'AO', 'AR', 'AP', 'AT', 'AV', 'BA', 'BT', 'BL', 'BN', 'BG', 'BI', 'BO', 'BZ', 'BS', 'BR',
  'CA', 'CL', 'CB', 'CI', 'CE', 'CT', 'CZ', 'CH', 'CO', 'CS', 'CR', 'KR', 'CN', 'EN', 'FM', 'FE', 'FI', 'FG',
  'FC', 'FR', 'GE', 'GO', 'GR', 'IM', 'IS', 'SP', 'AQ', 'LT', 'LE', 'LC', 'LI', 'LO', 'LU', 'MC', 'MN', 'MS',
  'MT', 'VS', 'ME', 'MI', 'MO', 'MB', 'NA', 'NO', 'NU', 'OG', 'OT', 'OR', 'PD', 'PA', 'PR', 'PV', 'PG', 'PU',
  'PE', 'PC', 'PI', 'PT', 'PN', 'PZ', 'PO', 'RG', 'RA', 'RC', 'RE', 'RI', 'RN', 'RM', 'RO', 'SA', 'SS', 'SV',
  'SI', 'SR', 'SO', 'TA', 'TE', 'TR', 'TO', 'TP', 'TN', 'TV', 'TS', 'UD', 'VA', 'VE', 'VB', 'VC', 'VR', 'VV',
  'VI', 'VT'
] as const;