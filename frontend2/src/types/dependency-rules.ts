// Tipi per le condizioni delle regole di dipendenza
export interface ICondizioneRegola {
  id: number;
  id_regola: number;
  id_parametro_condizionante: number;
  id_valore_condizione_predefinita: number | null;
  valore_condizione_libero: string | null;
  tipo_condizione: string;
  ordine_valutazione: number;
}

// Tipi per i risultati delle regole di dipendenza
export interface IRisultatoRegola {
  id: number;
  id_regola: number;
  id_parametro_effetto: number;
  tipo_effetto: string;
  id_valore_effetto_predefinito: number | null;
  id_valori_effetto_predefiniti: number[] | null; // Per selezione multipla (FILTER_VALUES)
  valore_effetto_libero: string | null;
}

// Tipo principale per le regole di dipendenza complesse
export interface IRegolaDipendenzaComplessa {
  id: number;
  nome_regola: string;
  descrizione: string | null;
  attiva: boolean;
  logica_combinazione_condizioni: string;
  condizioni: ICondizioneRegola[];
  risultati: IRisultatoRegola[];
}

// Tipi per i payload del form (senza ID per la creazione)
export interface ICondizioneRegolaFormPayload {
  id_parametro_condizionante: number;
  id_valore_condizione_predefinita: number | null;
  valore_condizione_libero: string | null;
  tipo_condizione: string;
  ordine_valutazione: number;
}

export interface IRisultatoRegolaFormPayload {
  id_parametro_effetto: number;
  tipo_effetto: string;
  id_valore_effetto_predefinito: number | null;
  valore_effetto_libero: string | null;
}

// Tipo interno per il form che supporta selezione multipla
export interface IRisultatoRegolaFormData {
  id_parametro_effetto: number;
  tipo_effetto: string;
  id_valore_effetto_predefinito: number | null;
  id_valori_effetto_predefiniti: number[] | null; // Per selezione multipla (FILTER_VALUES)
  valore_effetto_libero: string | null;
}

export interface IRegolaDipendenzaComplessaFormPayload {
  nome_regola: string;
  descrizione?: string | null;
  attiva?: boolean;
  logica_combinazione_condizioni?: string;
  condizioni: ICondizioneRegolaFormPayload[];
  risultati: IRisultatoRegolaFormPayload[];
}

// Tipo interno per il form
export interface IRegolaDipendenzaComplessaFormData {
  nome_regola: string;
  descrizione?: string | null;
  attiva?: boolean;
  logica_combinazione_condizioni?: string;
  condizioni: ICondizioneRegolaFormPayload[];
  risultati: IRisultatoRegolaFormData[];
}

// Tipi per le opzioni delle select
export interface ITipoCondizione {
  value: string;
  label: string;
}

export interface ITipoEffetto {
  value: string;
  label: string;
}

// Costanti per i tipi di condizione e effetto
export const TIPI_CONDIZIONE: ITipoCondizione[] = [
  { value: 'EQUALS', label: 'Uguale a' },
  { value: 'NOT_EQUALS', label: 'Diverso da' },
  { value: 'HAS_VALUE', label: 'Ha un valore' },
  { value: 'GREATER_THAN', label: 'Maggiore di' },
  { value: 'LESS_THAN', label: 'Minore di' },
  { value: 'GREATER_THAN_OR_EQUAL', label: 'Maggiore o uguale a' },
  { value: 'LESS_THAN_OR_EQUAL', label: 'Minore o uguale a' },
  { value: 'CONTAINS', label: 'Contiene' },
];

export const TIPI_EFFETTO: ITipoEffetto[] = [
  { value: 'SHOW', label: 'Mostra' },
  { value: 'HIDE', label: 'Nascondi' },
  { value: 'SET_VALUE', label: 'Imposta valore' },
  { value: 'FILTER_VALUES', label: 'Filtra valori' },
  { value: 'MAKE_REQUIRED', label: 'Rendi obbligatorio' },
  { value: 'MAKE_OPTIONAL', label: 'Rendi opzionale' },
];

export const LOGICHE_COMBINAZIONE = [
  { value: 'AND', label: 'E (AND)' },
  { value: 'OR', label: 'O (OR)' },
];