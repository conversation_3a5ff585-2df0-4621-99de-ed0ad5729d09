// Tipi per la gestione dei prodotti (Diametri)

export interface IProduct {
  id: number;
  nome_prodotto: string;
  descrizione?: string;
  foto?: string;
  colore?: string;
  ordine_visualizzazione: number;
  data_creazione?: string;
  data_modifica?: string;
  regole_associate: IProductRule[];
  parametro_diametro_id: number;
}

export interface IProductRule {
  id: number;
  nome_regola: string;
  descrizione?: string;
  attiva: boolean;
  prerequisiti: Record<string, IProductPrerequisite>;
}

export interface IProductPrerequisite {
  id_parametro: number;
  id_valore: number;
  nome_valore: string;
  tipo_condizione: string;
}

export interface IProductsResponse {
  products: IProduct[];
  parametro_diametro: {
    id: number;
    nome: string;
    tipo_controllo_ui: string;
  };
  total_products: number;
}

export interface IProductFormPayload {
  nome_prodotto: string;
  descrizione?: string;
  colore?: string;
  ordine_visualizzazione?: number;
  prerequisiti: {
    id_lavorazione: number;
    id_tecniche: number[]; // Cambiato da singolo a array
    id_tipologia_impianto: number;
    id_connessione: number;
  };
}

export interface IProductCreateResponse {
  message: string;
  product: {
    id: number;
    nome_prodotto: string;
    descrizione?: string;
    foto?: string;
    colore?: string;
    ordine_visualizzazione: number;
  };
  regola: {
    id: number;
    nome_regola: string;
    descrizione?: string;
    attiva: boolean;
  };
}

export interface IProductPrerequisites {
  prerequisiti: {
    lavorazione?: {
      id_parametro: number;
      nome_parametro: string;
      valore_predefinito: {
        id: number;
        nome: string;
      };
    };
    tecnica?: {
      id_parametro: number;
      nome_parametro: string;
      valori_disponibili: IPrerequisiteValue[];
    };
    tipologia_impianto?: {
      id_parametro: number;
      nome_parametro: string;
      valori_disponibili: IPrerequisiteValue[];
    };
    connessione?: {
      id_parametro: number;
      nome_parametro: string;
      valori_disponibili: IPrerequisiteValue[];
    };
  };
  parametro_diametro?: {
    id_parametro: number;
    nome_parametro: string;
    tipo_controllo_ui: string;
  };
  prerequisiti_mancanti: string[];
  sistema_pronto: boolean;
}

export interface IPrerequisiteValue {
  id: number;
  nome: string;
  descrizione?: string;
  ordine: number;
}

export interface IProductDetailResponse {
  product: IProduct;
}

export interface IProductDeleteResponse {
  message: string;
  regole_eliminate: Array<{
    id: number;
    nome_regola: string;
  }>;
  product_id: number;
}