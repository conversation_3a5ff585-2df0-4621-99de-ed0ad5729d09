export interface LoginFormData {
  username: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: number;
    username: string;
    nome_completo: string;
  };
}

export interface CurrentUser {
  id: number;
  username: string;
  nome_completo: string | null;
}

export interface AuthState {
  jwt_token: string | null;
  refresh_token: string | null;
  currentUser: CurrentUser | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

export type AuthAction =
  | { type: 'LOGIN_SUCCESS'; payload: { jwt_token: string; refresh_token: string; user: CurrentUser } }
  | { type: 'LOGIN_FAIL'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'LOADING' };