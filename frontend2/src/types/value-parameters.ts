export interface IValueParameter {
  id: number;
  parametro_id: number;
  valore: string | number | boolean | null;
  foto?: string;
  colore?: string;
  descrizione?: string;
  ordine_visualizzazione: number;
  data_creazione: string;
  data_ultima_modifica: string;
}

export interface IValueParameterFormPayload {
  parametro_id: number;
  testo_visualizzato_ui: string | number | boolean | null;
  foto?: string;
  colore?: string;
  descrizione?: string;
  ordine_visualizzazione?: number;
}