export interface IParameter {
  id_parametro: number;
  nome_parametro: string;
  tipo_controllo_ui: string;
  descrizione?: string;
  foto?: string;
  is_root: boolean;
  ordine_visualizzazione: number;
  attivo: boolean;
  data_creazione: string;
  data_modifica: string;
}

export interface IParameterFormPayload {
  nome_parametro: string;
  descrizione?: string;
  foto?: FileList;
  tipo_controllo_ui: string;
  obbligatorio: boolean;
  is_root: boolean;
  ordine_visualizzazione?: number;
  attivo: boolean;
}