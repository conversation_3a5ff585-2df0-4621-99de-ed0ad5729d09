import React, { createContext, useReducer, useEffect, ReactNode, useContext } from 'react';
import { CurrentUser, AuthState, AuthAction, LoginFormData, AuthResponse } from '../types/auth';
import api, { setupInterceptors } from '../services/api'; // Importa l'istanza di axios e setupInterceptors

// Definizione dello stato iniziale
const initialState: AuthState = {
  jwt_token: null,
  refresh_token: null, // Aggiunto refresh_token
  currentUser: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

// Reducer per la gestione dello stato di autenticazione
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        jwt_token: action.payload.jwt_token,
        refresh_token: action.payload.refresh_token, // Aggiunto refresh_token
        currentUser: action.payload.user,
        isAuthenticated: true,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAIL':
      return {
        ...state,
        jwt_token: null,
        refresh_token: null, // Aggiunto refresh_token
        currentUser: null,
        isAuthenticated: false,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        jwt_token: null,
        refresh_token: null, // Aggiunto refresh_token
        currentUser: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      };
    case 'LOADING':
      return {
        ...state,
        loading: true,
        error: null,
      };
    default:
      return state;
  }
};

// Definizione del tipo per il contesto
interface AuthContextType extends AuthState {
  login: (credentials: LoginFormData) => Promise<void>;
  logout: () => void;
}

// Creazione del contesto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider dell'autenticazione
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Inizializza gli interceptor di Axios con la funzione di logout
  useEffect(() => {
    setupInterceptors(logout);
  }, []); // Esegui solo una volta all'avvio dell'applicazione

  // Carica lo stato di autenticazione da localStorage all'avvio dell'applicazione
  useEffect(() => {
    const storedJwtToken = localStorage.getItem('jwt_token');
    const storedRefreshToken = localStorage.getItem('refresh_token');
    const storedUser = localStorage.getItem('user');

    if (storedJwtToken && storedRefreshToken && storedUser) {
      try {
        const user: CurrentUser = JSON.parse(storedUser);
        dispatch({ type: 'LOGIN_SUCCESS', payload: { jwt_token: storedJwtToken, refresh_token: storedRefreshToken, user } });
      } catch (error) {
        console.error('Failed to parse user from localStorage:', error);
        localStorage.removeItem('jwt_token');
        localStorage.removeItem('refresh_token'); // Rimuovi anche il refresh_token
        localStorage.removeItem('user');
      }
    }
  }, []);

  const login = async (credentials: LoginFormData) => {
    dispatch({ type: 'LOADING' });
    try {
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      const { access_token, refresh_token, user } = response.data;

      localStorage.setItem('jwt_token', access_token);
      localStorage.setItem('refresh_token', refresh_token);
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({ type: 'LOGIN_SUCCESS', payload: { jwt_token: access_token, refresh_token, user } });
    } catch (err: any) {
      console.error('Login failed:', err);
      dispatch({ type: 'LOGIN_FAIL', payload: err.response?.data?.msg || 'Login failed' });
      throw err; // Rilancia l'errore per essere gestito dal componente chiamante (es. LoginPage)
    }
  };

  const logout = () => {
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('refresh_token'); // Rimuovi anche il refresh_token
    localStorage.removeItem('user');
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{ ...state, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook personalizzato per usare il contesto di autenticazione
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};