import React, { PropsWithChildren } from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  UsersIcon,
  // AdjustmentsHorizontalIcon,
  SparklesIcon, // Importa una nuova icona per le regole di dipendenza
  CubeIcon, // Importa icona per i prodotti
} from '@heroicons/react/24/outline'; // Importa le icone necessarie

const AdminLayout: React.FC<PropsWithChildren> = ({ children }) => {
  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: HomeIcon }, // Rotta per la dashboard
    { name: 'Gestione Prodotti', href: '/admin/products', icon: CubeIcon }, // Nuova rotta per la gestione prodotti
    { name: 'Parametri', href: '/admin/parameters', icon: Cog6ToothIcon }, // Rotta per i parametri
    // { name: 'Valori Parametro', href: '/admin/parameters/:parameterId/values', icon: AdjustmentsHorizontalIcon }, // Rotta per i valori dei parametri
    { name: '<PERSON>ole Dipendenza', href: '/admin/dependency-rules', icon: SparklesIcon }, // Nuova rotta per le regole di dipendenza
    { name: 'Clienti', href: '/admin/clienti', icon: UsersIcon }, // Rotta per i clienti
    { name: 'Utenti', href: '/admin/users', icon: UserGroupIcon }, // Rotta per gli utenti
    // Aggiungi qui altre voci di navigazione se necessario
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className="w-64 bg-gray-800 text-white flex flex-col">
        <div className="flex items-center justify-center h-16 border-b border-gray-700">
          <span className="text-2xl font-semibold">Admin Panel</span>
        </div>
        <nav className="flex-1 px-2 py-4 space-y-2">
          {navigation.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `flex items-center px-4 py-2 text-sm font-medium rounded-md
                ${isActive ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`
              }
            >
              <item.icon className="mr-3 h-6 w-6" aria-hidden="true" />
              {item.name}
            </NavLink>
          ))}
        </nav>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Navbar (Placeholder) */}
        <header className="flex items-center justify-between h-16 bg-white border-b border-gray-200 px-4">
          <h1 className="text-xl font-semibold">Dashboard</h1> {/* Potrebbe essere dinamico */}
          {/* Altri elementi della navbar (es. profilo utente) */}
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-4">
          {children}
        </main>

        {/* Footer (Placeholder) */}
        <footer className="footer-placeholder bg-white border-t border-gray-200 h-12 flex items-center justify-center text-gray-600">
          © 2025 DEA3D. Tutti i diritti riservati.
        </footer>
      </div>
    </div>
  );
};

export default AdminLayout;