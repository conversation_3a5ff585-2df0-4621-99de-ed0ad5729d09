# Test delle Nuove Funzionalità "Filtra Valori"

## Checklist di Test

### ✅ Test Base
- [ ] Aprire il form di creazione/modifica regola di dipendenza
- [ ] Aggiungere un risultato con tipo "Filtra valori"
- [ ] Selezionare un parametro che ha molti valori (es. >10 valori)
- [ ] Verificare che appaiano i pulsanti di controllo

### ✅ Test Pulsante "Seleziona tutto"
- [ ] <PERSON><PERSON><PERSON> "Seleziona tutto"
- [ ] Verificare che tutti i valori siano selezionati
- [ ] Verificare che il contatore mostri "(X/X selezionati)"
- [ ] Verificare che il pulsante "Seleziona tutto" sia disabilitato
- [ ] Verificare che l'anteprima mostri i primi 10 valori + overflow se necessario

### ✅ Test Pulsante "Deseleziona tutto"
- [ ] Con alcuni valori selezionati, cliccare "Deseleziona tutto"
- [ ] Verificare che nessun valore sia selezionato
- [ ] Verificare che il contatore mostri "(0/X selezionati)"
- [ ] Verificare che il pulsante "Deseleziona tutto" sia disabilitato
- [ ] Verificare che l'anteprima non sia visibile

### ✅ Test Pulsante "Inverti selezione"
- [ ] Selezionare manualmente 2-3 valori
- [ ] Cliccare "Inverti selezione"
- [ ] Verificare che siano selezionati tutti i valori tranne quelli inizialmente selezionati
- [ ] Verificare che il contatore sia aggiornato correttamente
- [ ] Cliccare nuovamente "Inverti selezione" per tornare alla selezione originale

### ✅ Test Anteprima Valori
- [ ] Selezionare meno di 10 valori
- [ ] Verificare che tutti i valori selezionati siano mostrati come tag blu
- [ ] Selezionare più di 10 valori
- [ ] Verificare che siano mostrati solo i primi 10 + indicatore "+X altri..."
- [ ] Verificare che i tag mostrino il testo corretto dei valori

### ✅ Test Contatore
- [ ] Con 0 valori selezionati: "(0/X selezionati)"
- [ ] Con alcuni valori selezionati: "(Y/X selezionati)"
- [ ] Con tutti i valori selezionati: "(X/X selezionati)"

### ✅ Test Stati Disabilitati
- [ ] **Seleziona tutto**: Disabilitato quando tutti i valori sono già selezionati o quando non ci sono valori
- [ ] **Deseleziona tutto**: Disabilitato quando nessun valore è selezionato
- [ ] **Inverti selezione**: Disabilitato quando non ci sono valori disponibili
- [ ] Tutti i pulsanti disabilitati durante il caricamento (`isLoadingValori`)

### ✅ Test Responsive
- [ ] Testare su schermo desktop: i pulsanti dovrebbero essere in una riga
- [ ] Testare su schermo mobile: i pulsanti dovrebbero andare a capo con `flex-wrap`
- [ ] L'anteprima dovrebbe adattarsi alla larghezza disponibile

### ✅ Test Integrazione Form
- [ ] Selezionare valori e salvare la regola
- [ ] Riaprire la regola per modifica
- [ ] Verificare che i valori selezionati siano caricati correttamente
- [ ] Verificare che i pulsanti funzionino anche con dati preesistenti

### ✅ Test Edge Cases
- [ ] Parametro con 0 valori: verificare che i pulsanti siano disabilitati appropriatamente
- [ ] Parametro con 1 solo valore: testare tutte le funzionalità
- [ ] Parametro con 100+ valori: verificare le performance e l'usabilità
- [ ] Cambio parametro: verificare che la selezione si resetti correttamente

## Scenari d'Uso Reali

### Scenario 1: Gestione Materiali (50+ opzioni)
1. Selezionare parametro "Materiale"
2. Cliccare "Seleziona tutto"
3. Deselezionare manualmente solo "Materiale obsoleto" e "Materiale sperimentale"
4. Verificare che l'anteprima mostri correttamente la selezione

### Scenario 2: Esclusione Rapida
1. Selezionare parametro con molti valori
2. Selezionare manualmente solo i 3 valori da escludere
3. Cliccare "Inverti selezione"
4. Verificare che tutti gli altri valori siano selezionati

### Scenario 3: Reset e Riconfigurazione
1. Fare una selezione complessa
2. Cliccare "Deseleziona tutto"
3. Rifare una selezione diversa
4. Verificare che tutto funzioni correttamente

## Criteri di Successo

✅ **Funzionalità**: Tutti i pulsanti funzionano come previsto
✅ **Usabilità**: L'interfaccia è intuitiva e riduce il tempo di configurazione
✅ **Feedback**: L'utente ha sempre chiaro cosa ha selezionato
✅ **Performance**: Nessun lag anche con 100+ valori
✅ **Accessibilità**: I pulsanti sono chiaramente etichettati e hanno stati appropriati
✅ **Integrazione**: Funziona correttamente con il sistema di form esistente