#!/usr/bin/env python3
"""
Script per testare il caso specifico Avvitato vs Incollato
"""

import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

from app import app
from app.extensions import db
from app.models import RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, Parametri, ValoriParametro
from sqlalchemy.orm import joinedload

def test_specific_case():
    with app.app_context():
        print("=== TEST CASO SPECIFICO: Avvitato vs Incollato ===")
        
        # Trova i parametri e valori rilevanti
        parametri = {p.nome_parametro: p for p in Parametri.query.all()}
        
        # Cerca parametri che potrebbero essere "Tecnica" e "Connessione"
        print("Parametri disponibili:")
        for nome, param in parametri.items():
            print(f"  {param.id_parametro}: {nome}")
            for v in param.valori:
                print(f"    - {v.id_valore_parametro}: {v.testo_visualizzato_ui}")
        
        # Cerca valori che contengono "Avvitato" o "Incollato"
        print("\nValori che contengono 'Avvitato' o 'Incollato':")
        valori_avvitato_incollato = ValoriParametro.query.filter(
            db.or_(
                ValoriParametro.testo_visualizzato_ui.ilike('%avvitato%'),
                ValoriParametro.testo_visualizzato_ui.ilike('%incollato%')
            )
        ).all()
        
        for v in valori_avvitato_incollato:
            param = parametri.get(v.parametro.nome_parametro)
            print(f"  {v.id_valore_parametro}: {v.testo_visualizzato_ui} (Parametro: {v.parametro.nome_parametro}, ID: {v.id_parametro})")
        
        # Cerca parametri che potrebbero essere "Connessione"
        print("\nParametri che contengono 'Connessione':")
        parametri_connessione = Parametri.query.filter(
            Parametri.nome_parametro.ilike('%connessione%')
        ).all()
        
        for p in parametri_connessione:
            print(f"  {p.id_parametro}: {p.nome_parametro}")
            for v in p.valori:
                print(f"    - {v.id_valore_parametro}: {v.testo_visualizzato_ui}")
        
        # Cerca regole che coinvolgono questi parametri
        print("\n=== REGOLE CHE COINVOLGONO QUESTI PARAMETRI ===")
        
        # IDs dei parametri di interesse
        param_ids_interesse = []
        if valori_avvitato_incollato:
            param_ids_interesse.extend([v.id_parametro for v in valori_avvitato_incollato])
        if parametri_connessione:
            param_ids_interesse.extend([p.id_parametro for p in parametri_connessione])
        
        param_ids_interesse = list(set(param_ids_interesse))  # Rimuovi duplicati
        
        print(f"IDs parametri di interesse: {param_ids_interesse}")
        
        # Trova regole che hanno condizioni o risultati su questi parametri
        regole_rilevanti = RegolaDipendenzaComplessa.query.options(
            joinedload(RegolaDipendenzaComplessa.condizioni),
            joinedload(RegolaDipendenzaComplessa.risultati)
        ).filter(
            db.or_(
                RegolaDipendenzaComplessa.condizioni.any(CondizioniRegola.id_parametro_condizionante.in_(param_ids_interesse)),
                RegolaDipendenzaComplessa.risultati.any(RisultatiRegola.id_parametro_effetto.in_(param_ids_interesse))
            )
        ).filter(RegolaDipendenzaComplessa.attiva == True).all()
        
        for rule in regole_rilevanti:
            print(f"\nRegola {rule.id}: {rule.nome_regola}")
            print(f"  Descrizione: {rule.descrizione}")
            
            print(f"  Condizioni:")
            for condition in rule.condizioni:
                param = Parametri.query.get(condition.id_parametro_condizionante)
                param_name = param.nome_parametro if param else "Unknown"
                
                if condition.id_valore_condizione_predefinita:
                    valore = ValoriParametro.query.get(condition.id_valore_condizione_predefinita)
                    valore_name = valore.testo_visualizzato_ui if valore else "Unknown"
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} '{valore_name}' (ID: {condition.id_valore_condizione_predefinita})")
                elif condition.valore_condizione_libero:
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} '{condition.valore_condizione_libero}'")
                else:
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} (presenza)")
            
            print(f"  Risultati:")
            for result in rule.risultati:
                param = Parametri.query.get(result.id_parametro_effetto)
                param_name = param.nome_parametro if param else "Unknown"
                
                if result.id_valore_effetto_predefinito:
                    valore = ValoriParametro.query.get(result.id_valore_effetto_predefinito)
                    valore_name = valore.testo_visualizzato_ui if valore else "Unknown"
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto}) -> '{valore_name}' (ID: {result.id_valore_effetto_predefinito})")
                elif result.valore_effetto_libero:
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto}) -> '{result.valore_effetto_libero}'")
                else:
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto})")

if __name__ == "__main__":
    test_specific_case()