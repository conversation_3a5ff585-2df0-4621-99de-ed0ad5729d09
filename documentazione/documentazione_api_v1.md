# Documentazione API v1

## Introduzione
API per la gestione delle lavorazioni e dei parametri di configurazione. Tutte le richieste e risposte sono in formato JSON.

## Endpoints API

### 1. Ottieni Lavorazioni Attive
**Endpoint:** `GET /api/v1/lavorazioni`  
**Descrizione:** Recupera l'elenco delle lavorazioni attive.  
**Risposta (200 OK):**  
```json
[
  {
    "id_lavorazione": 1,
    "codice_lavorazione": "LAV001",
    "nome_lavorazione": "Taglio Laser",
    "descrizione": "Taglio di precisione con tecnologia laser"
  }
]
```

### 2. Configurazione Iniziale Lavorazione
**Endpoint:** `GET /api/v1/lavorazioni/{id_lavorazione}/configurazione-iniziale`  
**Parametri:**  
- `id_lavorazione` (integer): ID della lavorazione  
**Risposta (200 OK):**  
```json
[
  {
    "id_parametro": 10,
    "codice_parametro": "MAT",
    "nome_parametro": "Materiale",
    "tipo_controllo_ui": "select",
    "obbligatorio": true,
    "ordine_visualizzazione": 1,
    "valori_disponibili": [
      {
        "id_valore_parametro": 101,
        "valore_memorizzato": "ACCIAIO",
        "testo_visualizzato_ui": "Acciaio Inox",
        "default_selezionato": true
      }
    ]
  }
]
```

### 3. Configurazione Dinamica Parametri
**Endpoint:** `POST /api/v1/parametri/configurazione-dinamica`  
**Body Request:**  
```json
{
  "id_lavorazione": 1,
  "selezioni_correnti": [
    {
      "id_parametro": 10,
      "id_valore_selezionato": 101
    }
  ]
}
```
**Risposta (200 OK):**  
```json
{
  "nuovi_parametri_dipendenti": [
    {
      "id_parametro": 15,
      "codice_parametro": "SPESS",
      "nome_parametro": "Spessore",
      "tipo_controllo_ui": "number",
      "obbligatorio": true,
      "ordine_visualizzazione": 2
    }
  ],
  "aggiornamenti_valori_parametri_visibili": []
}
```

## Schemi Dati

### LavorazioneSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| id_lavorazione | integer | ID lavorazione |
| codice_lavorazione | string | Codice identificativo |
| nome_lavorazione | string | Nome descrittivo |

### ParametroSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| id_parametro | integer | ID parametro |
| codice_parametro | string | Codice identificativo |
| tipo_controllo_ui | string | Tipo controllo UI |

### ValoreParametroSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| id_valore_parametro | integer | ID valore |
| valore_memorizzato | string | Valore effettivo |
| testo_visualizzato_ui | string | Testo visualizzato |