# Documentazione API v1 - Aggiornata

## Introduzione
API per la gestione dei parametri di configurazione e valutazione delle dipendenze. Tutte le richieste e risposte sono in formato JSON.

## Base URL
`/api/v1`

## Endpoints API

### 1. Ottieni Parametri Root
**Endpoint:** `GET /api/v1/parameters/root`  
**Descrizione:** Recupera tutti i parametri marcati come root (is_root=true). Questi parametri rappresentano le "lavorazioni" principali.  
**Autenticazione:** Non richiesta  
**Risposta (200 OK):**  
```json
[
  {
    "id_parametro": 1,
    "nome_parametro": "Lavorazioni",
    "tipo_controllo_ui": "TipoControlloUI.SELECT",
    "descrizione": "Tipo di lavorazione principale",
    "foto": "images/parametri/1/lavorazione.png",
    "is_root": true,
    "ordine_visualizzazione": 1,
    "attivo": true,
    "has_dependent_children": true,
    "data_creazione": "2023-10-26T10:00:00",
    "data_modifica": "2023-10-26T10:00:00",
    "valori": [
      {
        "id_valore_parametro": 156,
        "testo_visualizzato_ui": "Elemento Singolo Avvitato",
        "ordine_visualizzazione": 1,
        "foto": null,
        "colore": null,
        "descrizione": "Lavorazione per elemento singolo con connessione avvitata"
      }
    ]
  }
]
```

### 2. Ottieni Dettagli Parametro
**Endpoint:** `GET /api/v1/parameters/{id_parametro}`  
**Parametri:**  
- `id_parametro` (integer): ID del parametro  
**Autenticazione:** Non richiesta  
**Risposta (200 OK):**  
```json
{
  "id_parametro": 1,
  "nome_parametro": "Lavorazioni",
  "tipo_controllo_ui": "TipoControlloUI.SELECT",
  "descrizione": "Tipo di lavorazione principale",
  "foto": "images/parametri/1/lavorazione.png",
  "is_root": true,
  "ordine_visualizzazione": 1,
  "attivo": true,
  "has_dependent_children": true,
  "data_creazione": "2023-10-26T10:00:00",
  "data_modifica": "2023-10-26T10:00:00",
  "valori": [...]
}
```

### 3. Valutazione Dipendenze
**Endpoint:** `POST /api/v1/dependency_evaluation/evaluate`  
**Descrizione:** Valuta le regole di dipendenza basate sui parametri selezionati e restituisce i parametri dipendenti che devono essere mostrati.  
**Autenticazione:** Non richiesta  
**Body Request:**  
```json
{
  "current_parameters": [
    {
      "parameter_id": 1,
      "value_id": 156
    },
    {
      "parameter_id": 3,
      "value_id": 154
    }
  ]
}
```
**Risposta (200 OK):**  
```json
{
  "dependent_parameters": [
    {
      "id_parametro": 3,
      "nome_parametro": "Tipologia Impianto",
      "tipo_controllo_ui": "TipoControlloUI.SELECT",
      "descrizione": "Tipo di impianto utilizzato",
      "foto": null,
      "is_root": false,
      "ordine_visualizzazione": 3,
      "attivo": true,
      "has_dependent_children": true,
      "valori": [
        {
          "id_valore_parametro": 154,
          "testo_visualizzato_ui": "Impianto Tipo A",
          "ordine_visualizzazione": 1,
          "foto": null,
          "colore": null,
          "descrizione": null
        }
      ]
    }
  ]
}
```

## Schemi Dati

### ParametroSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| id_parametro | integer | ID univoco del parametro |
| nome_parametro | string | Nome descrittivo del parametro |
| tipo_controllo_ui | string | Tipo di controllo UI (SELECT, RADIO, CHECKBOX_GROUP, etc.) |
| descrizione | string | Descrizione dettagliata (opzionale) |
| foto | string | Path dell'immagine associata (opzionale) |
| is_root | boolean | Indica se è un parametro root (lavorazione) |
| ordine_visualizzazione | integer | Ordine di visualizzazione del parametro |
| attivo | boolean | Indica se il parametro è attivo |
| has_dependent_children | boolean | Indica se ha parametri dipendenti |
| data_creazione | datetime | Data di creazione |
| data_modifica | datetime | Data ultima modifica |
| valori | array | Array di ValoreParametro |

### ValoreParametroSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| id_valore_parametro | integer | ID univoco del valore |
| testo_visualizzato_ui | string | Testo mostrato nell'interfaccia |
| ordine_visualizzazione | integer | Ordine di visualizzazione |
| foto | string | Path dell'immagine (opzionale) |
| colore | string | Codice colore (opzionale) |
| descrizione | string | Descrizione dettagliata (opzionale) |

### EvaluationRequestSchema
| Campo | Tipo | Descrizione |
|-------|------|-------------|
| current_parameters | array | Array di parametri selezionati |
| current_parameters[].parameter_id | integer | ID del parametro |
| current_parameters[].value_id | integer | ID del valore selezionato |

## Tipi di Controllo UI Supportati

- `TipoControlloUI.SELECT` - Dropdown di selezione
- `TipoControlloUI.RADIO` - Radio buttons
- `TipoControlloUI.CHECKBOX_GROUP` - Gruppo di checkbox
- `TipoControlloUI.INPUT_TEXT` - Campo di testo
- `TipoControlloUI.INPUT_NUMBER` - Campo numerico
- `TipoControlloUI.TEXTAREA` - Area di testo

## Gestione Errori

### Codici di Stato
- `200 OK` - Richiesta completata con successo
- `400 Bad Request` - Errore nella richiesta (payload malformato)
- `404 Not Found` - Risorsa non trovata
- `500 Internal Server Error` - Errore interno del server

### Formato Errori
```json
{
  "error": "Descrizione dell'errore",
  "details": "Dettagli aggiuntivi (opzionale)"
}
```

## Note di Implementazione

### Sistema di Dipendenze
Il sistema di valutazione delle dipendenze supporta:
- **Condizioni multiple** con logica AND/OR
- **Tipi di condizione**: EQUALS, NOT_EQUALS, HAS_VALUE, GREATER_THAN, etc.
- **Tipi di effetto**: SHOW, HIDE, FILTER_VALUES, SET_VALUE, etc.
- **Valutazione dinamica** basata sui parametri selezionati

### Performance
- Le API sono ottimizzate per gestire regole complesse
- Utilizza joinedload per evitare problemi N+1
- Caching dei parametri con dipendenze per lookup efficiente
- **Ordinamento automatico**: I parametri vengono sempre restituiti ordinati per `ordine_visualizzazione`

### Ordinamento dei Parametri
Il sistema garantisce un ordine stabile dei parametri attraverso il campo `ordine_visualizzazione`:
- **Lavorazioni** (ordine: 1) - Parametro root
- **Tipologia dente** (ordine: 2)
- **Tipologia Impianto** (ordine: 3)
- **Tecnica** (ordine: 4)
- **Connessione** (ordine: 5)
- **Diametro** (ordine: 6)

Questo garantisce che l'ordine di visualizzazione rimanga consistente indipendentemente da quando i parametri vengono aggiunti o rimossi dalle regole di dipendenza.

### Compatibilità
Queste API sono utilizzate dal frontend cliente attraverso un layer di adattamento che mantiene la compatibilità con l'interfaccia esistente.