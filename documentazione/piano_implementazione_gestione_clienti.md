# Piano di Implementazione - Gestione Clienti

## Panoramica del Progetto

Implementazione completa del sistema di gestione clienti per il sistema DEA3D, che include:
- Nuovo modello database `Cliente` con anagrafica completa
- Sistema di autenticazione separato per clienti
- Interfaccia admin per gestione clienti (frontend2)
- Frontend cliente funzionante con login/logout
- Dati di test per sviluppo

## Fase 1: Backend - Modello Database e Migrazione

### 1.1 Creazione Modello Cliente
- **File**: `app/models.py`
- **Azione**: Aggiungere la classe `Cliente` con tutti i campi richiesti
- **Campi principali**:
  - Autenticazione: username, password, email
  - Anagrafica: nome, cognome, ragione_sociale, partita_iva, codice_fiscale
  - Contatti: telefono, cellulare, indirizzo, città, cap, provincia, nazione
  - Pagamenti: iban, modalita_pagamento
  - Fatturazione elettronica: codice_sdi, indirizzo_pec
  - Altri: note, stato, timestamp

### 1.2 Migrazione Database
- **Comando**: `alembic revision --autogenerate -m "Aggiunta tabella clienti"`
- **Verifica**: Controllo della migrazione generata
- **Applicazione**: `alembic upgrade head`

## Fase 2: Backend - API di Autenticazione Clienti

### 2.1 Endpoints Autenticazione Cliente
- **Blueprint**: `app/client_auth/routes.py` (nuovo)
- **Prefix**: `/api/client/auth`
- **Endpoints**:
  - `POST /login` - Login cliente
  - `POST /logout` - Logout cliente
  - `POST /refresh` - Refresh token
  - `GET /me` - Profilo cliente corrente

### 2.2 Middleware di Autorizzazione
- **Funzione**: Distinguere tra token admin e cliente
- **Implementazione**: Decorator per verificare tipo utente
- **Claims JWT**: Aggiungere campo `user_type` ('admin' | 'client')

### 2.3 API Profilo Cliente
- **Endpoint**: `PUT /api/client/profile`
- **Funzionalità**: Aggiornamento dati anagrafici cliente
- **Validazione**: Schema Marshmallow per validazione input

## Fase 3: Backend - API Admin per Gestione Clienti

### 3.1 CRUD Clienti per Admin
- **File**: `app/api_admin/routes.py` (estensione)
- **Prefix**: `/api/admin/clienti`
- **Endpoints**:
  - `GET /` - Lista clienti (con paginazione e filtri)
  - `POST /` - Crea nuovo cliente
  - `GET /{id}` - Dettagli cliente
  - `PUT /{id}` - Aggiorna cliente
  - `DELETE /{id}` - Elimina cliente
  - `PUT /{id}/toggle-status` - Attiva/disattiva cliente

### 3.2 Filtri e Ricerca
- **Parametri**: nome, cognome, email, città, stato
- **Ordinamento**: Per nome, data creazione, ultimo accesso
- **Paginazione**: Limite e offset configurabili

### 3.3 Schema Validazione
- **File**: `app/schemas/cliente_schema.py` (nuovo)
- **Schemi**: ClienteCreateSchema, ClienteUpdateSchema, ClienteResponseSchema

## Fase 4: Frontend Admin (frontend2) - Gestione Clienti

### 4.1 Pagine Admin Clienti
- **File**: `frontend2/src/pages/ClientiListPage.tsx` (nuovo)
- **File**: `frontend2/src/pages/ClienteFormPage.tsx` (nuovo)
- **File**: `frontend2/src/pages/ClienteDetailPage.tsx` (nuovo)

### 4.2 Componenti UI
- **Lista Clienti**: Tabella con filtri, ricerca, paginazione
- **Form Cliente**: Form completo per creazione/modifica
- **Dettaglio Cliente**: Vista readonly con azioni

### 4.3 Hooks e Services
- **File**: `frontend2/src/hooks/useClienti.ts` (nuovo)
- **File**: `frontend2/src/services/api.ts` (estensione)
- **Funzionalità**: CRUD completo, filtri, ricerca

### 4.4 Tipi TypeScript
- **File**: `frontend2/src/types/clienti.ts` (nuovo)
- **Interfacce**: Cliente, ClienteCreate, ClienteUpdate, ClienteFilters

### 4.5 Schema Validazione Zod
- **File**: `frontend2/src/schemas/clienteSchemas.ts` (nuovo)
- **Validazioni**: Email, partita IVA, codice fiscale, IBAN, codice SDI

### 4.6 Navigazione Admin
- **File**: `frontend2/src/layouts/AdminLayout.tsx` (modifica)
- **Aggiunta**: Link "Clienti" nel menu di navigazione

## Fase 5: Script Dati di Test

### 5.1 Script Creazione Clienti Fittizi
- **File**: `seed_clienti_test.py` (nuovo)
- **Funzionalità**: Creazione di 5 clienti con dati realistici
- **Dati**: Nomi italiani, indirizzi, partite IVA, codici fiscali validi
- **Password**: Password di test documentate

### 5.2 Dati di Test Inclusi
```python
clienti_test = [
    {
        "username": "mario.rossi",
        "email": "<EMAIL>",
        "nome": "Mario",
        "cognome": "Rossi",
        "telefono": "02-1234567",
        "cellulare": "333-1234567",
        "indirizzo": "Via Roma 123",
        "citta": "Milano",
        "cap": "20100",
        "provincia": "MI",
        "partita_iva": "12345678901",
        "codice_fiscale": "****************"
    },
    # ... altri 4 clienti
]
```

## Fase 6: Frontend Cliente - Sistema di Login Funzionante

### 6.1 Aggiornamento AuthContext
- **File**: `frontend-cliente/contexts/AuthContext.tsx`
- **Funzionalità**:
  - Gestione token JWT
  - Persistenza localStorage
  - Auto-refresh token
  - Logout automatico su scadenza

### 6.2 Servizio API Cliente
- **File**: `frontend-cliente/services/api.ts` (estensione)
- **Endpoints**: Login, logout, refresh, profilo
- **Interceptors**: Gestione automatica token e refresh

### 6.3 Pagina Login Funzionante
- **File**: `frontend-cliente/app/login/page.tsx` (modifica)
- **Funzionalità**:
  - Form di login con validazione
  - Gestione errori
  - Redirect post-login
  - Loading states

### 6.4 Route Protette
- **Componente**: `ProtectedRoute` per frontend-cliente
- **Funzionalità**: Redirect a login se non autenticato
- **Applicazione**: Su tutte le pagine che richiedono autenticazione

### 6.5 Pagina Profilo Cliente
- **File**: `frontend-cliente/app/profile/page.tsx` (modifica)
- **Funzionalità**:
  - Visualizzazione dati cliente
  - Modifica dati anagrafici
  - Cambio password
  - Gestione errori e successo

### 6.6 Navbar Aggiornata
- **File**: `frontend-cliente/components/navbar.tsx` (modifica)
- **Funzionalità**:
  - Mostra nome cliente quando loggato
  - Pulsante logout
  - Link al profilo

## Fase 7: Testing e Documentazione

### 7.1 Test Backend
- **Test**: Autenticazione clienti
- **Test**: CRUD admin clienti
- **Test**: Validazioni e sicurezza

### 7.2 Test Frontend
- **Test**: Login/logout funzionante
- **Test**: Gestione admin clienti
- **Test**: Route protette

### 7.3 Documentazione API
- **File**: `documentazione/api_clienti.md` (nuovo)
- **Contenuto**: Documentazione completa endpoints clienti

## Cronologia di Implementazione Suggerita

### Sprint 1 (Backend Foundation)
1. Modello Cliente e migrazione database
2. API autenticazione clienti
3. Middleware autorizzazione

### Sprint 2 (Admin Backend)
1. API admin per gestione clienti
2. Schema validazione
3. Script dati di test

### Sprint 3 (Frontend Admin)
1. Pagine gestione clienti in frontend2
2. Componenti UI e form
3. Integrazione API

### Sprint 4 (Frontend Cliente)
1. Sistema login funzionante
2. AuthContext aggiornato
3. Route protette e profilo

### Sprint 5 (Testing e Rifinitura)
1. Testing completo
2. Documentazione
3. Bug fixing e ottimizzazioni

## Note Tecniche

### Sicurezza
- Password hashate con Werkzeug
- JWT con scadenza configurabile
- Validazione input lato server
- Sanitizzazione dati

### Performance
- Paginazione per liste clienti
- Indici database su campi ricercati
- Cache per dati frequenti

### Usabilità
- Messaggi errore chiari
- Loading states
- Validazione real-time
- Responsive design

## File da Creare/Modificare

### Nuovi File
- `app/client_auth/routes.py`
- `app/schemas/cliente_schema.py`
- `frontend2/src/pages/ClientiListPage.tsx`
- `frontend2/src/pages/ClienteFormPage.tsx`
- `frontend2/src/hooks/useClienti.ts`
- `frontend2/src/types/clienti.ts`
- `frontend2/src/schemas/clienteSchemas.ts`
- `seed_clienti_test.py`
- `documentazione/api_clienti.md`

### File da Modificare
- `app/models.py`
- `app/__init__.py`
- `app/api_admin/routes.py`
- `frontend2/src/services/api.ts`
- `frontend2/src/layouts/AdminLayout.tsx`
- `frontend-cliente/contexts/AuthContext.tsx`
- `frontend-cliente/app/login/page.tsx`
- `frontend-cliente/app/profile/page.tsx`
- `frontend-cliente/services/api.ts`
- `frontend-cliente/components/navbar.tsx`

## Risultato Finale

Al termine dell'implementazione avremo:
1. **Sistema completo di gestione clienti** con anagrafica dettagliata
2. **Autenticazione separata** per admin e clienti
3. **Interfaccia admin** per gestione clienti completa
4. **Frontend cliente** con login/logout funzionante
5. **Dati di test** per sviluppo e testing
6. **Documentazione completa** del sistema

Il sistema sarà pronto per l'uso in produzione con tutte le funzionalità richieste per la gestione dei clienti del laboratorio odontotecnico.