# Sistema di Regole di Dipendenza - Documentazione Completa

## Panoramica

Il sistema di regole di dipendenza permette di creare logiche complesse per mostrare/nascondere parametri e filtrare i loro valori in base alle selezioni dell'utente.

## Architettura del Sistema

### Entità Principali

```mermaid
erDiagram
    RegolaDipendenzaComplessa {
        int id PK
        string nome_regola
        string descrizione
        boolean attiva
        string logica_combinazione_condizioni
        datetime data_creazione
        datetime data_modifica
    }
    
    CondizioniRegola {
        int id PK
        int id_regola FK
        int id_parametro_condizionante FK
        int id_valore_condizione_predefinita FK
        string valore_condizione_libero
        string tipo_condizione
        int ordine_valutazione
    }
    
    RisultatiRegola {
        int id PK
        int id_regola FK
        int id_parametro_effetto FK
        string tipo_effetto
        int id_valore_effetto_predefinito FK
        string valore_effetto_libero
    }
    
    Parametri {
        int id_parametro PK
        string nome_parametro
        enum tipo_controllo_ui
        boolean is_root
        boolean attivo
    }
    
    ValoriParametro {
        int id_valore_parametro PK
        int id_parametro FK
        string testo_visualizzato_ui
        int ordine_visualizzazione
    }
    
    RegolaDipendenzaComplessa ||--o{ CondizioniRegola : "ha"
    RegolaDipendenzaComplessa ||--o{ RisultatiRegola : "produce"
    CondizioniRegola }o--|| Parametri : "condiziona"
    CondizioniRegola }o--o| ValoriParametro : "valore_condizione"
    RisultatiRegola }o--|| Parametri : "effetto_su"
    RisultatiRegola }o--o| ValoriParametro : "valore_effetto"
    Parametri ||--o{ ValoriParametro : "ha"
```

## Tipi di Condizione Supportati

### 1. **EQUALS** (Uguale a)
- **Descrizione**: Verifica che il parametro abbia esattamente il valore specificato
- **Richiede**: `id_valore_condizione_predefinita` o `valore_condizione_libero`
- **Esempio**: "Tipologia Impianto = Impianto Tipo A"

### 2. **NOT_EQUALS** (Diverso da)
- **Descrizione**: Verifica che il parametro NON abbia il valore specificato
- **Richiede**: `id_valore_condizione_predefinita` o `valore_condizione_libero`
- **Esempio**: "Materiale ≠ Ceramica"

### 3. **HAS_VALUE** (Ha un valore)
- **Descrizione**: Verifica che il parametro abbia un qualsiasi valore selezionato
- **Richiede**: Nessun valore specifico
- **Esempio**: "Tipologia Impianto ha un valore" AND "Tecnica ha un valore"

### 4. **GREATER_THAN** (Maggiore di)
- **Descrizione**: Per valori numerici, verifica che sia maggiore del valore specificato
- **Richiede**: `valore_condizione_libero` (numerico)
- **Esempio**: "Spessore > 2.5"

### 5. **LESS_THAN** (Minore di)
- **Descrizione**: Per valori numerici, verifica che sia minore del valore specificato
- **Richiede**: `valore_condizione_libero` (numerico)
- **Esempio**: "Altezza < 10"

### 6. **CONTAINS** (Contiene)
- **Descrizione**: Per valori testuali, verifica che contenga la stringa specificata
- **Richiede**: `valore_condizione_libero` (stringa)
- **Esempio**: "Note contiene 'urgente'"

## Tipi di Effetto Supportati

### 1. **SHOW** (Mostra)
- **Descrizione**: Rende visibile il parametro specificato
- **Uso**: Per far apparire parametri dipendenti
- **Esempio**: Mostra "Connessione" quando Tipologia e Tecnica sono selezionate

### 2. **HIDE** (Nascondi)
- **Descrizione**: Nasconde il parametro specificato
- **Uso**: Per nascondere parametri non più rilevanti
- **Esempio**: Nascondi "Spessore" quando Materiale = "Flessibile"

### 3. **FILTER_VALUES** (Filtra valori)
- **Descrizione**: Mostra solo i valori specificati per il parametro
- **Uso**: Per limitare le opzioni disponibili
- **Esempio**: Per "Impianto A" + "Tecnica Avvitata" mostra solo "Connessione 1" e "Connessione 2"

### 4. **SET_VALUE** (Imposta valore)
- **Descrizione**: Imposta automaticamente un valore per il parametro
- **Uso**: Per preselezionare valori basati su altre selezioni
- **Esempio**: Se Materiale = "Titanio" allora Spessore = "3.0"

## Logica di Combinazione

### AND (E)
- **Descrizione**: Tutte le condizioni devono essere vere
- **Uso**: Quando serve una combinazione specifica
- **Esempio**: "Tipologia = A" AND "Tecnica = Avvitata"

### OR (O)
- **Descrizione**: Almeno una condizione deve essere vera
- **Uso**: Quando diverse condizioni portano allo stesso risultato
- **Esempio**: "Materiale = Ceramica" OR "Materiale = Zirconia"

## Esempi Pratici

### Esempio 1: Mostra Connessione per Combinazione Specifica
```json
{
  "nome_regola": "Mostra Connessione per Avvitato",
  "descrizione": "Mostra il parametro Connessione quando Tipologia e Tecnica sono selezionate",
  "attiva": true,
  "logica_combinazione_condizioni": "AND",
  "condizioni": [
    {
      "id_parametro_condizionante": 3,
      "tipo_condizione": "HAS_VALUE",
      "ordine_valutazione": 1
    },
    {
      "id_parametro_condizionante": 4,
      "tipo_condizione": "EQUALS",
      "id_valore_condizione_predefinita": 158,
      "ordine_valutazione": 2
    }
  ],
  "risultati": [
    {
      "id_parametro_effetto": 5,
      "tipo_effetto": "SHOW"
    }
  ]
}
```

### Esempio 2: Filtra Valori di Connessione
```json
{
  "nome_regola": "Filtra Connessioni per Impianto A",
  "descrizione": "Mostra solo connessioni compatibili con Impianto A",
  "attiva": true,
  "logica_combinazione_condizioni": "AND",
  "condizioni": [
    {
      "id_parametro_condizionante": 3,
      "tipo_condizione": "EQUALS",
      "id_valore_condizione_predefinita": 154,
      "ordine_valutazione": 1
    },
    {
      "id_parametro_condizionante": 4,
      "tipo_condizione": "EQUALS",
      "id_valore_condizione_predefinita": 158,
      "ordine_valutazione": 2
    }
  ],
  "risultati": [
    {
      "id_parametro_effetto": 5,
      "tipo_effetto": "FILTER_VALUES",
      "id_valori_effetto_predefiniti": [160, 161]
    }
  ]
}
```

## Processo di Valutazione

### 1. Input
- Lista dei parametri attualmente selezionati con i loro valori
- Formato: `[{parameter_id: 1, value_id: 156}, ...]`

### 2. Elaborazione
1. **Caricamento regole**: Recupera tutte le regole attive
2. **Valutazione condizioni**: Per ogni regola, verifica se tutte le condizioni sono soddisfatte
3. **Applicazione logica**: Usa AND/OR per combinare le condizioni
4. **Raccolta risultati**: Se la regola è soddisfatta, raccoglie i parametri da mostrare

### 3. Output
- Lista dei parametri dipendenti che devono essere mostrati
- Include tutti i dettagli del parametro e i suoi valori disponibili

## Best Practices

### Progettazione Regole
1. **Nomi descrittivi**: Usa nomi che spiegano chiaramente cosa fa la regola
2. **Ordine valutazione**: Imposta ordini logici per le condizioni
3. **Regole atomiche**: Preferisci regole semplici e specifiche
4. **Test incrementale**: Testa ogni regola singolarmente prima di combinarle

### Performance
1. **Regole attive**: Disattiva regole non utilizzate
2. **Condizioni efficienti**: Metti le condizioni più selettive per prime
3. **Evita cicli**: Non creare dipendenze circolari tra parametri

### Manutenibilità
1. **Documentazione**: Aggiungi descrizioni dettagliate
2. **Versionamento**: Traccia le modifiche alle regole
3. **Testing**: Verifica il comportamento dopo ogni modifica

## Troubleshooting

### Problemi Comuni

#### Regola non si attiva
- ✅ Verifica che la regola sia attiva
- ✅ Controlla che tutte le condizioni siano soddisfatte
- ✅ Verifica i tipi di condizione e i valori
- ✅ Controlla la logica di combinazione (AND/OR)

#### Parametro non appare
- ✅ Verifica che il tipo di effetto sia "SHOW"
- ✅ Controlla che il parametro effetto sia attivo
- ✅ Verifica che non ci siano regole conflittuali

#### Valori filtrati non corretti
- ✅ Controlla i valori specificati in `id_valori_effetto_predefiniti`
- ✅ Verifica che i valori esistano e siano attivi
- ✅ Controlla che non ci siano regole multiple che filtrano lo stesso parametro

### Debug
1. **Log del backend**: Attiva i log per vedere quali regole vengono valutate
2. **Console frontend**: Verifica le chiamate API e le risposte
3. **Test isolato**: Testa le regole singolarmente
4. **Verifica dati**: Controlla che parametri e valori esistano nel database