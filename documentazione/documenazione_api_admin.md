Assolutamente! Ecco il nuovo documento di documentazione API che riflette lo stato implementato delle tue `admin routes`, inclusi esempi di payload e risposte.

---

# Documentazione API di Amministrazione Backend

Questo documento descrive gli endpoint dell'API di amministrazione implementati nel blueprint `admin_bp`. Queste API consentono la gestione delle entità principali del sistema, quali `Parametri`, `ValoriParametro`, `Utenti` e `RegoleDipendenzaComplessa`.

## 1. Introduzione

Le API di amministrazione sono progettate per essere utilizzate da un'interfaccia utente di backend (GUI) e forniscono funzionalità CRUD (Create, Read, Update, Delete) complete. Tutti gli endpoint sono protetti e richiedono autenticazione tramite token JWT.

## 2. Autenticazione

Tutti gli endpoint descritti in questo documento richiedono un **token JWT di accesso valido** nell'header `Authorization`. Il formato dell'header deve essere:

`Authorization: Bearer <token_di_accesso>`

Se il token non è fornito, non è valido o è scaduto, la risposta sarà un `401 Unauthorized`. La gestione dei token di accesso e refresh è demandata al modulo di autenticazione (es. `auth_bp`).

## 3. Sezioni API

### 3.1. CRUD Parametri

Gestione dei parametri configurabili del sistema.

#### 3.1.1. Ottieni tutti i Parametri

*   **Endpoint**: `GET /admin/parametri`
*   **Descrizione**: Recupera un elenco di tutti i parametri presenti nel sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: Nessuno
*   **Risposte**:
    *   `200 OK`: Elenco di oggetti parametro.
        ```json
        [
            {
                "id_parametro": 1,
                "nome_parametro": "Lavorazione",
                "tipo_controllo_ui": "SELECT",
                "descrizione": "Tipo di lavorazione principale",
                "foto": "images/parametri/1/lavorazione.png",
                "is_root": true,
                "ordine_visualizzazione": 1,
                "attivo": true,
                "data_creazione": "2023-10-26T10:00:00",
                "data_modifica": "2023-10-26T10:00:00"
            },
            {
                "id_parametro": 2,
                "nome_parametro": "Materiale",
                "tipo_controllo_ui": "RADIO_BUTTONS",
                "descrizione": "Materiale utilizzato per la lavorazione",
                "foto": null,
                "is_root": false,
                "ordine_visualizzazione": 7,
                "attivo": true,
                "data_creazione": "2023-10-26T10:05:00",
                "data_modifica": "2023-10-26T10:05:00"
            }
        ]
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.1.2. Ottieni Parametri per Select (ID e Nome)

*   **Endpoint**: `GET /admin/parametri/all-for-select`
*   **Descrizione**: Recupera un elenco semplificato di tutti i parametri, contenente solo `id_parametro` e `nome_parametro`, utile per i campi di selezione (dropdown).
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: Nessuno
*   **Risposte**:
    *   `200 OK`: Elenco di oggetti parametro semplificati.
        ```json
        [
            {
                "id_parametro": 1,
                "nome_parametro": "Lavorazione"
            },
            {
                "id_parametro": 2,
                "nome_parametro": "Materiale"
            }
        ]
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.1.3. Ottieni Valori per Select di un Parametro Specifico

*   **Endpoint**: `GET /admin/parametri/<int:id_parametro>/valori-for-select`
*   **Descrizione**: Recupera un elenco semplificato dei valori associati a un parametro specifico, contenente solo `id_valore_parametro` e `testo_visualizzato_ui`, utile per i campi di selezione. Restituisce una lista vuota se il parametro non esiste o non ha valori.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del parametro di cui recuperare i valori.
*   **Risposte**:
    *   `200 OK`: Elenco di oggetti valore parametro semplificati.
        ```json
        [
            {
                "id_valore_parametro": 101,
                "testo_visualizzato_ui": "Elemento Singolo"
            },
            {
                "id_valore_parametro": 102,
                "testo_visualizzato_ui": "Ponte"
            }
        ]
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.1.4. Crea un nuovo Parametro

*   **Endpoint**: `POST /admin/parametri`
*   **Descrizione**: Crea un nuovo parametro nel sistema. Supporta l'upload di una foto associata.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (Form Data - `multipart/form-data`)
    *   `nome_parametro`: `string`, Obbligatorio. Il nome del parametro.
    *   `tipo_controllo_ui`: `string`, Obbligatorio. Il tipo di controllo UI (es. `"TEXT_INPUT"`, `"SELECT"`, `"CHECKBOX"`, `"RADIO_BUTTONS"`, `"TEXTAREA"`, `"NUMBER_INPUT"`, `"COLOR_PICKER"`, ecc.).
    *   `descrizione`: `string`, Opzionale. Descrizione del parametro.
    *   `is_root`: `boolean`, Opzionale. Indica se il parametro è un parametro radice (default: `false`). Deve essere inviato come stringa "true" o "false".
    *   `attivo`: `boolean`, Opzionale. Indica se il parametro è attivo (default: `true`). Deve essere inviato come stringa "true" o "false".
    *   `foto`: `file`, Opzionale. Il file immagine da associare al parametro.
*   **Esempio di Richiesta (Form Data)**:
    ```
    Content-Type: multipart/form-data; boundary=---WebKitFormBoundary7MA4YWxkTrZu0gW

    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="nome_parametro"

    Colore Dentina
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="tipo_controllo_ui"

    COLOR_PICKER
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="descrizione"

    Colore standard della dentina del dente.
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="is_root"

    false
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="attivo"

    true
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="foto"; filename="dentin_color.png"
    Content-Type: image/png

    (binary image data)
    ---WebKitFormBoundary7MA4YWxkTrZu0gW--
    ```
*   **Risposte**:
    *   `201 Created`: Parametro creato con successo.
        ```json
        {
            "id_parametro": 123
        }
        ```
    *   `400 Bad Request`: Se `nome_parametro` o `tipo_controllo_ui` sono mancanti o vuoti.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.1.5. Ottieni un Parametro Specifico

*   **Endpoint**: `GET /admin/parametri/<int:id_parametro>`
*   **Descrizione**: Recupera i dettagli di un singolo parametro tramite il suo ID.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del parametro da recuperare.
*   **Risposte**:
    *   `200 OK`: Dettagli del parametro.
        ```json
        {
            "id_parametro": 1,
            "nome_parametro": "Lavorazione",
            "tipo_controllo_ui": "SELECT",
            "descrizione": "Tipo di lavorazione principale",
            "foto": "images/parametri/1/lavorazione.png",
            "is_root": true,
            "attivo": true,
            "data_creazione": "2023-10-26T10:00:00",
            "data_modifica": "2023-10-26T10:00:00"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il parametro specificato non esiste.

#### 3.1.6. Aggiorna un Parametro Esistente

*   **Endpoint**: `PUT /admin/parametri/<int:id_parametro>`
*   **Descrizione**: Aggiorna i dettagli di un parametro esistente. Supporta l'upload di una nuova foto. Accetta un aggiornamento parziale.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (Form Data - `multipart/form-data`)
    *   `id_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del parametro da aggiornare.
    *   `nome_parametro`: `string`, Opzionale. Il nuovo nome del parametro.
    *   `tipo_controllo_ui`: `string`, Opzionale. Il nuovo tipo di controllo UI.
    *   `descrizione`: `string`, Opzionale. Nuova descrizione.
    *   `is_root`: `boolean`, Opzionale. Nuovo stato di root ("true" o "false").
    *   `attivo`: `boolean`, Opzionale. Nuovo stato attivo ("true" o "false").
    *   `foto`: `file`, Opzionale. Il nuovo file immagine.
*   **Esempio di Richiesta (Form Data)**:
    ```
    Content-Type: multipart/form-data; boundary=---WebKitFormBoundary7MA4YWxkTrZu0gW

    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="nome_parametro"

    Lavorazione Principale Aggiornata
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="attivo"

    false
    ---WebKitFormBoundary7MA4YWxkTrZu0gW--
    ```
*   **Risposte**:
    *   `200 OK`: Parametro aggiornato con successo.
        ```json
        {
            "message": "Parametro aggiornato"
        }
        ```
    *   `400 Bad Request`: Se `nome_parametro` o `tipo_controllo_ui` sono forniti ma vuoti.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il parametro specificato non esiste.

#### 3.1.7. Elimina un Parametro

*   **Endpoint**: `DELETE /admin/parametri/<int:id_parametro>`
*   **Descrizione**: Elimina un parametro dal sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del parametro da eliminare.
*   **Risposte**:
    *   `200 OK`: Parametro eliminato con successo.
        ```json
        {
            "message": "Parametro eliminato"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il parametro specificato non esiste.

### 3.2. CRUD ValoriParametro

Gestione dei valori associati ai parametri.

#### 3.2.1. Ottieni tutti i Valori Parametro

*   **Endpoint**: `GET /admin/valoriparametro`
*   **Descrizione**: Recupera un elenco di tutti i valori dei parametri presenti nel sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: Nessuno
*   **Risposte**:
    *   `200 OK`: Elenco di oggetti valore parametro.
        ```json
        [
            {
                "id_valore_parametro": 101,
                "id_parametro": 1,
                "testo_visualizzato_ui": "Elemento Singolo",
                "ordine_visualizzazione": 1,
                "foto": "images/valoriparametro/101/singolo.png",
                "colore": "#F0F0F0",
                "descrizione": "Descrizione per elemento singolo",
                "data_creazione": "2023-10-26T10:00:00",
                "data_modifica": "2023-10-26T10:00:00"
            },
            {
                "id_valore_parametro": 102,
                "id_parametro": 1,
                "testo_visualizzato_ui": "Ponte",
                "ordine_visualizzazione": 2,
                "foto": "images/valoriparametro/102/ponte.png",
                "colore": null,
                "descrizione": null,
                "data_creazione": "2023-10-26T10:01:00",
                "data_modifica": "2023-10-26T10:01:00"
            }
        ]
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.2.2. Crea un nuovo Valore Parametro

*   **Endpoint**: `POST /admin/valoriparametro`
*   **Descrizione**: Crea un nuovo valore per un parametro specifico. Supporta l'upload di una foto.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (Form Data - `multipart/form-data`)
    *   `id_parametro`: `integer`, Obbligatorio. L'ID del parametro a cui associare questo valore.
    *   `testo_visualizzato_ui`: `string`, Obbligatorio. Il testo visualizzato nell'interfaccia utente per questo valore.
    *   `ordine_visualizzazione`: `integer`, Opzionale. L'ordine di visualizzazione (default: `0`).
    *   `colore`: `string`, Opzionale. Un codice colore (es. "#FFFFFF").
    *   `descrizione`: `string`, Opzionale. Descrizione del valore.
    *   `foto`: `file`, Opzionale. Il file immagine da associare al valore.
*   **Esempio di Richiesta (Form Data)**:
    ```
    Content-Type: multipart/form-data; boundary=---WebKitFormBoundary7MA4YWxkTrZu0gW

    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="id_parametro"

    2
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="testo_visualizzato_ui"

    Zirconia
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="ordine_visualizzazione"

    1
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="descrizione"

    Materiale in Zirconia
    ---WebKitFormBoundary7MA4YWxkTrZu0gW--
    ```
*   **Risposte**:
    *   `201 Created`: Valore parametro creato con successo.
        ```json
        {
            "id_valore_parametro": 201
        }
        ```
    *   `400 Bad Request`: Se `id_parametro` o `testo_visualizzato_ui` sono mancanti.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.2.3. Ottieni un Valore Parametro Specifico

*   **Endpoint**: `GET /admin/valoriparametro/<int:id_valore_parametro>`
*   **Descrizione**: Recupera i dettagli di un singolo valore parametro tramite il suo ID.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_valore_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del valore parametro da recuperare.
*   **Risposte**:
    *   `200 OK`: Dettagli del valore parametro.
        ```json
        {
            "id_valore_parametro": 101,
            "id_parametro": 1,
            "testo_visualizzato_ui": "Elemento Singolo",
            "ordine_visualizzazione": 1,
            "foto": "images/valoriparametro/101/singolo.png",
            "colore": "#F0F0F0",
            "descrizione": "Descrizione per elemento singolo",
            "data_creazione": "2023-10-26T10:00:00",
            "data_modifica": "2023-10-26T10:00:00"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il valore parametro specificato non esiste.

#### 3.2.4. Aggiorna un Valore Parametro Esistente

*   **Endpoint**: `PUT /admin/valoriparametro/<int:id_valore_parametro>`
*   **Descrizione**: Aggiorna i dettagli di un valore parametro esistente. Supporta l'upload di una nuova foto. Accetta un aggiornamento parziale.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (Form Data - `multipart/form-data`)
    *   `id_valore_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del valore parametro da aggiornare.
    *   `testo_visualizzato_ui`: `string`, Opzionale. Il nuovo testo visualizzato.
    *   `ordine_visualizzazione`: `integer`, Opzionale. Il nuovo ordine di visualizzazione.
    *   `colore`: `string`, Opzionale. Il nuovo codice colore.
    *   `descrizione`: `string`, Opzionale. Nuova descrizione.
    *   `foto`: `file`, Opzionale. Il nuovo file immagine.
*   **Esempio di Richiesta (Form Data)**:
    ```
    Content-Type: multipart/form-data; boundary=---WebKitFormBoundary7MA4YWxkTrZu0gW

    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="testo_visualizzato_ui"

    Zirconia Aggiornata
    ---WebKitFormBoundary7MA4YWxkTrZu0gW
    Content-Disposition: form-data; name="colore"

    #AABBCC
    ---WebKitFormBoundary7MA4YWxkTrZu0gW--
    ```
*   **Risposte**:
    *   `200 OK`: Valore parametro aggiornato con successo.
        ```json
        {
            "message": "ValoreParametro aggiornato"
        }
        ```
    *   `400 Bad Request`: Se `testo_visualizzato_ui` è fornito ma vuoto.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il valore parametro specificato non esiste.

#### 3.2.5. Elimina un Valore Parametro

*   **Endpoint**: `DELETE /admin/valoriparametro/<int:id_valore_parametro>`
*   **Descrizione**: Elimina un valore parametro dal sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_valore_parametro` (Path Parameter): `integer`, Obbligatorio. L'ID del valore parametro da eliminare.
*   **Risposte**:
    *   `200 OK`: Valore parametro eliminato con successo.
        ```json
        {
            "message": "ValoreParametro eliminato"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se il valore parametro specificato non esiste.

### 3.3. CRUD Utenti

Gestione degli utenti del sistema.

#### 3.3.1. Ottieni tutti gli Utenti

*   **Endpoint**: `GET /admin/utenti`
*   **Descrizione**: Recupera un elenco paginato di tutti gli utenti. Supporta filtri per username ed email.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (Query Parameters)
    *   `page`: `integer`, Opzionale. Numero di pagina (default: `1`).
    *   `per_page`: `integer`, Opzionale. Numero di elementi per pagina (default: `10`).
    *   `username`: `string`, Opzionale. Filtra per username (ricerca parziale case-insensitive).
    *   `email`: `string`, Opzionale. Filtra per email (ricerca parziale case-insensitive).
*   **Risposte**:
    *   `200 OK`: Elenco paginato di oggetti utente.
        ```json
        {
            "items": [
                {
                    "id_utente": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "nome_completo": "Amministratore di Sistema",
                    "attivo": true,
                    "data_creazione": "2023-10-26T09:00:00",
                    "data_ultima_modifica": "2023-10-26T11:30:00"
                },
                {
                    "id_utente": 2,
                    "username": "editor",
                    "email": "<EMAIL>",
                    "nome_completo": "Utente Editor",
                    "attivo": true,
                    "data_creazione": "2023-10-26T10:15:00",
                    "data_ultima_modifica": "2023-10-26T10:15:00"
                }
            ],
            "total": 2,
            "page": 1,
            "per_page": 10,
            "pages": 1
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.3.2. Crea un nuovo Utente

*   **Endpoint**: `POST /admin/utenti`
*   **Descrizione**: Crea un nuovo utente nel sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (JSON Body)
    *   `username`: `string`, Obbligatorio. Username unico per l'utente.
    *   `password`: `string`, Obbligatorio. Password dell'utente.
    *   `email`: `string`, Obbligatorio. Email unica dell'utente.
    *   `nome_completo`: `string`, Opzionale. Nome completo dell'utente.
    *   `attivo`: `boolean`, Opzionale. Indica se l'utente è attivo (default: `true`).
*   **Esempio di Richiesta (JSON Body)**:
    ```json
    {
        "username": "nuovo_utente",
        "password": "Password123!",
        "email": "<EMAIL>",
        "nome_completo": "Nuovo Utente Test",
        "attivo": true
    }
    ```
*   **Risposte**:
    *   `201 Created`: Utente creato con successo.
        ```json
        {
            "message": "Utente creato con successo",
            "id_utente": 3
        }
        ```
    *   `400 Bad Request`: Se `username`, `password` o `email` sono mancanti, o se username/email esistono già.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.3.3. Ottieni un Utente Specifico

*   **Endpoint**: `GET /admin/utenti/<int:id_utente>`
*   **Descrizione**: Recupera i dettagli di un singolo utente tramite il suo ID.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_utente` (Path Parameter): `integer`, Obbligatorio. L'ID dell'utente da recuperare.
*   **Risposte**:
    *   `200 OK`: Dettagli dell'utente.
        ```json
        {
            "id_utente": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "nome_completo": "Amministratore di Sistema",
            "attivo": true,
            "data_creazione": "2023-10-26T09:00:00",
            "data_ultima_modifica": "2023-10-26T11:30:00"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se l'utente specificato non esiste.

#### 3.3.4. Aggiorna un Utente Esistente

*   **Endpoint**: `PUT /admin/utenti/<int:id_utente>`
*   **Descrizione**: Aggiorna i dettagli di un utente esistente. Accetta un aggiornamento parziale.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (JSON Body)
    *   `id_utente` (Path Parameter): `integer`, Obbligatorio. L'ID dell'utente da aggiornare.
    *   `password`: `string`, Opzionale. La nuova password dell'utente.
    *   `email`: `string`, Opzionale. La nuova email dell'utente.
    *   `nome_completo`: `string`, Opzionale. Il nuovo nome completo dell'utente.
    *   `attivo`: `boolean`, Opzionale. Il nuovo stato attivo dell'utente.
*   **Esempio di Richiesta (JSON Body)**:
    ```json
    {
        "email": "<EMAIL>",
        "attivo": false
    }
    ```
*   **Risposte**:
    *   `200 OK`: Utente aggiornato con successo.
        ```json
        {
            "message": "Utente aggiornato con successo"
        }
        ```
    *   `400 Bad Request`: Se l'email è già in uso da un altro utente.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se l'utente specificato non esiste.

#### 3.3.5. Elimina un Utente

*   **Endpoint**: `DELETE /admin/utenti/<int:id_utente>`
*   **Descrizione**: Elimina un utente dal sistema.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_utente` (Path Parameter): `integer`, Obbligatorio. L'ID dell'utente da eliminare.
*   **Risposte**:
    *   `200 OK`: Utente eliminato con successo.
        ```json
        {
            "message": "Utente eliminato con successo"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se l'utente specificato non esiste.

### 3.4. CRUD RegoleDipendenzaComplessa

Gestione delle regole di dipendenza complesse. Queste regole definiscono come i valori di un parametro possono dipendere da combinazioni di condizioni su altri parametri.

**Definizione dei Tipi di Condizione e Effetto:**

*   **`tipo_condizione`**: Definisce l'operatore di confronto per una condizione. Esempi comuni includono:
    *   `EQUALS`: Il valore del parametro condizionante è uguale al valore specificato.
    *   `NOT_EQUALS`: Il valore del parametro condizionante non è uguale al valore specificato.
    *   `CONTAINS`: Il valore libero del parametro condizionante contiene il valore specificato (utile per TEXT_INPUT).
    *   `GREATER_THAN`: Il valore libero del parametro condizionante è maggiore del valore specificato (utile per NUMBER_INPUT).
    *   `LESS_THAN`: Il valore libero del parametro condizionante è minore del valore specificato (utile per NUMBER_INPUT).
    *   `IS_SELECTED`: Il valore del parametro condizionante è selezionato (per campi SELECT, CHECKBOX, RADIO).
    *   `IS_NOT_SELECTED`: Il valore del parametro condizionante non è selezionato.
*   **`tipo_effetto`**: Definisce l'azione da intraprendere su un parametro di effetto. Esempi comuni includono:
    *   `SET_VALUE`: Imposta un valore predefinito o libero per il parametro di effetto.
    *   `ENABLE`: Abilita il parametro di effetto.
    *   `DISABLE`: Disabilita il parametro di effetto.
    *   `SHOW`: Rende visibile il parametro di effetto.
    *   `HIDE`: Nasconde il parametro di effetto.
    *   `FILTER_VALUES`: Filtra i valori disponibili per il parametro di effetto per includere solo quelli specificati nei risultati.

#### 3.4.1. Ottieni tutte le Regole di Dipendenza

*   **Endpoint**: `GET /admin/dependency-rules`
*   **Descrizione**: Recupera un elenco di tutte le regole di dipendenza complesse, incluse le loro condizioni e risultati nidificati.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: Nessuno
*   **Risposte**:
    *   `200 OK`: Elenco di oggetti regola di dipendenza complessa.
        ```json
        [
            {
                "id": 1,
                "nome_regola": "Regola Lavorazione Ponte Materiale Zirconia",
                "descrizione": "Regola per abilitare la scelta del colore quando la lavorazione è 'Ponte' e il materiale è 'Zirconia'",
                "attiva": true,
                "logica_combinazione_condizioni": "AND",
                "condizioni": [
                    {
                        "id": 10,
                        "id_regola": 1,
                        "id_parametro_condizionante": 1,  // ID del parametro 'Lavorazione'
                        "id_valore_condizione_predefinita": 102, // ID del valore 'Ponte'
                        "valore_condizione_libero": null,
                        "tipo_condizione": "EQUALS",
                        "ordine_valutazione": 0
                    },
                    {
                        "id": 11,
                        "id_regola": 1,
                        "id_parametro_condizionante": 2,  // ID del parametro 'Materiale'
                        "id_valore_condizione_predefinita": 201, // ID del valore 'Zirconia'
                        "valore_condizione_libero": null,
                        "tipo_condizione": "EQUALS",
                        "ordine_valutazione": 1
                    }
                ],
                "risultati": [
                    {
                        "id": 20,
                        "id_regola": 1,
                        "id_parametro_effetto": 3,  // ID del parametro 'Colore Dentina'
                        "tipo_effetto": "SHOW",
                        "id_valore_effetto_predefinito": null,
                        "valore_effetto_libero": null
                    },
                    {
                        "id": 21,
                        "id_regola": 1,
                        "id_parametro_effetto": 3,  // ID del parametro 'Colore Dentina'
                        "tipo_effetto": "FILTER_VALUES",
                        "id_valore_effetto_predefinito": 305, // ID del valore 'Bianco Zirconia'
                        "valore_effetto_libero": null
                    }
                ]
            }
        ]
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.4.2. Crea una nuova Regola di Dipendenza

*   **Endpoint**: `POST /admin/dependency-rules`
*   **Descrizione**: Crea una nuova regola di dipendenza complessa, incluse le sue condizioni e risultati. I `condizioni.id` e `risultati.id` non devono essere forniti nel payload di creazione, poiché vengono generati dal server.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (JSON Body)
    *   `nome_regola`: `string`, Obbligatorio. Nome univoco della regola.
    *   `descrizione`: `string`, Opzionale. Descrizione della regola.
    *   `attiva`: `boolean`, Opzionale. Stato attivo della regola (default: `true`).
    *   `logica_combinazione_condizioni`: `string`, Opzionale. Logica di combinazione delle condizioni (`"AND"` o `"OR"`, default: `"AND"`).
    *   `condizioni`: `array` di oggetti `CondizioniRegola`, Opzionale.
        *   `id_parametro_condizionante`: `integer`, Obbligatorio. ID del parametro che è la sorgente della condizione.
        *   `id_valore_condizione_predefinita`: `integer`, Opzionale, `null` se si usa `valore_condizione_libero`. ID di un `ValoreParametro` predefinito per la condizione.
        *   `valore_condizione_libero`: `string`, Opzionale, `null` se si usa `id_valore_condizione_predefinita`. Valore testuale o numerico per la condizione (es. per TEXT_INPUT, NUMBER_INPUT).
        *   `tipo_condizione`: `string`, Obbligatorio. Tipo di condizione (es. `"EQUALS"`, `"NOT_EQUALS"`, `"GREATER_THAN"`).
        *   `ordine_valutazione`: `integer`, Opzionale. Ordine di valutazione della condizione all'interno della regola (default: `0`).
    *   `risultati`: `array` di oggetti `RisultatiRegola`, Opzionale.
        *   `id_parametro_effetto`: `integer`, Obbligatorio. ID del parametro su cui questa regola ha un effetto.
        *   `tipo_effetto`: `string`, Obbligatorio. Tipo di effetto (es. `"SHOW"`, `"HIDE"`, `"SET_VALUE"`, `"FILTER_VALUES"`).
        *   `id_valore_effetto_predefinito`: `integer`, Opzionale, `null` se si usa `valore_effetto_libero`. ID di un `ValoreParametro` da applicare o filtrare.
        *   `valore_effetto_libero`: `string`, Opzionale, `null` se si usa `id_valore_effetto_predefinito`. Valore testuale o numerico libero da applicare (es. per TEXT_INPUT).
*   **Esempio di Richiesta (JSON Body)**:
    ```json
    {
        "nome_regola": "Abilita Finitura Superficie per Zirconia",
        "descrizione": "Abilita il parametro 'Finitura Superficie' se il materiale è Zirconia",
        "attiva": true,
        "logica_combinazione_condizioni": "AND",
        "condizioni": [
            {
                "id_parametro_condizionante": 2,          // Parametro 'Materiale'
                "id_valore_condizione_predefinita": 201,  // Valore 'Zirconia'
                "tipo_condizione": "EQUALS"
            }
        ],
        "risultati": [
            {
                "id_parametro_effetto": 4,               // Parametro 'Finitura Superficie'
                "tipo_effetto": "SHOW"
            },
            {
                "id_parametro_effetto": 4,
                "tipo_effetto": "FILTER_VALUES",
                "id_valore_effetto_predefinito": 403     // Valore 'Lucida'
            },
            {
                "id_parametro_effetto": 4,
                "tipo_effetto": "FILTER_VALUES",
                "id_valore_effetto_predefinito": 404     // Valore 'Opaca'
            }
        ]
    }
    ```
*   **Risposte**:
    *   `201 Created`: Regola di dipendenza creata con successo.
        ```json
        {
            "message": "Regola di dipendenza complessa creata con successo",
            "id": 123
        }
        ```
    *   `400 Bad Request`: Se i dati di input non sono forniti.
    *   `422 Unprocessable Entity`: Se i dati forniti non sono validi secondo lo schema (es. campi obbligatori mancanti, tipi errati).
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

#### 3.4.3. Ottieni una Regola di Dipendenza Specifica

*   **Endpoint**: `GET /admin/dependency-rules/<int:id_regola>`
*   **Descrizione**: Recupera i dettagli di una singola regola di dipendenza complessa tramite il suo ID, incluse le sue condizioni e risultati.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_regola` (Path Parameter): `integer`, Obbligatorio. L'ID della regola da recuperare.
*   **Risposte**:
    *   `200 OK`: Dettagli della regola di dipendenza. Struttura identica all'output di `GET /admin/dependency-rules` ma con un singolo oggetto.
        ```json
        {
            "id": 1,
            "nome_regola": "Regola Lavorazione Ponte Materiale Zirconia",
            "descrizione": "Regola per abilitare la scelta del colore quando la lavorazione è 'Ponte' e il materiale è 'Zirconia'",
            "attiva": true,
            "logica_combinazione_condizioni": "AND",
            "condizioni": [
                {
                    "id": 10,
                    "id_regola": 1,
                    "id_parametro_condizionante": 1,
                    "id_valore_condizione_predefinita": 102,
                    "valore_condizione_libero": null,
                    "tipo_condizione": "EQUALS",
                    "ordine_valutazione": 0
                },
                {
                    "id": 11,
                    "id_regola": 1,
                    "id_parametro_condizionante": 2,
                    "id_valore_condizione_predefinita": 201,
                    "valore_condizione_libero": null,
                    "tipo_condizione": "EQUALS",
                    "ordine_valutazione": 1
                }
            ],
            "risultati": [
                {
                    "id": 20,
                    "id_regola": 1,
                    "id_parametro_effetto": 3,
                    "tipo_effetto": "SHOW",
                    "id_valore_effetto_predefinito": null,
                    "valore_effetto_libero": null
                },
                {
                    "id": 21,
                    "id_regola": 1,
                    "id_parametro_effetto": 3,
                    "tipo_effetto": "FILTER_VALUES",
                    "id_valore_effetto_predefinito": 305,
                    "valore_effetto_libero": null
                }
            ]
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se la regola specificata non esiste.

#### 3.4.4. Aggiorna una Regola di Dipendenza Esistente

*   **Endpoint**: `PUT /admin/dependency-rules/<int:id_regola>`
*   **Descrizione**: Aggiorna i dettagli di una regola di dipendenza complessa esistente. Questo endpoint supporta aggiornamenti parziali per i campi diretti della regola. **Per le condizioni e i risultati, l'aggiornamento è atomico: tutte le condizioni e i risultati esistenti associati alla regola verranno eliminati e sostituiti con quelli forniti nel payload.**
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: (JSON Body)
    *   `id_regola` (Path Parameter): `integer`, Obbligatorio. L'ID della regola da aggiornare.
    *   `nome_regola`: `string`, Opzionale. Il nuovo nome della regola.
    *   `descrizione`: `string`, Opzionale. Nuova descrizione.
    *   `attiva`: `boolean`, Opzionale. Nuovo stato attivo.
    *   `logica_combinazione_condizioni`: `string`, Opzionale. Nuova logica di combinazione.
    *   `condizioni`: `array` di oggetti `CondizioniRegola`, Opzionale. Se fornito, sostituisce completamente le condizioni esistenti. I `id` di `CondizioniRegola` non devono essere presenti nel payload.
    *   `risultati`: `array` di oggetti `RisultatiRegola`, Opzionale. Se fornito, sostituisce completamente i risultati esistenti. I `id` di `RisultatiRegola` non devono essere presenti nel payload.
*   **Esempio di Richiesta (JSON Body - Aggiornamento parziale e sostituzione condizioni/risultati)**:
    ```json
    {
        "nome_regola": "Regola Lavorazione Ponte (Aggiornata)",
        "attiva": false,
        "condizioni": [
            {
                "id_parametro_condizionante": 1,          // Parametro 'Lavorazione'
                "id_valore_condizione_predefinita": 102,  // Valore 'Ponte'
                "tipo_condizione": "EQUALS"
            },
            {
                "id_parametro_condizionante": 5,          // Nuovo parametro 'Età Paziente'
                "valore_condizione_libero": "18",
                "tipo_condizione": "GREATER_THAN"
            }
        ],
        "risultati": [
            {
                "id_parametro_effetto": 3,               // Parametro 'Colore Dentina'
                "tipo_effetto": "HIDE"
            }
        ]
    }
    ```
*   **Risposte**:
    *   `200 OK`: Regola di dipendenza aggiornata con successo.
        ```json
        {
            "message": "Regola di dipendenza complessa aggiornata con successo"
        }
        ```
    *   `400 Bad Request`: Se i dati di input non sono forniti.
    *   `422 Unprocessable Entity`: Se i dati forniti non sono validi secondo lo schema.
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se la regola specificata non esiste.

#### 3.4.5. Elimina una Regola di Dipendenza

*   **Endpoint**: `DELETE /admin/dependency-rules/<int:id_regola>`
*   **Descrizione**: Elimina una regola di dipendenza complessa dal sistema, incluse tutte le sue condizioni e risultati associati (grazie alla configurazione delle relazioni nel database).
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**:
    *   `id_regola` (Path Parameter): `integer`, Obbligatorio. L'ID della regola da eliminare.
*   **Risposte**:
    *   `200 OK`: Regola di dipendenza eliminata con successo.
        ```json
        {
            "message": "Regola di dipendenza complessa eliminata con successo"
        }
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.
    *   `404 Not Found`: Se la regola specificata non esiste.

### 3.5. Home API Admin

*   **Endpoint**: `GET /admin/`
*   **Descrizione**: Endpoint di base per le API di amministrazione.
*   **Autenticazione**: Richiesta (JWT)
*   **Parametri di Richiesta**: Nessuno
*   **Risposte**:
    *   `200 OK`: Messaggio di benvenuto.
        ```
        "API Admin Home"
        ```
    *   `401 Unauthorized`: Se il token JWT non è fornito o non è valido.

## 4. Modifiche Recenti e Note Implementative

### 4.1. Campo `ordine_visualizzazione` per Parametri

**Data implementazione**: 2025-06-27

È stato aggiunto il campo `ordine_visualizzazione` alla tabella `parametri` per garantire un ordine stabile di visualizzazione dei parametri nell'interfaccia utente.

#### Caratteristiche:
- **Tipo**: `INTEGER NOT NULL DEFAULT 0`
- **Scopo**: Definisce l'ordine di visualizzazione dei parametri nell'UI
- **Ordinamento predefinito**:
  - Lavorazioni: 1 (parametro root)
  - Tipologia dente: 2
  - Tipologia Impianto: 3
  - Tecnica: 4
  - Connessione: 5
  - Diametro: 6
  - Altri parametri: ID + 100

#### Impatto sulle API:
- Tutti gli endpoint che restituiscono parametri ora includono il campo `ordine_visualizzazione`
- Le API v1 ordinano automaticamente i parametri per questo campo
- Le API admin permettono di modificare questo valore tramite i normali endpoint CRUD

#### Esempio di utilizzo:
```json
{
  "id_parametro": 4,
  "nome_parametro": "Tecnica",
  "tipo_controllo_ui": "SELECT",
  "ordine_visualizzazione": 4,
  "is_root": false,
  "attivo": true
}
```

### 4.2. Ordinamento Automatico nelle API v1

Le seguenti API v1 ora restituiscono sempre i parametri ordinati per `ordine_visualizzazione`:
- `GET /api/v1/parameters/root`
- `GET /api/v1/parameters/{id}/initial-config`
- `POST /api/v1/dependency_evaluation/evaluate`

Questo garantisce un ordine stabile nell'interfaccia utente, indipendentemente da quando i parametri vengono aggiunti o rimossi dalle regole di dipendenza.

### 4.3. Migrazione Database

È stata creata la migrazione `6d24f4c5fffc_add_ordine_visualizzazione_to_parametri.py` che:
1. Aggiunge il campo `ordine_visualizzazione` alla tabella `parametri`
2. Imposta valori iniziali logici basati sui nomi dei parametri
3. Fornisce un rollback per rimuovere il campo se necessario

### 4.4. Compatibilità

- **Backward compatibility**: Mantenuta per tutte le API esistenti
- **Frontend**: Il frontend ora riceve parametri sempre ordinati correttamente
- **Database**: Nessun impatto sulle query esistenti, solo aggiunta di un nuovo campo

### 4.5. Gestione delle Dipendenze - Correzioni

**Problema risolto**: Il parametro "Connessione" non compariva quando si selezionava "Incollato" come tecnica.

**Causa**: Mancava una regola di dipendenza per mostrare il parametro "Connessione" quando la tecnica è "Incollato".

**Soluzione**: È stata aggiunta la regola mancante tramite lo script `fix_incollato_rule.py` che crea una regola equivalente a quella esistente per "Avvitato".

**Regola aggiunta**:
- **Nome**: "Mostra Connessione per Tipologia e Tecnica - Incollato"
- **Condizioni**: Tipologia Impianto HAS_VALUE AND Tecnica = "Incollato"
- **Risultati**: FILTER_VALUES Connessione -> "Interna", "M.U.A. originale"