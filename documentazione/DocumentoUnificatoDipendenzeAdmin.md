# Progetto: Gestione Dipendenze Parametri Multifattoriali e Implementazione GUI Amministrazione Backend

## 1. Introduzione

Questo documento unificato descrive le modifiche necessarie al backend per supportare la gestione di valori di parametri che dipendono da *condizioni multiple* (logica "AND") basate su altri parametri e i loro valori. L'obiettivo è fornire maggiore flessibilità nella configurazione delle logiche di visualizzazione lato frontend.

Inoltre, delinea l'approccio per lo sviluppo di un'unica GUI di amministrazione backend, che sarà realizzataCTX_ASK_CLARIFICATION_TURN utilizzando **React** come framework frontend e **HeroUI** per la componentistica dell'interfaccia utente. L'obiettivo è creare un'esperienza utente altamente intuitiva, performante e usabile per la gestione di tutte le entità del backend, inclusi dati complessi come le "Regole di Dipendenza Complessa", tabelle esistenti come `Parametri` e `ValoriParametro`, e la gestione sicura degli accessi tramite **autenticazione utente**.

Si evidenzia che la tabella `Lavorazioni` verrà eliminata, e le sue funzionalità saranno integrate nella tabella `Parametri` come parametro "root" (`is_root: TRUE`). Considerando che siamo in fase di progettazione e sviluppo e che i dati attuali sono solo di test, il database verrà ricreato da capo. **Le migrazioni Alembic esistenti verranno eliminate e si partirà da una nuova migrazione iniziale.**

## 2. Architettura Proposta per la Gestione delle Dipendenze e Utenti

Per implementare le dipendenze multifattoriali, includere la "Lavorazione" come parametro "root", e gestire l'autenticazione, si propone l'introduzione delle seguenti tabelle:

*   **`utenti`**: Memorizza le credenziali e le informazioni degli amministratori.
    *   Campi chiave: `id_utente` (PK), `username` (UNIQUE), `hashed_password`, `email` (UNIQUE, opzionale), `nome_completo` (opzionale), `attivo` (BOOLEAN), `data_creazione`, `data_ultima_modifica`.
*   **`parametri`**: Definisce le opzioni configurabili e i parametri radice (ex Lavorazioni).
    *   Campi chiave: `id_parametro` (PK), `nome_parametro`, `is_root`, `tipo_controllo_ui`, `foto` (OPTIONAL), `data_creazione`, `data_modifica`.
*   **`valoriparametro`**: Contiene i valori selezionabili per ciascun parametro.
    *   Campi chiave: `id_valore_parametro` (PK), `id_parametro` (FK), `testo_visualizzato_ui`, `foto` (OPTIONAL), `colore` (OPTIONAL), `descrizione` (OPTIONAL), `data_creazione`, `data_modifica`.
*   **`regole_dipendenza_complessa`**: Definisce una regola di dipendenza che lega un parametro dipendente a un insieme di condizioni e ai valori che devono essere resi disponibili.
    *   Campi chiave: `id_regola_dipendenza_complessa` (PK), `id_parametro_dipendente` (FK verso `parametri`), `nome_regola`, `descrizione`, `attiva`.
*   **`condizioni_regola`**: Associa uno specifico `valore_parametro_condizionante` a una `regola_dipendenza_complessa`. Ogni riga qui rappresenta una singola condizione "AND".
    *   Campi chiave: `id_condizione_regola` (PK), `id_regola_dipendenza_complessa` (FK), `id_valore_parametro_condizionante` (FK verso `valoriparametro`).
*   **`risultati_regola`**: Associa i `valori_parametro_risultato` che devono essere visualizzati per il `parametro_dipendente` quando la `regola_dipendenza_complessa` è soddisfatta.
    *   Campi chiave: `id_risultato_regola` (PK), `id_regola_dipendenza_complessa` (FK), `id_valore_parametro_risultato` (FK verso `valoriparametro`).

Le tabelle `parametrodipendenze` e `parametrovaloridipendenze` verranno sostituite. La tabella `Lavorazioni` verrà eliminata.

### Schema del Database (Mermaid ER Diagram)

```mermaid
erDiagram
    utenti {
        INT id_utente PK
        VARCHAR username UK
        VARCHAR hashed_password
        VARCHAR email UK "Opzionale, per notifiche o recupero password"
        VARCHAR nome_completo OPTIONAL
        BOOLEAN attivo "Indica se l'utente può loggarsi"
        DATETIME data_creazione
        DATETIME data_ultima_modifica
    }

    parametri {
        INT id_parametro PK
        VARCHAR nome_parametro
        BOOLEAN is_root  "Indica se è un parametro 'radice' (ex Lavorazione)"
        VARCHAR tipo_controllo_ui
        VARCHAR foto OPTIONAL
        DATETIME data_creazione
        DATETIME data_modifica
    }

    valoriparametro {
        INT id_valore_parametro PK
        INT id_parametro FK
        VARCHAR testo_visualizzato_ui
        VARCHAR foto OPTIONAL
        VARCHAR colore OPTIONAL
        TEXT descrizione OPTIONAL
        DATETIME data_creazione
        DATETIME data_modifica
    }

    regole_dipendenza_complessa {
        INT id_regola_dipendenza_complessa PK
        INT id_parametro_dipendente FK "Parametro i cui valori sono influenzati da questa regola"
        VARCHAR nome_regola
        VARCHAR descrizione OPTIONAL
        BOOLEAN attiva "Indica se la regola è attualmente attiva"
    }

    condizioni_regola {
        INT id_condizione_regola PK
        INT id_regola_dipendenza_complessa FK "Regola a cui questa condizione appartiene"
        INT id_valore_parametro_condizionante FK "Valore del parametro che funge da condizione"
    }

    risultati_regola {
        INT id_risultato_regola PK
        INT id_regola_dipendenza_complessa FK "Regola che definisce questo risultato"
        INT id_valore_parametro_risultato FK "Valore del parametro dipendente da mostrare se la regola è attiva"
    }

    parametri ||--o{ valoriparametro : "ha valori"
    parametri ||--o{ regole_dipendenza_complessa : "è il parametro dipendente di"
    regole_dipendenza_complessa ||--o{ condizioni_regola : "contiene condizioni (AND)"
    regole_dipendenza_complessa ||--o{ risultati_regola : "definisce risultati"
    valoriparametro ||--|| condizioni_regola : "è valore condizionante in"
    valoriparametro ||--|| risultati_regola : "è valore risultato in"
```

## 3. Struttura della GUI di Amministrazione React con HeroUI

La GUI sarà composta da un'architettura modulare di componenti React.

*   **`App`**: Componente radice che gestisce il routing principale e lo stato di autenticazione globale.
*   **`AuthLayout`**: Layout per le pagine di autenticazione (es. Login).
*   **`AdminLayout`**: Layout principale per l'area amministrativa (contiene `Sidebar`, `Navbar`, `Footer` di HeroUI). Sarà accessibile solo ad utenti autenticati.
    *   **`AdminDashboard`**: Pagina principale dopo il login, parte di `AdminLayout`.
*   **`LoginPage`**: Pagina con il form di login per accedere all'area amministrativa.
*   **`GenericListView`**: Componente riutilizzabile per visualizzare liste di record (es. `ParametersList`, `ValuesList`, `RulesList`), inclusi i parametri "root".
    *   **Componenti UI di HeroUI**: `Table`, `Pagination`, `Input` (ricerca/filtro), `Button` ("Nuovo", "Elimina").
    *   **Props**: `modelName`, `columnsConfig`, `data`, `onEdit`, `onDelete`, `onNew`.
    *   **Logica**: Fetching dati, paginazione, ordinamento, ricerca, stato caricamento, gestione errori.
*   **`GenericFormView`**: Componente riutilizzabile per i form di creazione e modifica (es. `ParameterForm`, `ValueForm`).
    *   **Componenti UI di HeroUI**: `Input`, `Textarea`, `Select`, `Checkbox`, `Radio`, `DatePicker`.
    *   **Props**: `modelName`, `recordId` (modifica), `formConfig`, `onSaveSuccess`, `onCancel`.
    *   **Stato Locale**: Valori campi, stato caricamento/salvataggio, errori validazione.
    *   **Logica**: Popolamento form, validazione, invio dati.
*   **`RuleForm`**: Componente specifico per la creazione e modifica di `regole_dipendenza_complessa`.
    *   Gestirà campi base della regola (`nome_regola`, `descrizione`, `attiva`, `id_parametro_dipendente`).
    *   Sezione per `condizioni_regola` (logica "AND"): interfaccia visuale (lista dinamica di "chip") per aggiungere/rimuovere/modificare condizioni.
        *   **Stack**: HeroUI, `react-hook-form` con `useFieldArray`, Zod.
        *   **`ConditionChip`**: Elemento UI compatto per parametro e valore condizionante.
        *   **`ConditionList`**: Contenitore per `ConditionChip`.
    *   Sezione per `risultati_regola`: `MultiSelect` o `Checkbox.Group` (HeroUI) per selezionare `id_valore_parametro_risultato`.
    *   **Logica**: Salvataggio/aggiornamento di `regole_dipendenza_complessa` e relative `condizioni_regola`, `risultati_regola`.

## 4. Gestione dello Stato in React

*   **Stato Locale (hooks `useState`, `useReducer`, `react-hook-form`)**: Per form, UI temporanea.
*   **Stato Globale/Autenticazione (`useContext` + `useReducer` o Zustand)**:
    *   Gestione stato autenticazione: token JWT, info utente (`id_utente`, `username`, `nome_completo`).
    *   Provider autenticazione con metodi `login`, `logout`, stato corrente.
*   **Stato Cache API (TanStack Query / SWR)**:
    *   Caching dati globali (liste `Parametri`, `ValoriParametro`).
    *   Gestione chiamate API CRUD protette, invalidazione cache.

## 5. Interazione con le API Backend

La GUI interagirà con API RESTful. **In ambiente di produzione, la comunicazione HTTPS sarà gestita da un reverse proxy; l'applicazione Flask interna potrà comunicare via HTTP.**

### API di Autenticazione (es. `/api/auth`)

*   **`POST /api/auth/login`**:
    *   Input: `{ username, password }`
    *   Output (successo): `{ access_token: "jwt_token_string", user: { id, username, nome_completo } }`
    *   Output (errore): `401 Unauthorized`
*   **`POST /api/auth/logout`**: (Opzionale, per blacklist token server-side)
    *   Richiede token JWT. Invalida il token.
*   **`GET /api/auth/me`**: (Opzionale, per recuperare info utente)
    *   Richiede token JWT. Output: `{ user: { id, username, nome_completo } }`

### API di Amministrazione Protette (es. `/api/admin/`)

Tutti gli endpoint CRUD richiederanno un token JWT valido (`Authorization: Bearer <access_token>`). Risposte `401 Unauthorized` o `403 Forbidden` per accessi non validi.

*   **CRUD per `Parametri`**: `GET /api/admin/parametri`, `GET /api/admin/parametri/{id}`, `POST /api/admin/parametri`, `PUT /api/admin/parametri/{id}`, `DELETE /api/admin/parametri/{id}`.
*   **CRUD per `ValoriParametro`**: Simili a `Parametri`.
*   **CRUD per `RegoleDipendenzaComplessa` (es. `/api/admin/dependency-rules`)**:
    *   `POST` e `PUT` payload includeranno dettagli regola, array `id_valore_parametro_condizionante`, array `id_valore_parametro_risultato`.
*   **Endpoint di Supporto**:
    *   `GET /api/admin/parametri/all-for-select`: Per dropdown.
    *   `GET /api/admin/parametri/{id_parametro}/valori-for-select`: Per dropdown.
*   **CRUD per `Utenti` (super-admin)**: `GET /api/admin/utenti`, `POST /api/admin/utenti`, `PUT /api/admin/utenti/{id}`, `DELETE /api/admin/utenti/{id}`.

## 6. Routing Frontend

Si utilizzerà `React Router`.

*   `/login`: Pagina di login (`LoginPage` in `AuthLayout`).
*   `/admin`: Radice area amministrativa protetta.
    *   `/admin/dashboard`: Schermata principale.
    *   `/admin/parametri`, `/admin/parametri/new`, `/admin/parametri/:id/edit`.
    *   `/admin/valoriparametro` (o `/admin/parametri/:parametroId/valori`), `/admin/valoriparametro/new`, `/admin/valoriparametro/:id/edit`.
    *   `/admin/dependency-rules` (o `regole-dipendenza`), `/admin/dependency-rules/new`, `/admin/dependency-rules/:id/edit`.
    *   `/admin/utenti`, `/admin/utenti/new`, `/admin/utenti/:id/edit`.
*   **`ProtectedRoute`**: HOC/wrapper per verificare autenticazione e reindirizzare a `/login`.

## 7. Utilizzo di HeroUI per una UX Migliorata

HeroUI sarà fondamentale per:

*   **Campi Relazionali**: `Select` con ricerca (`SearchableSelect`, `Autocomplete`) per FK.
*   **Campi Data/Ora**: `DatePicker` (se editabili).
*   **Tipo Controllo UI**: `Select` o `Radio.Group` per enum.
*   **Validazione in Tempo Reale**: Integrazione con `Yup`/`Zod` + `React Hook Form`.
*   **Upload Immagini**: Componenti `FileUpload` per `foto`. Endpoint API dedicato.
*   **Color Picker**: Per campo `colore`.
*   **Liste Dinamiche e Selezione Multipla**: Per `condizioni_regola` e `risultati_regola` nel `RuleForm`.
*   **Feedback utente**: `Toast`/`Notification`, `Spinner`/`Loading`.
*   **Layout Responsive**.

## 8. Modifiche al Backend (Flask)

*   [`app/models.py`](app/models.py):
    *   **Aggiungere modello `Utente`**:
        ```python
        from werkzeug.security import generate_password_hash, check_password_hash
        # from .extensions import db # Assumendo che db sia definito in extensions.py

        class Utente(db.Model):
            __tablename__ = 'utenti'
            id_utente = db.Column(db.Integer, primary_key=True)
            username = db.Column(db.String(80), unique=True, nullable=False)
            hashed_password = db.Column(db.String(200), nullable=False)
            email = db.Column(db.String(120), unique=True, nullable=True)
            nome_completo = db.Column(db.String(120), nullable=True)
            attivo = db.Column(db.Boolean, default=True, nullable=False)
            data_creazione = db.Column(db.DateTime, default=db.func.current_timestamp())
            data_ultima_modifica = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

            def set_password(self, password):
                self.hashed_password = generate_password_hash(password)

            def check_password(self, password):
                return check_password_hash(self.hashed_password, password)

            def __repr__(self):
                return f'<Utente {self.username}>'
        ```
    *   **Modello `Parametri`**:
        *   Aggiungere `foto` (String, nullable).
        *   Rimuovere `codice_parametro`, `validazione_regex`, `placeholder_ui`.
        *   Aggiungere `is_root` (Boolean, default FALSE).
    *   **Modello `ValoriParametro`**:
        *   Rimuovere `valore_memorizzato`.
        *   Aggiungere `foto` (String, nullable), `colore` (String, nullable), `descrizione` (Text, nullable).
    *   Rimuovere `Lavorazioni`, `LavorazioneParametriConfig`, `ParametriPadreFiglio`.
    *   Aggiungere `RegolaDipendenzaComplessa`, `CondizioniRegola`, `RisultatiRegola`.
    *   Rimuovere `ParametroDipendenze`, `ParametroValoriDipendenze`.
    *   Aggiornare relazioni `db.relationship`.
*   **Gestione File Immagini**: Cartelle `public/images/parametri/{id_parametro}/` e `public/images/valoriparametro/{id_valore_parametro}/`.
*   **Installare Flask-JWT-Extended**: `pip install Flask-JWT-Extended` e configurarlo.
*   **Nuovo Modulo/Blueprint per Autenticazione (es. `app/auth/routes.py`)**: Implementare `/login`, opzionali `/logout`, `/refresh_token`.
*   **Protezione Endpoint Admin**: Usare `@jwt_required()` di Flask-JWT-Extended.
*   **Gestione Password**: Usare `werkzeug.security` o `passlib`.
*   [`seed_data.py`](seed_data.py): Aggiornare per creare utente admin di default e dati per nuove tabelle/campi.
    ```python
    # Esempio per seed_data.py
    # from app.models import Utente, db # Adattare import
    # def create_admin_user():
    #     if not Utente.query.filter_by(username='admin').first():
    #         admin_user = Utente(username='admin', email='<EMAIL>', nome_completo='Admin User', attivo=True)
    #         admin_user.set_password('ScegliUnaPasswordSicura123!') # CAMBIARE QUESTA PASSWORD
    #         db.session.add(admin_user)
    #         print("Utente admin creato.")
    # # Chiamare create_admin_user() nel seed principale
    ```
*   **Configurazione Flask**: Aggiungere `JWT_SECRET_KEY` alla configurazione.
*   **Endpoint API Admin (`app/api_admin/routes.py`)**: Implementare CRUD per nuove entità e aggiornare esistenti.
*   **Endpoint API App (`app/api_v1/routes.py`)**:
    Questi endpoint sono destinati all'applicazione utente finale. Dovranno essere implementati o significativamente modificati per supportare il nuovo sistema di dipendenze complesse con caricamento progressivo.

    1.  **`GET /api/v1/get_root_parameters`** (Nuovo o Sostituisce parte di `configurazione-iniziale`)
        *   **Scopo**: Fornire al frontend i parametri iniziali da cui l'utente comincia la configurazione.
        *   **Logica**:
            *   Identifica tutti i parametri con `is_root = TRUE`.
            *   Per ciascun parametro root:
                *   Recupera le sue proprietà (ID, nome, tipo_controllo_ui, foto).
                *   Recupera tutti i suoi `ValoriParametro` associati (con tutte le loro proprietà: ID, testo, foto, colore, descrizione).
                *   **Calcola dinamicamente il flag `has_dependent_children`** (vedi sottosezione "Calcolo Dinamico del Flag `has_dependent_children`").
        *   **Output (Esempio JSON)**:
            ```json
            {
              "root_parameters": [
                {
                  "id_parametro": 1,
                  "nome_parametro": "Lavorazione",
                  "is_root": true,
                  "tipo_controllo_ui": "select",
                  "has_dependent_children": true, // Calcolato dal backend
                  "foto": null,
                  "valori_parametro": [
                    { "id_valore_parametro": 101, "testo_visualizzato_ui": "Elemento Singolo", /* ... */ },
                    { "id_valore_parametro": 102, "testo_visualizzato_ui": "Ponte", /* ... */ }
                  ]
                }
                // ... eventuali altri parametri root
              ]
            }
            ```

    2.  **`POST /api/v1/evaluate_and_get_dependent_parameters`** (Nuovo o Sostituisce `evaluate_dependencies` e parte di `configurazione-iniziale`)
        *   **Scopo**: Valutare le selezioni utente correnti, determinare quali parametri dipendenti diventano attivi o cambiano le loro opzioni, e restituire la loro definizione completa con valori filtrati.
        *   **Input (Esempio JSON)**:
            ```json
            {
              "current_selections": [
                { "id_parametro": 1, "id_valore_parametro": 101 },
                { "id_parametro": 2, "id_valore_parametro": 205 }
                // ... altre selezioni attive
              ]
            }
            ```
        *   **Logica**:
            *   Per ogni `RegolaDipendenzaComplessa` attiva:
                *   Verificare se tutte le sue `CondizioniRegola` sono soddisfatte dalle `current_selections`.
                *   Se una regola è soddisfatta, il suo `id_parametro_dipendente` diventa "attivo". I `RisultatiRegola` associati determinano quali `ValoriParametro` sono disponibili per questo parametro dipendente.
            *   Costruire una lista di parametri che dovrebbero essere "visibili/attivi" per il frontend:
                *   Includere i parametri dalle `current_selections` (con le loro opzioni complete, a meno che non siano ulteriormente filtrate da altre regole che li riguardano come dipendenti).
                *   Per ogni `id_parametro_dipendente` attivato dalle regole:
                    *   Recuperare le sue proprietà (ID, nome, tipo_controllo_ui, foto).
                    *   Determinare la lista dei suoi `ValoriParametro` validi (basati sui `RisultatiRegola` aggregati da tutte le regole soddisfatte che lo influenzano). Se nessuna regola lo condiziona ma i suoi "genitori" sono selezionati, tutti i suoi valori sono validi.
                    *   **Calcolare dinamicamente il flag `has_dependent_children`** per questo parametro.
            *   La risposta dovrebbe contenere solo i parametri che sono rilevanti (attivi o selezionati) e le loro opzioni valide. Se un parametro precedentemente attivo non è più rilevante o non ha opzioni valide, non dovrebbe essere incluso (o segnalato come tale).
        *   **Output (Esempio JSON)**:
            ```json
            {
              "active_parameters_definition": [
                { // Parametro selezionato dall'utente (dalla richiesta)
                  "id_parametro": 1,
                  "nome_parametro": "Lavorazione",
                  "is_root": true,
                  "tipo_controllo_ui": "select",
                  "has_dependent_children": true,
                  "valori_parametro": [ /* tutti i valori del root */ ],
                  "valore_selezionato_id": 101 // Opzionale: conferma selezione
                },
                { // Nuovo parametro attivato o aggiornato
                  "id_parametro": 2,
                  "nome_parametro": "Tipologia Impianto",
                  "is_root": false,
                  "tipo_controllo_ui": "select_searchable",
                  "has_dependent_children": true,
                  "valori_parametro": [ // Lista GIA' FILTRATA dei valori validi
                    { "id_valore_parametro": 201, "testo_visualizzato_ui": "Alpha-Bio", /*...*/ },
                    { "id_valore_parametro": 205, "testo_visualizzato_ui": "Alpha-Dent", /*...*/ }
                  ]
                },
                { // Altro parametro
                  "id_parametro": 3,
                  "nome_parametro": "Colore Capsula",
                  "is_root": false,
                  "tipo_controllo_ui": "color_picker",
                  "has_dependent_children": false, // Esempio di parametro foglia
                  "valori_parametro": [ /* valori filtrati */ ]
                }
                // ... altri parametri attivi con le loro definizioni e valori filtrati
              ]
              // Opzionale: "updated_parameter_options" se si vuole separare
              // l'aggiornamento delle sole opzioni di parametri già noti al frontend
              // dalla definizione di nuovi parametri. Per semplicità, si può includere
              // tutto in "active_parameters_definition".
            }
            ```
        *   **Nota sulla Struttura della Risposta**: È cruciale che il backend e il frontend concordino se la risposta di questo endpoint sovrascrive completamente la lista dei parametri dipendenti visualizzati o se fornisce aggiornamenti incrementali. L'approccio di "sovrascrittura completa dei parametri attivi" (come suggerito in `active_parameters_definition`) è spesso più semplice da gestire nel frontend.

*   **Calcolo Dinamico del Flag `has_dependent_children` (per API App)**:
    Il flag `has_dependent_children` non è un campo persistito nella tabella `parametri`. Deve essere calcolato dal backend ogni volta che la definizione di un parametro viene serializzata per le API dell'applicazione utente.
    Un parametro `P` ha figli dipendenti (`has_dependent_children: true`) se almeno uno dei suoi `ValoriParametro` è utilizzato come condizione (`id_valore_parametro_condizionante`) in una o più `CondizioniRegola` associate a una `RegolaDipendenzaComplessa` che sia `attiva`.

    **Logica di calcolo per un dato `id_parametro_corrente`:**
    1.  Ottenere tutti gli `id_valore_parametro` associati a `id_parametro_corrente`.
    2.  Verificare se esiste almeno una riga nella tabella `condizioni_regola` (`CR`) tale che:
        *   `CR.id_valore_parametro_condizionante` sia uno degli ID valore del `id_parametro_corrente`.
        *   La `regole_dipendenza_complessa` (`RDC`) associata a `CR.id_regola_dipendenza_complessa` (tramite `RDC.id_regola_dipendenza_complessa == CR.id_regola_dipendenza_complessa`) abbia `RDC.attiva = TRUE`.
    3.  Se tale condizione è soddisfatta, il parametro `id_parametro_corrente` ha figli dipendenti.

    **Esempio concettuale con SQLAlchemy (da implementare nella serializzazione del parametro):**
    ```python
    # from app.models import Parametri, ValoriParametro, CondizioniRegola, RegolaDipendenzaComplessa, db
    # # all'interno della funzione che prepara i dati di un parametro per l'API v1

    # parameter_id = # ID del parametro in questione

    # Subquery per ottenere gli ID dei valori del parametro corrente
    valori_del_parametro_ids = db.session.query(ValoriParametro.id_valore_parametro).filter(ValoriParametro.id_parametro == parameter_id)

    # Query per verificare l'esistenza di condizioni attive legate a questi valori
    has_children = db.session.query(
        db.exists().where(
            db.and_(
                CondizioniRegola.id_valore_parametro_condizionante.in_(valori_del_parametro_ids),
                RegolaDipendenzaComplessa.id_regola_dipendenza_complessa == CondizioniRegola.id_regola_dipendenza_complessa,
                RegolaDipendenzaComplessa.attiva == True
            )
        )
    ).scalar() # Restituisce True o False

    # Aggiungere al payload del parametro per il frontend:
    # serialized_parameter_data['has_dependent_children'] = has_children
    ```
    Questa logica dovrà essere integrata nelle route che servono le definizioni dei parametri all'applicazione utente (es. in `app/api_v1/routes.py`).

## 9. Migrazioni con Alembic e Setup Iniziale del Database

Dato che il database verrà ricreato da zero:

1.  **Pulizia Ambiente Migrazioni Esistente**:
    *   Eliminare la cartella `migrations/versions/` (o file al suo interno).
    *   (Opzionale) Eliminare tabella `alembic_version` dal DB.
2.  **Pulizia Database Esistente**:
    *   Eliminare manualmente tutte le tabelle dal database di sviluppo.
3.  **Aggiornamento Modelli**: `app/models.py` deve contenere la definizione finale.
4.  **Inizializzazione Migrazioni Alembic** (se non già fatto):
    *   `flask db init` (se `migrations/` non esiste).
    *   Modificare `migrations/env.py` per importare metadati (`target_metadata = db.metadata`).
5.  **Creazione Migrazione Iniziale**:
    *   `flask db migrate -m "Creazione schema iniziale database"`
    *   Revisionare file di migrazione generato: verificare `op.create_table()` per tutte le tabelle.
6.  **Applicazione Migrazione Iniziale**:
    *   `flask db upgrade`
7.  **Popolamento Dati Iniziali**: Eseguire `seed_data.py` o comando Flask dedicato.

## 10. Passi di Implementazione per la GUI Frontend

1.  Setup Progetto React e installazione dipendenze (`react-router-dom`, `TanStack Query`, `react-hook-form`, `zod`, HeroUI).
2.  **Sviluppare Componenti Autenticazione**: `LoginPage`, servizio/hook login, logica salvataggio JWT.
3.  **Implementare Stato Globale Autenticazione**: Context API per `currentUser`, `token`, `isAuthenticated`, `login()`, `logout()`.
4.  Configurare **routing frontend** con `ProtectedRoute`.
5.  Sviluppare `AdminLayout` e `AdminDashboard`.
6.  Sviluppare `GenericListView`, `GenericFormView`.
7.  Implementare viste per `Parametri`, `ValoriParametro`.
8.  Sviluppare `RuleForm` (usando `useFieldArray` per condizioni/risultati).
9.  Integrare HeroUI.
10. Implementare fetching dati con `TanStack Query`, inviando token JWT (es. con interceptor Axios/Fetch).
11. Implementare **Logout**: Pulire token, resettare stato, reindirizzare.
12. (Opzionale) Implementare gestione utenti UI.
13. Validazione robusta e gestione errori.

## 11. Considerazioni Aggiuntive e Suggerimenti

*   **Sicurezza JWT**:
    *   `JWT_SECRET_KEY` sicura (variabili d'ambiente).
    *   Scadenza ragionevole per token (access token brevi, refresh token lunghi se usati).
    *   Considerare revoca token (blacklist) per scenari critici (complessità aggiuntiva).
*   **Configurazione Reverse Proxy**: Assicurarsi che inoltri header corretti (`X-Forwarded-For`, `X-Forwarded-Proto`) se Flask ne ha bisogno.
*   **Password Policy**: Forzare complessità password admin.
*   **Gestione Utenti Iniziale**: `seed_data.py` o comando CLI per primo utente. Registrazione self-service tipicamente non abilitata per admin.
*   **Coerenza Nomi API**: Standardizzare nomi (es. `dependency-rules`, `conditions`, `results`).
*   **Atomicità Operazioni su Regole**: Operazioni su `RegolaDipendenzaComplessa`, `CondizioniRegola`, `RisultatiRegola` dovrebbero essere atomiche (transazioni backend).
*   **Documentazione API**: Generare/mantenere documentazione OpenAPI/Swagger per API admin.

```

**Come salvare il file:**

1.  **Copia tutto il testo** sopra, partendo da `# Progetto: ...` fino alla fine.
2.  Apri un editor di testo semplice (come Notepad su Windows, TextEdit su Mac in modalità testo semplice, VS Code, Sublime Text, Atom, etc.).
3.  **Incolla il testo** copiato nell'editor.
4.  Vai su "File" -> "Salva con nome..." (o "Save As...").
5.  Scegli una cartella dove salvare il file.
6.  Nel campo "Nome file" (o "File name"), digita un nome per il file, assicurandoti che termini con l'estensione `.md`. Ad esempio: `Progetto_Backend_Admin_GUI.md`.
7.  Nel campo "Tipo file" (o "Save as type"), se disponibile, seleziona "Tutti i file (\*.\*)" o "All Files" per evitare che l'editor aggiunga automaticamente un'estensione `.txt`.
8.  Clicca su "Salva".

