# Buone Pratiche TypeScript e Zod per la Gestione dei Form

Questo documento illustra problematiche comuni riscontrate con le validazioni dei form e la gestione dei tipi in TypeScript, particolarmente quando si utilizzano librerie come React Hook Form e Zod. Vengono fornite le procedure per mitigare questi errori e promuovere buone pratiche di programmazione.

## 1. Problematiche Iniziali

Durante lo sviluppo frontend, è frequente incontrare discrepanze tra i tipi di dati gestiti dal form (che riflettono gli input HTML spesso generici come `string | undefined`) e i tipi di dati attesi dalle API (che richiedono formati specifici come `number`, `boolean` o `null`).

### Casi Comuni di Mismatch:

-   **`unknown` da `z.preprocess`**: L'uso improprio di `z.preprocess` può portare Zod a inferire un tipo `unknown` per un campo, causando incompatibilità con le interfacce TypeScript che si aspettano un tipo più specifico (es. `boolean`). Questo genera errori come:
    `Il tipo 'unknown' non è assegnabile al tipo 'boolean'.`
-   **Incompatibilità tra `string` e `number` per gli ID**: Nei form di modifica, gli ID possono essere numeri (dal backend) o stringhe UUID temporanee (per nuovi elementi non ancora salvati). Zod può inferire un `union type` (`string | number | undefined`), che poi non è compatibile con i payload API che si aspettano solo `number | undefined` per gli ID esistenti. Questo porta a errori come:
    `Il tipo 'string' non è assegnabile al tipo 'number'.`
-   **Mancanza di coerenza `null`/`undefined`**: Le API possono accettare `null` per campi opzionali o non selezionati, mentre i campi di input HTML possono generare `''` (stringa vuota) o `undefined`. Una gestione non coerente di questi valori può causare errori di tipo.

## 2. Strategia di Soluzione: Separazione degli Schemi Zod

La soluzione adottata consiste nel definire **due schemi Zod separati** per ogni entità del form:

### a) `FormSchema` (Schema per l'Input del Form)

Questo schema definisce i tipi di dati **esattamente come vengono ricevuti dagli input del form HTML**. Deve essere permissivo e riflettere la realtà dei valori che possono provenire dalla UI.

**Esempio (da `ParameterFormPage.tsx`):**

```typescript
const parameterFormSchema = z.object({
  nome: z.string().min(1, 'Il nome è obbligatorio'),
  descrizione: z.string().optional(),
  tipo: z.string().min(1, 'Il campo tipo è obbligatorio'),
  obbligatorio: z.boolean(), // Per checkbox, React Hook Form gestisce il booleano direttamente
  valore_default: z.string().nullable().optional(), // Stringa o null dal campo input
});

type FormInput = z.infer<typeof parameterFormSchema>;
```

**Esempio (da `DependencyRuleFormPage.tsx` per campi annidati):**

```typescript
const CondizioneRegolaFormSchema = z.object({
  id_condizione: z.union([z.number(), z.string().uuid()]).optional(), // ID numerico o stringa UUID
  parametro_id: z.number().int().positive().nullable(), // Nullable: valore selezionato o null
  valore_parametro_id: z.number().int().positive().nullable(),
});

const RegolaDipendenzaComplessaFormSchema = z.object({
  id_regola: z.union([z.number(), z.string().uuid()]).optional(),
  nome_regola: z.string().min(1, 'Il nome della regola è obbligatorio'),
  condizioni: z.array(CondizioneRegolaFormSchema).min(1, 'Aggiungere almeno una condizione'),
  // ... altri campi e risultati
});

type FormInput = z.infer<typeof RegolaDipendenzaComplessaFormSchema>;
```

**Benefici di `FormSchema`:**
-   Rappresenta accuratamente lo stato del form UI.
-   Facilita l'integrazione con React Hook Form, specialmente per `register` e `defaultValues`.
-   Semplifica la validazione iniziale lato client.

### b) `PayloadSchema` o Trasformazione Esplicita (Schema per il Payload API)

Questo schema o una funzione di trasformazione serve a convertire i dati dal `FormSchema` nel formato **esatto richiesto dal backend per l'invio via API**. Qui vengono gestite le conversioni di tipo (`string` a `number`/`boolean`), la rimozione di campi non necessari e la gestione di `null`/`undefined`.

**Esempio (da `ParameterFormPage.tsx` con `transform`):**

```typescript
const parameterTransformSchema = parameterFormSchema.transform(data => {
  let transformedDefault: string | number | boolean | null = null;
  
  // Logica di trasformazione per valore_default
  if (data.valore_default !== null && data.valore_default !== undefined && data.valore_default !== '') {
    const val = data.valore_default;
    if (val.toLowerCase() === 'true') transformedDefault = true;
    else if (val.toLowerCase() === 'false') transformedDefault = false;
    else if (!isNaN(Number(val)) && !isNaN(parseFloat(val))) transformedDefault = Number(val);
    else transformedDefault = val;
  }
  
  return {
    nome: data.nome,
    descrizione: data.descrizione,
    tipo: data.tipo,
    obbligatorio: data.obbligatorio,
    valore_default: transformedDefault,
  };
});

// Utilizzare in onSubmit:
const onSubmit = (formData: FormInput) => {
  const transformedData = parameterTransformSchema.parse(formData);
  mutation.mutate(transformedData); // Questo sarà di tipo IParameterFormPayload
};
```

**Esempio (da `DependencyRuleFormPage.tsx` con costruzione esplicita del payload):**

```typescript
const CondizioneRegolaPayloadSchema = z.object({
  id_condizione: z.number().optional(), // ID numerico (UUID temporanei rimossi)
  parametro_id: z.number().int().positive(),
  valore_parametro_id: z.number().int().positive().nullable(),
});

const RegolaDipendenzaComplessaPayloadSchema = z.object({
  id_regola: z.number().optional(), // ID numerico (UUID temporanei rimossi)
  nome_regola: z.string().min(1),
  condizioni: z.array(CondizioneRegolaPayloadSchema).min(1),
  // ... altri campi e risultati
});

type ApiPayload = z.infer<typeof RegolaDipendenzaComplessaPayloadSchema>;

// Utilizzare in onSubmit:
const onSubmit = async (data: FormInput) => {
  try {
    const payload: ApiPayload = {
      id_regola: typeof data.id_regola === 'string' ? undefined : data.id_regola, // Gestione UUID
      nome_regola: data.nome_regola,
      condizioni: data.condizioni
        .filter(cond => cond.parametro_id !== null && cond.parametro_id !== undefined) // Rimozione righe incomplete
        .map(cond => ({
          id_condizione: typeof cond.id_condizione === 'string' ? undefined : cond.id_condizione,
          parametro_id: cond.parametro_id!,
          valore_parametro_id: cond.valore_parametro_id,
        })),
      // ... logica simile per i risultati
    };

    // Validazione finale del payload prima dell'invio
    RegolaDipendenzaComplessaPayloadSchema.parse(payload);

    // ... mutazione API
  } catch (err) {
    // ... gestione errori (es. ZodError per validazione payload)
  }
};
```

**Benefici di `PayloadSchema`/Trasformazione esplicita:**
-   Garantisce che il payload inviato al backend sia correttamente tipizzato e strutturato.
-   Separa la logica di presentazione/input da quella di business/API.
-   Previene errori di runtime dovuti a dati malformati.

## 3. Gestione Coerente di `null` e `undefined`

È fondamentale decidere se usare `null` o `undefined` per i campi opzionali e attenersi a una convenzione.
-   **`undefined`**: Spesso usato per indicare che un campo non è presente o non è stato fornito (es. `optional()` in Zod). Le API GraphQL spesso lo preferiscono per campi non impostati.
-   **`null`**: Spesso usato per indicare l'assenza intenzionale di un valore. Le API REST o database SQL spesso usano `null` per campi che possono essere vuoti.

Nel contesto dei form HTML, i campi `select` o `input type="text"` non riempiti spesso restituiscono `''` (stringa vuota). È necessario trasformare `''` in `null` o `undefined` a seconda delle esigenze dell'API.

**Esempio di gestione `null` per `select` in `onChange`:**

```typescript
// Per un campo select che può essere null
onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value, 10) : null)}
// ... e nel value dell'input React, per mostrare '' quando il valore è null:
value={field.value === null ? '' : field.value || ''}
```

## 4. Principi SOLID applicati

L'adozione di questa strategia di separazione degli schemi Zod rafforza i seguenti principi SOLID:

-   **Principio di Singola Responsabilità (SRP)**: Ogni schema Zod ha una responsabilità chiara: `FormSchema` gestisce l'input della UI, `PayloadSchema` (o la logica di trasformazione) gestisce la preparazione dei dati per l'API. Questo rende il codice più facile da capire e mantenere.
-   **Principio Aperto/Chiuso (OCP)**: L'aggiunta di nuove logiche di trasformazione o nuovi campi può avvenire senza modificare drasticamente gli schemi esistenti, ma estendendo la logica di trasformazione.
-   **Principio di Inversione delle Dipendenze (DIP)**: I componenti del form dipendono da astrazioni (i tipi inferiti dagli schemi Zod) piuttosto che da implementazioni concrete di trasformazione, rendendo il sistema più flessibile.

Seguendo queste pratiche, è possibile minimizzare gli errori di programmazione legati alla gestione dei tipi nei form complessi e migliorare la robustezza del codice frontend.