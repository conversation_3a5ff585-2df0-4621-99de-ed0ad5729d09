### Promemoria: Gestione Componenti HeroUI per Pagine Frontend

Questo documento riassume i passaggi chiave per integrare e utilizzare i componenti HeroUI in una pagina React, basandosi sull'esperienza di modifica di `ParametersListPage.tsx`.

#### 1. **Analisi e Allineamento Dati (API & Interfacce)**
*   **Comprendere l'API**: Prima di iniziare, analizzare attentamente la struttura della risposta dell'API (es. `GET /admin/parametri`). Identificare i nomi esatti dei campi e i loro tipi.
*   **Aggiornare le Interfacce**: Assicurarsi che le interfacce TypeScript (`.ts` o `.d.ts`) nel progetto (es. `IParameter` in `src/types/parameters.ts`) siano perfettamente allineate con i campi e i tipi forniti dall'API. Questo è cruciale per evitare errori di tipo e garantire la coerenza dei dati.

#### 2. **Configurazione Iniziale di HeroUI (se non già presente)**
*   **`HeroUIProvider`**: Se non già fatto, avvolgere l'intera applicazione (o il punto più alto dell'albero dei componenti) con `HeroUIProvider` (es. in `src/main.tsx`). Questo fornisce il contesto necessario per tutti i componenti HeroUI.
    ```tsx
    // src/main.tsx
    import { HeroUIProvider } from '@heroui/react';
    // ...
    root.render(
      <React.StrictMode>
        <HeroUIProvider>
          {/* ... altri provider come ToastProvider */}
          <App />
        </HeroUIProvider>
      </React.StrictMode>
    );
    ```
*   **`ToastProvider`**: Per utilizzare le notifiche toast, includere `ToastProvider` all'interno di `HeroUIProvider` (es. in `src/main.tsx`).
    ```tsx
    // src/main.tsx
    import { ToastProvider } from '@heroui/toast';
    // ...
    root.render(
      <React.StrictMode>
        <HeroUIProvider>
          <ToastProvider /> {/* Posizionare qui */}
          <App />
        </HeroUIProvider>
      </React.StrictMode>
    );
    ```

#### 3. **Importazione e Utilizzo dei Componenti HeroUI**
*   **Importazioni Specifiche**: Importare i componenti HeroUI necessari direttamente dai loro pacchetti (es. `@heroui/react` per `Table`, `Button`, `addToast`).
    ```tsx
    import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Button, addToast } from '@heroui/react';
    ```
*   **Sostituzione Elementi HTML**: Sostituire gli elementi HTML nativi (es. `<table>`, `<button>`, `<div>` per notifiche) con i corrispondenti componenti HeroUI.
    *   **Tabelle**: Utilizzare `Table`, `TableHeader`, `TableColumn`, `TableBody`, `TableRow`, `TableCell`.
        *   Definire le colonne con un array di oggetti `{ key: string, label: string }` e passarle a `TableHeader`.
        *   Gestire lo stato di caricamento e l'assenza di dati tramite le props `isLoading`, `loadingContent` e `emptyContent` di `TableBody`.
        *   Accedere ai dati delle celle utilizzando la `columnKey` e il `param` (es. `(param as any)[columnKey]`).
    *   **Bottoni**: Utilizzare `Button`.
        *   Personalizzare l'aspetto con le props `color`, `variant`, `size` (es. `color="primary"`, `size="sm"`).
        *   Utilizzare `onPress` invece di `onClick` per la gestione degli eventi.
    *   **Notifiche**: Utilizzare la funzione `addToast` per mostrare messaggi di successo/errore.
        *   Specificare `title`, `description` e `color` (es. `color: 'success'`, `color: 'danger'`).

#### 4. **Gestione degli Stati e Logica**
*   **React Query**: Continuare a utilizzare `useQuery` e `useMutation` di `@tanstack/react-query` per la gestione dei dati asincroni (fetch, delete, update).
*   **Notifiche**: Integrare `addToast` nelle callback `onSuccess` e `onError` delle mutazioni per fornire feedback all'utente. Rimuovere la gestione manuale dello stato delle notifiche (`useState`).
*   **Navigazione**: Mantenere `useNavigate` di `react-router-dom` per la navigazione tra le pagine.

#### 5. **Pulizia e Refactoring**
*   **Rimuovere Stili Inutili**: Eliminare gli stili inline o le classi CSS personalizzate che sono state sostituite dagli stili di HeroUI (che spesso si basano su Tailwind CSS).
*   **Verificare le Props**: Controllare la documentazione di HeroUI per le props corrette di ogni componente, specialmente per la gestione di layout e classi (es. `className` invece di `css`).
*   

---

## Buone pratiche per l’uso dei componenti HeroUI con React Hook Form

1. **Usare `Controller` per Input e Checkbox HeroUI**
   Utilizza `<Controller>` invece di `register` per gestire i componenti HeroUI che non espongono una `ref` diretta.

2. **Mappare correttamente `value` e `isSelected`**
   All’interno della funzione `render` di `Controller`, passa `field.value` e `field.onChange` come `value` / `onValueChange` per gli Input e `isSelected` per i Checkbox.

3. **Gestire la validazione nativa e controllata**
   HeroUI supporta prop come `isRequired`, `isInvalid` ed `errorMessage`. Collega questi attributi a `fieldState.error` per mostrare correttamente messaggi di errore.

4. **Impostare `defaultValue` nel Controller**
   Specifica sempre `defaultValue` nel `Controller` per evitare warning e garantire che il campo sia controllato fin dall’inizio.

5. **Evitare la doppia registrazione**
   Non usare `register` insieme a `Controller` per lo stesso campo: può causare conflitti di gestione dello stato.

6. **Combinare controllati e non controllati**
   Usa `register` per input semplici e `Controller` solo per componenti HeroUI o librerie esterne più complesse, per ottimizzare le performance.

7. **Usare `reset` in modo coerente**
   Quando utilizzi `reset`, assicurati che tutti i campi controllati tramite `Controller` abbiano i valori aggiornati, così la UI rifletterà correttamente i dati reimpostati.

---
