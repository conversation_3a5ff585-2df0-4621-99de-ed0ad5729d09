from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, create_refresh_token, get_jwt
from app.models import Cliente
from app.extensions import db
import datetime

client_auth_bp = Blueprint('client_auth', __name__, url_prefix='/api/client/auth')

@client_auth_bp.route('/login', methods=['POST'])
def login():
    """Login per clienti"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"msg": "Dati JSON richiesti"}), 400
            
        username = data.get('username', None)
        password = data.get('password', None)

        if not username or not password:
            return jsonify({"msg": "Username e password sono richiesti"}), 400

        # Cerca il cliente per username o email
        cliente = Cliente.query.filter(
            (Cliente.username == username) | (Cliente.email == username)
        ).first()

        if not cliente:
            return jsonify({"msg": "Credenziali non valide"}), 401
            
        if not cliente.attivo:
            return jsonify({"msg": "Account disattivato. Contattare l'amministratore"}), 401

        if cliente.check_password(password):
            # Aggiorna ultimo accesso
            cliente.ultimo_accesso = datetime.datetime.now()
            db.session.commit()
            
            # Crea token con claim per identificare il tipo di utente
            additional_claims = {
                "user_type": "client",
                "cliente_id": cliente.id_cliente,
                "username": cliente.username
            }
            
            access_token = create_access_token(
                identity=str(cliente.id_cliente),
                additional_claims=additional_claims
            )
            refresh_token = create_refresh_token(
                identity=str(cliente.id_cliente),
                additional_claims=additional_claims
            )
            
            return jsonify({
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user": {
                    "id": cliente.id_cliente,
                    "username": cliente.username,
                    "email": cliente.email,
                    "nome_completo": cliente.nome_completo,
                    "denominazione": cliente.denominazione
                }
            }), 200
        else:
            return jsonify({"msg": "Credenziali non valide"}), 401
            
    except Exception as e:
        return jsonify({"msg": f"Errore interno del server: {str(e)}"}), 500

@client_auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """Logout per clienti"""
    try:
        # Verifica che sia un token cliente
        claims = get_jwt()
        if claims.get('user_type') != 'client':
            return jsonify({"msg": "Token non valido per questo endpoint"}), 403
            
        # Per semplicità, il logout è lato client che rimuove il token
        # In futuro si potrebbe implementare una blacklist server-side
        return jsonify({"msg": "Logout effettuato con successo"}), 200
        
    except Exception as e:
        return jsonify({"msg": f"Errore interno del server: {str(e)}"}), 500

@client_auth_bp.route('/me', methods=['GET'])
@jwt_required()
def me():
    """Ottieni informazioni del cliente corrente"""
    try:
        # Verifica che sia un token cliente
        claims = get_jwt()
        if claims.get('user_type') != 'client':
            return jsonify({"msg": "Token non valido per questo endpoint"}), 403
            
        current_cliente_id = get_jwt_identity()
        cliente = Cliente.query.get(current_cliente_id)
        
        if not cliente:
            return jsonify({"msg": "Cliente non trovato"}), 404
            
        if not cliente.attivo:
            return jsonify({"msg": "Account disattivato"}), 401

        return jsonify({
            "id": cliente.id_cliente,
            "username": cliente.username,
            "email": cliente.email,
            "nome": cliente.nome,
            "cognome": cliente.cognome,
            "nome_completo": cliente.nome_completo,
            "denominazione": cliente.denominazione,
            "ragione_sociale": cliente.ragione_sociale,
            "telefono": cliente.telefono,
            "cellulare": cliente.cellulare,
            "indirizzo": cliente.indirizzo,
            "citta": cliente.citta,
            "cap": cliente.cap,
            "provincia": cliente.provincia,
            "nazione": cliente.nazione,
            "ultimo_accesso": cliente.ultimo_accesso.isoformat() if cliente.ultimo_accesso else None
        }), 200
        
    except Exception as e:
        return jsonify({"msg": f"Errore interno del server: {str(e)}"}), 500

@client_auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """Refresh token per clienti"""
    try:
        # Verifica che sia un token cliente
        claims = get_jwt()
        if claims.get('user_type') != 'client':
            return jsonify({"msg": "Token non valido per questo endpoint"}), 403
            
        current_cliente_id = get_jwt_identity()
        cliente = Cliente.query.get(current_cliente_id)
        
        if not cliente:
            return jsonify({"msg": "Cliente non trovato"}), 404
            
        if not cliente.attivo:
            return jsonify({"msg": "Account disattivato"}), 401
        
        # Crea nuovo access token
        additional_claims = {
            "user_type": "client",
            "cliente_id": cliente.id_cliente,
            "username": cliente.username
        }
        
        new_access_token = create_access_token(
            identity=current_cliente_id,
            additional_claims=additional_claims
        )
        
        return jsonify({"access_token": new_access_token}), 200
        
    except Exception as e:
        return jsonify({"msg": f"Errore interno del server: {str(e)}"}), 500

@client_auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Aggiorna profilo cliente"""
    try:
        # Verifica che sia un token cliente
        claims = get_jwt()
        if claims.get('user_type') != 'client':
            return jsonify({"msg": "Token non valido per questo endpoint"}), 403
            
        current_cliente_id = get_jwt_identity()
        cliente = Cliente.query.get(current_cliente_id)
        
        if not cliente:
            return jsonify({"msg": "Cliente non trovato"}), 404
            
        if not cliente.attivo:
            return jsonify({"msg": "Account disattivato"}), 401

        data = request.get_json()
        if not data:
            return jsonify({"msg": "Dati JSON richiesti"}), 400

        # Campi aggiornabili dal cliente
        updatable_fields = [
            'nome', 'cognome', 'ragione_sociale', 'telefono', 'cellulare',
            'indirizzo', 'citta', 'cap', 'provincia', 'nazione'
        ]
        
        updated = False
        for field in updatable_fields:
            if field in data:
                setattr(cliente, field, data[field])
                updated = True
        
        if updated:
            cliente.data_ultima_modifica = datetime.datetime.now()
            db.session.commit()
            
        return jsonify({
            "msg": "Profilo aggiornato con successo",
            "user": {
                "id": cliente.id_cliente,
                "username": cliente.username,
                "email": cliente.email,
                "nome_completo": cliente.nome_completo,
                "denominazione": cliente.denominazione
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({"msg": f"Errore interno del server: {str(e)}"}), 500