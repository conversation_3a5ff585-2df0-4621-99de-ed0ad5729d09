from marshmallow import Schema, fields, ValidationError, validates, validates_schema, post_load
from app.extensions import ma
from app.models import Cliente
import re

class ClienteCreateSchema(ma.Schema):
    """Schema per la creazione di un nuovo cliente"""
    
    # Campi obbligatori
    username = fields.String(required=True, validate=lambda x: len(x) >= 3)
    email = fields.Email(required=True)
    password = fields.String(required=True, validate=lambda x: len(x) >= 6, load_only=True)
    nome = fields.String(required=True, validate=lambda x: len(x) >= 2)
    cognome = fields.String(required=True, validate=lambda x: len(x) >= 2)
    
    # Campi opzionali
    ragione_sociale = fields.String(allow_none=True)
    partita_iva = fields.String(allow_none=True)
    codice_fiscale = fields.String(allow_none=True)
    
    # Contatti
    telefono = fields.String(allow_none=True)
    cellulare = fields.String(allow_none=True)
    indirizzo = fields.String(allow_none=True)
    citta = fields.String(allow_none=True)
    cap = fields.String(allow_none=True)
    provincia = fields.String(allow_none=True)
    nazione = fields.String(load_default='Italia')
    
    # Dati bancari e pagamento
    iban = fields.String(allow_none=True)
    modalita_pagamento = fields.String(allow_none=True)
    
    # Fatturazione elettronica
    codice_sdi = fields.String(allow_none=True)
    indirizzo_pec = fields.Email(allow_none=True)
    
    # Note
    note = fields.String(allow_none=True)
    
    # Stato
    attivo = fields.Boolean(load_default=True)
    
    @validates('partita_iva')
    def validate_partita_iva(self, value, **kwargs):
        if value and len(value) != 11:
            raise ValidationError('La partita IVA deve essere di 11 caratteri')
        if value and not value.isdigit():
            raise ValidationError('La partita IVA deve contenere solo numeri')
    
    @validates('codice_fiscale')
    def validate_codice_fiscale(self, value, **kwargs):
        if value and len(value) != 16:
            raise ValidationError('Il codice fiscale deve essere di 16 caratteri')
        if value and not re.match(r'^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$', value.upper()):
            raise ValidationError('Formato codice fiscale non valido')
    
    @validates('cap')
    def validate_cap(self, value, **kwargs):
        if value and (len(value) != 5 or not value.isdigit()):
            raise ValidationError('Il CAP deve essere di 5 cifre')
    
    @validates('provincia')
    def validate_provincia(self, value, **kwargs):
        if value and len(value) > 5:
            raise ValidationError('La provincia non può superare i 5 caratteri')
    
    @validates('codice_sdi')
    def validate_codice_sdi(self, value, **kwargs):
        if value and len(value) != 7:
            raise ValidationError('Il codice SDI deve essere di 7 caratteri')
    
    @validates('iban')
    def validate_iban(self, value, **kwargs):
        if value and (len(value) < 15 or len(value) > 34):
            raise ValidationError('IBAN non valido (lunghezza)')
        if value and not re.match(r'^[A-Z]{2}[0-9]{2}[A-Z0-9]+$', value.upper()):
            raise ValidationError('Formato IBAN non valido')

class ClienteUpdateSchema(ma.Schema):
    """Schema per l'aggiornamento di un cliente esistente"""
    
    # Tutti i campi sono opzionali per l'update
    username = fields.String(validate=lambda x: len(x) >= 3)
    email = fields.Email()
    nome = fields.String(validate=lambda x: len(x) >= 2)
    cognome = fields.String(validate=lambda x: len(x) >= 2)
    
    ragione_sociale = fields.String(allow_none=True)
    partita_iva = fields.String(allow_none=True)
    codice_fiscale = fields.String(allow_none=True)
    
    telefono = fields.String(allow_none=True)
    cellulare = fields.String(allow_none=True)
    indirizzo = fields.String(allow_none=True)
    citta = fields.String(allow_none=True)
    cap = fields.String(allow_none=True)
    provincia = fields.String(allow_none=True)
    nazione = fields.String(allow_none=True)
    
    iban = fields.String(allow_none=True)
    modalita_pagamento = fields.String(allow_none=True)
    
    codice_sdi = fields.String(allow_none=True)
    indirizzo_pec = fields.Email(allow_none=True)
    
    note = fields.String(allow_none=True)
    attivo = fields.Boolean()
    
    # Stesse validazioni del create schema
    @validates('partita_iva')
    def validate_partita_iva(self, value, **kwargs):
        if value and len(value) != 11:
            raise ValidationError('La partita IVA deve essere di 11 caratteri')
        if value and not value.isdigit():
            raise ValidationError('La partita IVA deve contenere solo numeri')
    
    @validates('codice_fiscale')
    def validate_codice_fiscale(self, value, **kwargs):
        if value and len(value) != 16:
            raise ValidationError('Il codice fiscale deve essere di 16 caratteri')
        if value and not re.match(r'^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$', value.upper()):
            raise ValidationError('Formato codice fiscale non valido')
    
    @validates('cap')
    def validate_cap(self, value, **kwargs):
        if value and (len(value) != 5 or not value.isdigit()):
            raise ValidationError('Il CAP deve essere di 5 cifre')
    
    @validates('provincia')
    def validate_provincia(self, value, **kwargs):
        if value and len(value) > 5:
            raise ValidationError('La provincia non può superare i 5 caratteri')
    
    @validates('codice_sdi')
    def validate_codice_sdi(self, value, **kwargs):
        if value and len(value) != 7:
            raise ValidationError('Il codice SDI deve essere di 7 caratteri')
    
    @validates('iban')
    def validate_iban(self, value, **kwargs):
        if value and (len(value) < 15 or len(value) > 34):
            raise ValidationError('IBAN non valido (lunghezza)')
        if value and not re.match(r'^[A-Z]{2}[0-9]{2}[A-Z0-9]+$', value.upper()):
            raise ValidationError('Formato IBAN non valido')

class ClienteResponseSchema(ma.Schema):
    """Schema per la risposta con i dati del cliente"""
    
    id_cliente = fields.Integer()
    username = fields.String()
    email = fields.String()
    nome = fields.String()
    cognome = fields.String()
    nome_completo = fields.Method("get_nome_completo")
    denominazione = fields.Method("get_denominazione")
    
    ragione_sociale = fields.String()
    partita_iva = fields.String()
    codice_fiscale = fields.String()
    
    telefono = fields.String()
    cellulare = fields.String()
    indirizzo = fields.String()
    citta = fields.String()
    cap = fields.String()
    provincia = fields.String()
    nazione = fields.String()
    
    iban = fields.String()
    modalita_pagamento = fields.String()
    
    codice_sdi = fields.String()
    indirizzo_pec = fields.String()
    
    note = fields.String()
    attivo = fields.Boolean()
    
    data_creazione = fields.DateTime()
    data_ultima_modifica = fields.DateTime()
    ultimo_accesso = fields.DateTime()
    
    def get_nome_completo(self, obj):
        return obj.nome_completo
    
    def get_denominazione(self, obj):
        return obj.denominazione

class ClienteListSchema(ma.Schema):
    """Schema semplificato per la lista clienti"""
    
    id_cliente = fields.Integer()
    username = fields.String()
    email = fields.String()
    nome_completo = fields.Method("get_nome_completo")
    denominazione = fields.Method("get_denominazione")
    citta = fields.String()
    telefono = fields.String()
    attivo = fields.Boolean()
    data_creazione = fields.DateTime()
    ultimo_accesso = fields.DateTime()
    
    def get_nome_completo(self, obj):
        return obj.nome_completo
    
    def get_denominazione(self, obj):
        return obj.denominazione

# Istanze degli schemi
cliente_create_schema = ClienteCreateSchema()
cliente_update_schema = ClienteUpdateSchema()
cliente_response_schema = ClienteResponseSchema()
clienti_response_schema = ClienteResponseSchema(many=True)
cliente_list_schema = ClienteListSchema()
clienti_list_schema = ClienteListSchema(many=True)