from marshmallow import Schema, fields

class ValoreParametroSchema(Schema):
    id_valore_parametro = fields.Int(dump_only=True)
    testo_visualizzato_ui = fields.Str()
    ordine_visualizzazione = fields.Int()
    foto = fields.Str(allow_none=True)
    colore = fields.Str(allow_none=True)
    descrizione = fields.Str(allow_none=True)

class ParametroSchema(Schema):
    id_parametro = fields.Int(dump_only=True)
    nome_parametro = fields.Str()
    tipo_controllo_ui = fields.Str()
    descrizione = fields.Str(allow_none=True)
    foto = fields.Str(allow_none=True)
    is_root = fields.Bool()
    ordine_visualizzazione = fields.Int()
    attivo = fields.Bool()
    valori = fields.List(fields.Nested(ValoreParametroSchema), dump_only=True)
    has_dependent_children = fields.Bool(dump_only=True)