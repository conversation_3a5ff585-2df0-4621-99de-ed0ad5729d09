from .extensions import db
import enum
from werkzeug.security import generate_password_hash, check_password_hash
import datetime 

class TipoControlloUI(enum.Enum):
    SELECT = "SELECT"
    RADIO = "RADIO"
    CHECKBOX_GROUP = "CHECKBOX_GROUP"
    INPUT_TEXT = "INPUT_TEXT"
    INPUT_NUMBER = "INPUT_NUMBER"
    TEXTAREA = "TEXTAREA"

class Utente(db.Model):
    __tablename__ = 'utente'

    id_utente = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    hashed_password = db.Column(db.String(255), nullable=False)  # Lunghezza aumentata
    email = db.Column(db.String(120), unique=True, nullable=False)
    nome_completo = db.Column(db.String(255), nullable=True)
    attivo = db.Column(db.<PERSON><PERSON><PERSON>, default=True, nullable=False)
    data_creazione = db.Column(db.DateTime, default=datetime.datetime.now)
    data_ultima_modifica = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def set_password(self, password):
        self.hashed_password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.hashed_password, password)

    def __repr__(self):
        return f'<Utente {self.username}>'

class Parametri(db.Model):
    __tablename__ = 'parametri'

    id_parametro = db.Column(db.Integer, primary_key=True, autoincrement=True)
    nome_parametro = db.Column(db.String(255), nullable=False)
    tipo_controllo_ui = db.Column(db.Enum(TipoControlloUI), nullable=False)
    descrizione = db.Column(db.TEXT, nullable=True)
    foto = db.Column(db.String(255), nullable=True) 
    is_root = db.Column(db.Boolean, nullable=False, default=False) 
    ordine_visualizzazione = db.Column(db.Integer, nullable=False, default=0)
    attivo = db.Column(db.Boolean, nullable=False, default=True)
    data_creazione = db.Column(db.TIMESTAMP, server_default=db.func.now())
    data_modifica = db.Column(db.TIMESTAMP, server_default=db.func.now(), onupdate=db.func.now())

    valori = db.relationship("ValoriParametro", back_populates="parametro", cascade="all, delete-orphan")
    
    # Relazioni con le nuove tabelle delle regole di dipendenza complesse
    condizioni_parametro = db.relationship("CondizioniRegola", back_populates="parametro_condizionante", foreign_keys="[CondizioniRegola.id_parametro_condizionante]")
    risultati_parametro = db.relationship("RisultatiRegola", back_populates="parametro_effetto", foreign_keys="[RisultatiRegola.id_parametro_effetto]")

    def __repr__(self):
        return self.nome_parametro

class ValoriParametro(db.Model):
    __tablename__ = 'valoriparametro'

    id_valore_parametro = db.Column(db.Integer, primary_key=True, autoincrement=True)
    id_parametro = db.Column(db.Integer, db.ForeignKey('parametri.id_parametro'), nullable=False)
    testo_visualizzato_ui = db.Column(db.String(255), nullable=False)
    ordine_visualizzazione = db.Column(db.Integer, nullable=False, default=0)
    data_creazione = db.Column(db.TIMESTAMP, server_default=db.func.now())
    data_modifica = db.Column(db.TIMESTAMP, server_default=db.func.now(), onupdate=db.func.now())
    
    foto = db.Column(db.String(255), nullable=True)
    colore = db.Column(db.String(50), nullable=True)
    descrizione = db.Column(db.TEXT, nullable=True)

    parametro = db.relationship("Parametri", back_populates="valori")
    
    # Relazione con CondizioniRegola per la condizione di valore
    condizioni_valore = db.relationship("CondizioniRegola", back_populates="valore_parametro_condizionante", foreign_keys="[CondizioniRegola.id_valore_condizione_predefinita]")

    def __repr__(self):
        return f"<ValoreParametro(id={self.id_valore_parametro}, testo='{self.testo_visualizzato_ui}')>"


class RegolaDipendenzaComplessa(db.Model):
    __tablename__ = 'regole_dipendenza_complessa'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    nome_regola = db.Column(db.String(255), nullable=False, unique=True)
    descrizione = db.Column(db.TEXT, nullable=True)
    attiva = db.Column(db.Boolean, nullable=False, default=True)
    logica_combinazione_condizioni = db.Column(db.String(10), nullable=False, default='AND') # E.g., 'AND', 'OR'
    data_creazione = db.Column(db.TIMESTAMP, server_default=db.func.now())
    data_modifica = db.Column(db.TIMESTAMP, server_default=db.func.now(), onupdate=db.func.now())

    condizioni = db.relationship("CondizioniRegola", back_populates="regola", cascade="all, delete-orphan")
    risultati = db.relationship("RisultatiRegola", back_populates="regola", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<RegolaDipendenzaComplessa(id={self.id}, nome_regola='{self.nome_regola}')>"

class CondizioniRegola(db.Model):
    __tablename__ = 'condizioni_regola'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    id_regola = db.Column(db.Integer, db.ForeignKey('regole_dipendenza_complessa.id'), nullable=False)
    id_parametro_condizionante = db.Column(db.Integer, db.ForeignKey('parametri.id_parametro'), nullable=False)
    
    id_valore_condizione_predefinita = db.Column(db.Integer, db.ForeignKey('valoriparametro.id_valore_parametro'), nullable=True)
    
    valore_condizione_libero = db.Column(db.String(255), nullable=True) # Per tipi INPUT_TEXT/NUMBER
    
    # Campo per indicare il tipo di operatore, es. 'EQ' (uguale), 'NEQ' (diverso), 'GT', 'LT', 'GTE', 'LTE', 'CONTAINS'
    tipo_condizione = db.Column(db.String(50), nullable=False)
    ordine_valutazione = db.Column(db.Integer, nullable=False, default=0)
    data_creazione = db.Column(db.TIMESTAMP, server_default=db.func.now())
    data_modifica = db.Column(db.TIMESTAMP, server_default=db.func.now(), onupdate=db.func.now())

    regola = db.relationship("RegolaDipendenzaComplessa", back_populates="condizioni")
    parametro_condizionante = db.relationship("Parametri", back_populates="condizioni_parametro", foreign_keys=[id_parametro_condizionante])
    valore_parametro_condizionante = db.relationship("ValoriParametro", back_populates="condizioni_valore", foreign_keys=[id_valore_condizione_predefinita])

    def __repr__(self):
        return f"<CondizioniRegola(id={self.id}, id_regola={self.id_regola}, id_parametro_condizionante={self.id_parametro_condizionante})>"

class RisultatiRegola(db.Model):
    __tablename__ = 'risultati_regola'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    id_regola = db.Column(db.Integer, db.ForeignKey('regole_dipendenza_complessa.id'), nullable=False)
    id_parametro_effetto = db.Column(db.Integer, db.ForeignKey('parametri.id_parametro'), nullable=False)
    
    # Tipo di effetto: 'MOSTRA', 'NASCONDI', 'IMPOSTA_VALORE', 'RENDI_OBBLIGATORIO', 'RENDI_OPZIONALE'
    tipo_effetto = db.Column(db.String(50), nullable=False)
    
    id_valore_effetto_predefinito = db.Column(db.Integer, db.ForeignKey('valoriparametro.id_valore_parametro'), nullable=True)
    
    valore_effetto_libero = db.Column(db.String(255), nullable=True) # Per effetti numerici o testuali

    data_creazione = db.Column(db.TIMESTAMP, server_default=db.func.now())
    data_modifica = db.Column(db.TIMESTAMP, server_default=db.func.now(), onupdate=db.func.now())

    regola = db.relationship("RegolaDipendenzaComplessa", back_populates="risultati")
    parametro_effetto = db.relationship("Parametri", back_populates="risultati_parametro", foreign_keys=[id_parametro_effetto])
    valore_effetto_associato = db.relationship("ValoriParametro", foreign_keys=[id_valore_effetto_predefinito])

    def __repr__(self):
        return f"<RisultatiRegola(id={self.id}, id_regola={self.id_regola}, id_parametro_effetto={self.id_parametro_effetto}, tipo_effetto='{self.tipo_effetto}')>"


class Cliente(db.Model):
    __tablename__ = 'clienti'
    
    id_cliente = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    hashed_password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    
    # Dati anagrafici
    nome = db.Column(db.String(100), nullable=False)
    cognome = db.Column(db.String(100), nullable=False)
    ragione_sociale = db.Column(db.String(255), nullable=True)  # Per aziende
    partita_iva = db.Column(db.String(20), nullable=True)
    codice_fiscale = db.Column(db.String(16), nullable=True)
    
    # Contatti
    telefono = db.Column(db.String(20), nullable=True)
    cellulare = db.Column(db.String(20), nullable=True)
    indirizzo = db.Column(db.String(255), nullable=True)
    citta = db.Column(db.String(100), nullable=True)
    cap = db.Column(db.String(10), nullable=True)
    provincia = db.Column(db.String(5), nullable=True)
    nazione = db.Column(db.String(100), nullable=False, default='Italia')
    
    # Dati bancari e pagamento
    iban = db.Column(db.String(34), nullable=True)  # IBAN standard europeo
    modalita_pagamento = db.Column(db.String(100), nullable=True)  # es: "Bonifico", "RID", "Contanti", etc.
    
    # Fatturazione elettronica
    codice_sdi = db.Column(db.String(7), nullable=True)  # Codice destinatario SDI (7 caratteri)
    indirizzo_pec = db.Column(db.String(255), nullable=True)  # PEC per fatturazione elettronica
    
    # Note
    note = db.Column(db.TEXT, nullable=True)
    
    # Stato e metadati
    attivo = db.Column(db.Boolean, default=True, nullable=False)
    data_creazione = db.Column(db.DateTime, default=datetime.datetime.now)
    data_ultima_modifica = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    ultimo_accesso = db.Column(db.DateTime, nullable=True)
    
    def set_password(self, password):
        self.hashed_password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.hashed_password, password)
    
    @property
    def nome_completo(self):
        return f"{self.nome} {self.cognome}"
    
    @property
    def denominazione(self):
        """Ritorna ragione sociale se presente, altrimenti nome completo"""
        return self.ragione_sociale if self.ragione_sociale else self.nome_completo

    def __repr__(self):
        return f'<Cliente {self.username} - {self.denominazione}>'