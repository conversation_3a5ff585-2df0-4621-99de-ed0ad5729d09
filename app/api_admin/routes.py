from flask import Blueprint
from flask_jwt_extended import jwt_required
from flask import request, jsonify, current_app
import os
from werkzeug.utils import secure_filename
from app.models import Parametri, ValoriParametro, RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, Cliente
from app.extensions import db, ma
from app.auth.middleware import admin_required
from app.schemas.cliente_schema import (
    cliente_create_schema, cliente_update_schema, cliente_response_schema, 
    clienti_response_schema, clienti_list_schema
)
from marshmallow import Schema, fields, ValidationError, post_load
import datetime
from sqlalchemy import or_, and_

admin_bp = Blueprint('admin', __name__)

class CondizioniRegolaSchema(ma.Schema):
    id = fields.Integer(dump_only=True)
    id_regola = fields.Integer(dump_only=True) # Aggiunto per output nidificato
    id_parametro_condizionante = fields.Integer(required=True)
    id_valore_condizione_predefinita = fields.Integer(allow_none=True)
    valore_condizione_libero = fields.String(allow_none=True)
    tipo_condizione = fields.String(required=True)
    ordine_valutazione = fields.Integer(load_default=0)

    class Meta:
        model = CondizioniRegola
        sqla_session = db.session

class RisultatiRegolaSchema(ma.Schema):
    id = fields.Integer(dump_only=True)
    id_regola = fields.Integer(dump_only=True) # Aggiunto per output nidificato
    id_parametro_effetto = fields.Integer(required=True)
    tipo_effetto = fields.String(required=True)
    id_valore_effetto_predefinito = fields.Integer(allow_none=True)
    valore_effetto_libero = fields.String(allow_none=True)

    class Meta:
        model = RisultatiRegola
        sqla_session = db.session

class RegolaDipendenzaComplessaSchema(ma.Schema):
    id = fields.Integer(dump_only=True)
    nome_regola = fields.String(required=True)
    descrizione = fields.String(allow_none=True)
    attiva = fields.Boolean(load_default=True)
    logica_combinazione_condizioni = fields.String(load_default='AND')
    condizioni = fields.List(fields.Nested(CondizioniRegolaSchema))
    risultati = fields.List(fields.Nested(RisultatiRegolaSchema))

    class Meta:
        model = RegolaDipendenzaComplessa
        sqla_session = db.session

    @post_load
    def make_regola_dipendenza_complessa(self, data, **kwargs):
        condizioni_data = data.pop('condizioni', [])
        risultati_data = data.pop('risultati', [])
        
        regola = RegolaDipendenzaComplessa(**data)
        
        regola.condizioni = [CondizioniRegola(**c) for c in condizioni_data]
        regola.risultati = [RisultatiRegola(**r) for r in risultati_data]
        
        return regola

# Schema separato per gli aggiornamenti (senza @post_load)
class RegolaDipendenzaComplessaUpdateSchema(ma.Schema):
    nome_regola = fields.String(required=True)
    descrizione = fields.String(allow_none=True)
    attiva = fields.Boolean(load_default=True)
    logica_combinazione_condizioni = fields.String(load_default='AND')
    condizioni = fields.List(fields.Nested(CondizioniRegolaSchema))
    risultati = fields.List(fields.Nested(RisultatiRegolaSchema))

    class Meta:
        model = RegolaDipendenzaComplessa
        sqla_session = db.session

regola_dipendenza_complessa_schema = RegolaDipendenzaComplessaSchema()
regole_dipendenza_complessa_schema = RegolaDipendenzaComplessaSchema(many=True)
regola_dipendenza_complessa_update_schema = RegolaDipendenzaComplessaUpdateSchema()

# Flask-JWT-Extended does not support applying @jwt_required directly to the Blueprint.
# Therefore, we apply the decorator individually to each route.
# CRUD Parametri

@admin_bp.route('/parametri', methods=['GET'])
@admin_required()
def list_parametri():
    parametri = Parametri.query.order_by(Parametri.ordine_visualizzazione.asc()).all()
    result = []
    for p in parametri:
        try:
            current_app.logger.debug(f"Processing parameter: {p.id_parametro}, TipoControlloUI: {p.tipo_controllo_ui}")
            result.append({
                'id_parametro': p.id_parametro,
                'nome_parametro': p.nome_parametro,
                'tipo_controllo_ui': p.tipo_controllo_ui.name,
                'descrizione': p.descrizione,
                'foto': p.foto,
                'is_root': p.is_root,
                'ordine_visualizzazione': p.ordine_visualizzazione,
                'attivo': p.attivo,
                'data_creazione': p.data_creazione.isoformat() if p.data_creazione else None,
                'data_modifica': p.data_modifica.isoformat() if p.data_modifica else None
            })
        except Exception as e:
            current_app.logger.error(f"Error processing parameter {p.id_parametro}: {e}")
            # Potresti voler saltare questo parametro o restituire un errore specifico
            # Per ora, lo salteremo e continueremo, ma l'errore 422 potrebbe ancora verificarsi se la lista è vuota o parziale
            continue
    return jsonify(result), 200

@admin_bp.route('/parametri/all-for-select', methods=['GET'])
@jwt_required()
def get_parametri_all_for_select():
    parametri = Parametri.query.with_entities(Parametri.id_parametro, Parametri.nome_parametro).all()
    result = [{'id_parametro': p.id_parametro, 'nome_parametro': p.nome_parametro} for p in parametri]
    return jsonify(result), 200

@admin_bp.route('/parametri/<int:id_parametro>/valori-for-select', methods=['GET'])
@jwt_required()
def get_valori_for_select(id_parametro):
    valori = ValoriParametro.query.filter_by(id_parametro=id_parametro).order_by(ValoriParametro.ordine_visualizzazione).with_entities(
        ValoriParametro.id_valore_parametro,
        ValoriParametro.testo_visualizzato_ui,
        ValoriParametro.ordine_visualizzazione,
        ValoriParametro.foto,
        ValoriParametro.colore,
        ValoriParametro.descrizione,
        ValoriParametro.data_creazione,
        ValoriParametro.data_modifica
    ).all()
    result = []
    for v in valori:
        result.append({
            'id_valore_parametro': v.id_valore_parametro, 
            'testo_visualizzato_ui': v.testo_visualizzato_ui,
            'ordine_visualizzazione': v.ordine_visualizzazione,
            'foto': v.foto,
            'colore': v.colore,
            'descrizione': v.descrizione,
            'data_creazione': v.data_creazione.isoformat() if v.data_creazione else None,
            'data_modifica': v.data_modifica.isoformat() if v.data_modifica else None
        })
    return jsonify(result), 200

@admin_bp.route('/parametri', methods=['POST'])
@jwt_required()
def create_parametro():
    data = request.form.to_dict()
    nome_parametro = data.get('nome_parametro')
    tipo_controllo_ui = data.get('tipo_controllo_ui')
    descrizione = data.get('descrizione')
    is_root = data.get('is_root', 'false').lower() == 'true'
    attivo = data.get('attivo', 'true').lower() == 'true'
    ordine_visualizzazione = data.get('ordine_visualizzazione')

    if not nome_parametro or not tipo_controllo_ui:
        return jsonify({'message': 'nome_parametro e tipo_controllo_ui sono obbligatori'}), 400

    # Se non è specificato un ordine, usa il prossimo disponibile
    if ordine_visualizzazione is None:
        max_order = db.session.query(db.func.max(Parametri.ordine_visualizzazione)).scalar()
        ordine_visualizzazione = (max_order or 0) + 1
    else:
        ordine_visualizzazione = int(ordine_visualizzazione)

    # Creazione nuovo parametro senza foto inizialmente
    nuovo_parametro = Parametri(
        nome_parametro=nome_parametro,
        tipo_controllo_ui=tipo_controllo_ui,
        descrizione=descrizione,
        is_root=is_root,
        ordine_visualizzazione=ordine_visualizzazione,
        attivo=attivo
    )
    db.session.add(nuovo_parametro)
    db.session.commit()

    # Gestione upload foto
    if 'foto' in request.files:
        foto_file = request.files['foto']
        if foto_file.filename != '':
            filename = secure_filename(foto_file.filename)
            upload_folder = os.path.join(current_app.root_path, 'public', 'images', 'parametri', str(nuovo_parametro.id_parametro))
            os.makedirs(upload_folder, exist_ok=True)
            file_path = os.path.join(upload_folder, filename)
            foto_file.save(file_path)
            # Salvo solo il percorso relativo nel db
            nuovo_parametro.foto = os.path.join('images', 'parametri', str(nuovo_parametro.id_parametro), filename)
            db.session.commit()

    return jsonify({'id_parametro': nuovo_parametro.id_parametro}), 201

@admin_bp.route('/parametri/<int:id_parametro>', methods=['GET'])
@jwt_required()
def get_parametro(id_parametro):
    parametro = Parametri.query.get_or_404(id_parametro)
    result = {
        'id_parametro': parametro.id_parametro,
        'nome_parametro': parametro.nome_parametro,
        'tipo_controllo_ui': parametro.tipo_controllo_ui.name,
        'descrizione': parametro.descrizione,
        'foto': parametro.foto,
        'is_root': parametro.is_root,
        'ordine_visualizzazione': parametro.ordine_visualizzazione,
        'attivo': parametro.attivo,
        'data_creazione': parametro.data_creazione.isoformat() if parametro.data_creazione else None,
        'data_modifica': parametro.data_modifica.isoformat() if parametro.data_modifica else None
    }
    return jsonify(result), 200

@admin_bp.route('/parametri/<int:id_parametro>', methods=['PUT'])
@jwt_required()
def update_parametro(id_parametro):
    parametro = Parametri.query.get_or_404(id_parametro)
    data = request.form.to_dict()

    nome_parametro = data.get('nome_parametro')
    tipo_controllo_ui = data.get('tipo_controllo_ui')
    descrizione = data.get('descrizione')
    is_root = data.get('is_root')
    attivo = data.get('attivo')
    ordine_visualizzazione = data.get('ordine_visualizzazione')

    # Validazione manuale basilare
    if nome_parametro is not None and not nome_parametro.strip():
        return jsonify({'message': 'nome_parametro non può essere vuoto'}), 400
    if tipo_controllo_ui is not None and not tipo_controllo_ui.strip():
        return jsonify({'message': 'tipo_controllo_ui non può essere vuoto'}), 400

    if nome_parametro is not None:
        parametro.nome_parametro = nome_parametro
    if tipo_controllo_ui is not None:
        parametro.tipo_controllo_ui = tipo_controllo_ui
    if descrizione is not None:
        parametro.descrizione = descrizione
    if is_root is not None:
        parametro.is_root = is_root.lower() == 'true'
    if attivo is not None:
        parametro.attivo = attivo.lower() == 'true'
    if ordine_visualizzazione is not None:
        parametro.ordine_visualizzazione = int(ordine_visualizzazione)

    # Gestione upload foto
    if 'foto' in request.files:
        foto_file = request.files['foto']
        if foto_file.filename != '':
            filename = secure_filename(foto_file.filename)
            upload_folder = os.path.join(current_app.root_path, 'public', 'images', 'parametri', str(parametro.id_parametro))
            os.makedirs(upload_folder, exist_ok=True)
            file_path = os.path.join(upload_folder, filename)
            foto_file.save(file_path)
            parametro.foto = os.path.join('images', 'parametri', str(parametro.id_parametro), filename)

    db.session.commit()

    return jsonify({'message': 'Parametro aggiornato'}), 200

@admin_bp.route('/parametri/<int:id_parametro>', methods=['DELETE'])
@jwt_required()
def delete_parametro(id_parametro):
    parametro = Parametri.query.get_or_404(id_parametro)
    db.session.delete(parametro)
    db.session.commit()
    return jsonify({'message': 'Parametro eliminato'}), 200

# CRUD ValoriParametro

@admin_bp.route('/valoriparametro', methods=['GET'])
@jwt_required()
def list_valori_parametro():
    valori = ValoriParametro.query.all()
    result = []
    for v in valori:
        result.append({
            'id_valore_parametro': v.id_valore_parametro,
            'id_parametro': v.id_parametro,
            'testo_visualizzato_ui': v.testo_visualizzato_ui,
            'ordine_visualizzazione': v.ordine_visualizzazione,
            'foto': v.foto,
            'colore': v.colore,
            'descrizione': v.descrizione,
            'data_creazione': v.data_creazione.isoformat() if v.data_creazione else None,
            'data_modifica': v.data_modifica.isoformat() if v.data_modifica else None
        })
    return jsonify(result), 200

@admin_bp.route('/valoriparametro', methods=['POST'])
@jwt_required()
def create_valore_parametro():
    data = request.form.to_dict()
    id_parametro = data.get('id_parametro')
    testo_visualizzato_ui = data.get('testo_visualizzato_ui')
    ordine_visualizzazione = data.get('ordine_visualizzazione')
    colore = data.get('colore')
    descrizione = data.get('descrizione')

    if not id_parametro or not testo_visualizzato_ui:
        return jsonify({'message': 'id_parametro e testo_visualizzato_ui sono obbligatori'}), 400

    # Se non è specificato un ordine, usa il prossimo disponibile
    if ordine_visualizzazione is None:
        max_order = db.session.query(db.func.max(ValoriParametro.ordine_visualizzazione)).filter_by(id_parametro=id_parametro).scalar()
        ordine_visualizzazione = (max_order or 0) + 1
    else:
        ordine_visualizzazione = int(ordine_visualizzazione)

    nuovo_valore = ValoriParametro(
        id_parametro=id_parametro,
        testo_visualizzato_ui=testo_visualizzato_ui,
        ordine_visualizzazione=ordine_visualizzazione,
        colore=colore,
        descrizione=descrizione
    )
    db.session.add(nuovo_valore)
    db.session.commit()

    # Gestione upload foto
    if 'foto' in request.files:
        foto_file = request.files['foto']
        if foto_file.filename != '':
            filename = secure_filename(foto_file.filename)
            upload_folder = os.path.join(current_app.root_path, 'public', 'images', 'valoriparametro', str(nuovo_valore.id_valore_parametro))
            os.makedirs(upload_folder, exist_ok=True)
            file_path = os.path.join(upload_folder, filename)
            foto_file.save(file_path)
            # Salvo solo il percorso relativo nel db
            nuovo_valore.foto = os.path.join('images', 'valoriparametro', str(nuovo_valore.id_valore_parametro), filename)
            db.session.commit()

    return jsonify({'id_valore_parametro': nuovo_valore.id_valore_parametro}), 201

@admin_bp.route('/valoriparametro/<int:id_valore_parametro>', methods=['GET'])
@jwt_required()
def get_valore_parametro(id_valore_parametro):
    valore = ValoriParametro.query.get_or_404(id_valore_parametro)
    result = {
        'id_valore_parametro': valore.id_valore_parametro,
        'id_parametro': valore.id_parametro,
        'testo_visualizzato_ui': valore.testo_visualizzato_ui,
        'ordine_visualizzazione': valore.ordine_visualizzazione,
        'foto': valore.foto,
        'colore': valore.colore,
        'descrizione': valore.descrizione,
        'data_creazione': valore.data_creazione.isoformat() if valore.data_creazione else None,
        'data_modifica': valore.data_modifica.isoformat() if valore.data_modifica else None
    }
    return jsonify(result), 200

@admin_bp.route('/valoriparametro/<int:id_valore_parametro>', methods=['PUT'])
@jwt_required()
def update_valore_parametro(id_valore_parametro):
    valore = ValoriParametro.query.get_or_404(id_valore_parametro)
    data = request.form.to_dict()

    testo_visualizzato_ui = data.get('testo_visualizzato_ui')
    ordine_visualizzazione = data.get('ordine_visualizzazione')
    colore = data.get('colore')
    descrizione = data.get('descrizione')

    # Validazione manuale basilare
    if testo_visualizzato_ui is not None and not testo_visualizzato_ui.strip():
        return jsonify({'message': 'testo_visualizzato_ui non può essere vuoto'}), 400

    if testo_visualizzato_ui is not None:
        valore.testo_visualizzato_ui = testo_visualizzato_ui
    if ordine_visualizzazione is not None:
        valore.ordine_visualizzazione = int(ordine_visualizzazione)
    if colore is not None:
        valore.colore = colore
    if descrizione is not None:
        valore.descrizione = descrizione

    # Gestione upload foto
    if 'foto' in request.files:
        foto_file = request.files['foto']
        if foto_file.filename != '':
            filename = secure_filename(foto_file.filename)
            upload_folder = os.path.join(current_app.root_path, 'public', 'images', 'valoriparametro', str(valore.id_valore_parametro))
            os.makedirs(upload_folder, exist_ok=True)
            file_path = os.path.join(upload_folder, filename)
            foto_file.save(file_path)
            valore.foto = os.path.join('images', 'valoriparametro', str(valore.id_valore_parametro), filename)

    db.session.commit()

    return jsonify({'message': 'ValoreParametro aggiornato'}), 200

@admin_bp.route('/valoriparametro/<int:id_valore_parametro>', methods=['DELETE'])
@jwt_required()
def delete_valore_parametro(id_valore_parametro):
    valore = ValoriParametro.query.get_or_404(id_valore_parametro)
    db.session.delete(valore)
    db.session.commit()
    return jsonify({'message': 'ValoreParametro eliminato'}), 200

@admin_bp.route('/parametri/<int:id_parametro>/valori/reorder', methods=['PUT'])
@jwt_required()
def reorder_valori_parametro(id_parametro):
    """
    Endpoint per riordinare i valori di un parametro.
    Payload: [{"id_valore_parametro": 1, "ordine_visualizzazione": 0}, ...]
    """
    data = request.get_json()
    if not data or not isinstance(data, list):
        return jsonify({'message': 'Payload deve essere una lista di oggetti con id_valore_parametro e ordine_visualizzazione'}), 400
    
    try:
        # Verifica che tutti i valori appartengano al parametro specificato
        for item in data:
            if 'id_valore_parametro' not in item or 'ordine_visualizzazione' not in item:
                return jsonify({'message': 'Ogni elemento deve avere id_valore_parametro e ordine_visualizzazione'}), 400
            
            valore = ValoriParametro.query.get_or_404(item['id_valore_parametro'])
            if valore.id_parametro != id_parametro:
                return jsonify({'message': f'Il valore {item["id_valore_parametro"]} non appartiene al parametro {id_parametro}'}), 400
        
        # Aggiorna l'ordine di visualizzazione
        for item in data:
            valore = ValoriParametro.query.get(item['id_valore_parametro'])
            valore.ordine_visualizzazione = item['ordine_visualizzazione']
        
        db.session.commit()
        return jsonify({'message': 'Ordine aggiornato con successo'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore durante l\'aggiornamento dell\'ordine: {str(e)}'}), 500

@admin_bp.route('/parametri/<int:id_parametro>/valori/normalize-order', methods=['PUT'])
@jwt_required()
def normalize_valori_order(id_parametro):
    """
    Normalizza l'ordine di visualizzazione dei valori di un parametro.
    Assegna ordini sequenziali (0, 1, 2, ...) basati sull'ordine attuale.
    """
    try:
        # Recupera tutti i valori del parametro ordinati per ordine_visualizzazione e poi per id
        valori = ValoriParametro.query.filter_by(id_parametro=id_parametro).order_by(
            ValoriParametro.ordine_visualizzazione.asc(),
            ValoriParametro.id_valore_parametro.asc()
        ).all()
        
        if not valori:
            return jsonify({'message': 'Nessun valore trovato per questo parametro'}), 404
        
        # Assegna ordini sequenziali
        for index, valore in enumerate(valori):
            valore.ordine_visualizzazione = index
        
        db.session.commit()
        return jsonify({'message': f'Ordine normalizzato per {len(valori)} valori'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore durante la normalizzazione dell\'ordine: {str(e)}'}), 500

@admin_bp.route('/parametri/reorder', methods=['PUT'])
@jwt_required()
def reorder_parametri():
    """
    Endpoint per riordinare i parametri.
    Payload: [{"id_parametro": 1, "ordine_visualizzazione": 0}, ...]
    """
    data = request.get_json()
    if not data or not isinstance(data, list):
        return jsonify({'message': 'Payload deve essere una lista di oggetti con id_parametro e ordine_visualizzazione'}), 400
    
    try:
        # Verifica che tutti i parametri esistano
        for item in data:
            if 'id_parametro' not in item or 'ordine_visualizzazione' not in item:
                return jsonify({'message': 'Ogni elemento deve avere id_parametro e ordine_visualizzazione'}), 400
            
            parametro = Parametri.query.get_or_404(item['id_parametro'])
        
        # Aggiorna l'ordine di visualizzazione
        for item in data:
            parametro = Parametri.query.get(item['id_parametro'])
            parametro.ordine_visualizzazione = item['ordine_visualizzazione']
        
        db.session.commit()
        return jsonify({'message': 'Ordine aggiornato con successo'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore durante l\'aggiornamento dell\'ordine: {str(e)}'}), 500

@admin_bp.route('/parametri/normalize-order', methods=['PUT'])
@jwt_required()
def normalize_parametri_order():
    """
    Normalizza l'ordine di visualizzazione dei parametri.
    Assegna ordini sequenziali (0, 1, 2, ...) basati sull'ordine attuale.
    """
    try:
        # Recupera tutti i parametri ordinati per ordine_visualizzazione e poi per id
        parametri = Parametri.query.order_by(
            Parametri.ordine_visualizzazione.asc(),
            Parametri.id_parametro.asc()
        ).all()
        
        if not parametri:
            return jsonify({'message': 'Nessun parametro trovato'}), 404
        
        # Assegna ordini sequenziali
        for index, parametro in enumerate(parametri):
            parametro.ordine_visualizzazione = index
        
        db.session.commit()
        return jsonify({'message': f'Ordine normalizzato per {len(parametri)} parametri'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore durante la normalizzazione dell\'ordine: {str(e)}'}), 500

@admin_bp.route('/')
@jwt_required()
def home():
    return "API Admin Home"
# CRUD Utenti

from app.models import Utente

@admin_bp.route('/utenti', methods=['GET'])
@jwt_required()
def list_utenti():
    page = request.args.get('page', default=1, type=int)
    per_page = request.args.get('per_page', default=10, type=int)
    username_filter = request.args.get('username', default=None, type=str)
    email_filter = request.args.get('email', default=None, type=str)

    query = Utente.query
    if username_filter:
        query = query.filter(Utente.username.ilike(f'%{username_filter}%'))
    if email_filter:
        query = query.filter(Utente.email.ilike(f'%{email_filter}%'))

    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    utenti = pagination.items

    result = []
    for u in utenti:
        result.append({
            'id_utente': u.id_utente,
            'username': u.username,
            'email': u.email,
            'nome_completo': u.nome_completo,
            'attivo': u.attivo,
            'data_creazione': u.data_creazione.isoformat() if u.data_creazione else None,
            'data_ultima_modifica': u.data_ultima_modifica.isoformat() if u.data_ultima_modifica else None
        })

    return jsonify({
        'items': result,
        'total': pagination.total,
        'page': pagination.page,
        'per_page': pagination.per_page,
        'pages': pagination.pages
    }), 200

@admin_bp.route('/utenti', methods=['POST'])
@jwt_required()
def create_utente():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    email = data.get('email')
    nome_completo = data.get('nome_completo')
    attivo = data.get('attivo', True)

    if not username or not password or not email:
        return jsonify({'message': 'username, password e email sono obbligatori'}), 400

    if Utente.query.filter_by(username=username).first():
        return jsonify({'message': 'username già esistente'}), 400

    if Utente.query.filter_by(email=email).first():
        return jsonify({'message': 'email già esistente'}), 400

    nuovo_utente = Utente(
        username=username,
        email=email,
        nome_completo=nome_completo,
        attivo=attivo
    )
    nuovo_utente.set_password(password)

    db.session.add(nuovo_utente)
    db.session.commit()

    return jsonify({'message': 'Utente creato con successo', 'id_utente': nuovo_utente.id_utente}), 201

@admin_bp.route('/utenti/<int:id_utente>', methods=['GET'])
@jwt_required()
def get_utente(id_utente):
    utente = Utente.query.get(id_utente)
    if not utente:
        return jsonify({'message': 'Utente non trovato'}), 404

    result = {
        'id_utente': utente.id_utente,
        'username': utente.username,
        'email': utente.email,
        'nome_completo': utente.nome_completo,
        'attivo': utente.attivo,
        'data_creazione': utente.data_creazione.isoformat() if utente.data_creazione else None,
        'data_ultima_modifica': utente.data_ultima_modifica.isoformat() if utente.data_ultima_modifica else None
    }
    return jsonify(result), 200

@admin_bp.route('/utenti/<int:id_utente>', methods=['PUT'])
@jwt_required()
def update_utente(id_utente):
    utente = Utente.query.get(id_utente)
    if not utente:
        return jsonify({'message': 'Utente non trovato'}), 404

    data = request.get_json()
    password = data.get('password')
    email = data.get('email')
    nome_completo = data.get('nome_completo')
    attivo = data.get('attivo')

    if password:
        utente.set_password(password)
    if email:
        if Utente.query.filter(Utente.email == email, Utente.id_utente != id_utente).first():
            return jsonify({'message': 'Email già in uso'}), 400
        utente.email = email
    if nome_completo is not None:
        utente.nome_completo = nome_completo
    if attivo is not None:
        utente.attivo = attivo

    db.session.commit()
    return jsonify({'message': 'Utente aggiornato con successo'}), 200

@admin_bp.route('/utenti/<int:id_utente>', methods=['DELETE'])
@jwt_required()
def delete_utente(id_utente):
    utente = Utente.query.get(id_utente)
    if not utente:
        return jsonify({'message': 'Utente non trovato'}), 404

    db.session.delete(utente)
    db.session.commit()
    return jsonify({'message': 'Utente eliminato con successo'}), 200

# CRUD RegoleDipendenzaComplessa

@admin_bp.route('/dependency-rules', methods=['GET'])
@jwt_required()
def list_dependency_rules():
    regole = RegolaDipendenzaComplessa.query.all()
    # Utilizza lo schema con many=True per serializzare la lista di regole
    result = regole_dipendenza_complessa_schema.dump(regole)
    return jsonify(result), 200

@admin_bp.route('/dependency-rules', methods=['POST'])
@jwt_required()
def create_dependency_rule():
    json_data = request.get_json()
    if not json_data:
        return jsonify({'message': 'No input data provided'}), 400
    try:
        data = regola_dipendenza_complessa_schema.load(json_data)
    except ValidationError as err:
        return jsonify(err.messages), 422
    
    # La logica atomica è gestita dal @post_load del Marshmallow Schema e dalla relazione cascade="all, delete-orphan"
    # che gestirà l'aggiunta di CondizioniRegola e RisultatiRegola in modo implicito.
    db.session.add(data) # 'data' è l'oggetto RegolaDipendenzaComplessa completo con figli
    db.session.commit()

    return jsonify({'message': 'Regola di dipendenza complessa creata con successo', 'id': data.id}), 201

@admin_bp.route('/dependency-rules/<int:id_regola>', methods=['GET'])
@jwt_required()
def get_dependency_rule(id_regola):
    regola = RegolaDipendenzaComplessa.query.get_or_404(id_regola)
    return regola_dipendenza_complessa_schema.jsonify(regola), 200

@admin_bp.route('/dependency-rules/<int:id_regola>', methods=['PUT'])
@jwt_required()
def update_dependency_rule(id_regola):
    regola = RegolaDipendenzaComplessa.query.get_or_404(id_regola)
    json_data = request.get_json()
    if not json_data:
        return jsonify({'message': 'No input data provided'}), 400

    try:
        # Usa lo schema di aggiornamento che non ha @post_load
        validated_data = regola_dipendenza_complessa_update_schema.load(json_data, partial=True)
    except ValidationError as err:
        return jsonify(err.messages), 422

    # Inizia una transazione per garantire l'atomicita'.
    with db.session.begin_nested():
        # Aggiorna i campi diretti della regola
        regola.nome_regola = validated_data.get('nome_regola', regola.nome_regola)
        regola.descrizione = validated_data.get('descrizione', regola.descrizione)
        regola.attiva = validated_data.get('attiva', regola.attiva)
        regola.logica_combinazione_condizioni = validated_data.get('logica_combinazione_condizioni', regola.logica_combinazione_condizioni)

        # Gestione atomica di CondizioniRegola e RisultatiRegola
        # Elimina quelle esistenti e ricreale da zero per semplicità e per garantire l'atomicita'
        # Questo approccio è efficiente per un numero non enorme di condizioni/risultati.
        # Per scenari con moltissimi figli, si potrebbe implementare un aggiornamento più granulare.
        
        # Elimina tutte le condizioni e risultati esistenti associati a questa regola
        CondizioniRegola.query.filter_by(id_regola=regola.id).delete(synchronize_session=False)
        RisultatiRegola.query.filter_by(id_regola=regola.id).delete(synchronize_session=False)
        
        # Aggiungi le nuove condizioni e risultati dal payload aggiornato
        db.session.flush() # Assicurati che le eliminazioni siano processate
        
        # Ora validated_data contiene dizionari puri, non oggetti
        for cond_data in validated_data.get('condizioni', []):
            # Rimuovi eventuali campi che non dovrebbero essere passati al costruttore
            cond_dict = {k: v for k, v in cond_data.items() if k not in ['id', 'id_regola']}
            new_cond = CondizioniRegola(**cond_dict, id_regola=regola.id)
            db.session.add(new_cond)

        for ris_data in validated_data.get('risultati', []):
            # Rimuovi eventuali campi che non dovrebbero essere passati al costruttore
            ris_dict = {k: v for k, v in ris_data.items() if k not in ['id', 'id_regola']}
            new_ris = RisultatiRegola(**ris_dict, id_regola=regola.id)
            db.session.add(new_ris)
        
    db.session.commit() # Commit della transazione esterna

    return jsonify({'message': 'Regola di dipendenza complessa aggiornata con successo'}), 200

@admin_bp.route('/dependency-rules/<int:id_regola>/duplicate', methods=['POST'])
@jwt_required()
def duplicate_dependency_rule(id_regola):
    """
    Duplica una regola di dipendenza complessa esistente.
    Crea una nuova regola con lo stesso contenuto ma con nome modificato.
    """
    regola_originale = RegolaDipendenzaComplessa.query.get_or_404(id_regola)
    
    try:
        with db.session.begin_nested():
            # Crea una nuova regola con i dati della regola originale
            nuova_regola = RegolaDipendenzaComplessa(
                nome_regola=f"{regola_originale.nome_regola} - Copia",
                descrizione=regola_originale.descrizione,
                attiva=False,  # La copia inizia come inattiva per sicurezza
                logica_combinazione_condizioni=regola_originale.logica_combinazione_condizioni
            )
            db.session.add(nuova_regola)
            db.session.flush()  # Per ottenere l'ID della nuova regola
            
            # Duplica tutte le condizioni
            for condizione_originale in regola_originale.condizioni:
                nuova_condizione = CondizioniRegola(
                    id_regola=nuova_regola.id,
                    id_parametro_condizionante=condizione_originale.id_parametro_condizionante,
                    id_valore_condizione_predefinita=condizione_originale.id_valore_condizione_predefinita,
                    valore_condizione_libero=condizione_originale.valore_condizione_libero,
                    tipo_condizione=condizione_originale.tipo_condizione,
                    ordine_valutazione=condizione_originale.ordine_valutazione
                )
                db.session.add(nuova_condizione)
            
            # Duplica tutti i risultati
            for risultato_originale in regola_originale.risultati:
                nuovo_risultato = RisultatiRegola(
                    id_regola=nuova_regola.id,
                    id_parametro_effetto=risultato_originale.id_parametro_effetto,
                    tipo_effetto=risultato_originale.tipo_effetto,
                    id_valore_effetto_predefinito=risultato_originale.id_valore_effetto_predefinito,
                    valore_effetto_libero=risultato_originale.valore_effetto_libero
                )
                db.session.add(nuovo_risultato)
        
        db.session.commit()
        
        return jsonify({
            'message': 'Regola di dipendenza complessa duplicata con successo',
            'id': nuova_regola.id,
            'nome_regola': nuova_regola.nome_regola
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore durante la duplicazione della regola: {str(e)}'}), 500

@admin_bp.route('/dependency-rules/<int:id_regola>', methods=['DELETE'])
@jwt_required()
def delete_dependency_rule(id_regola):
    regola = RegolaDipendenzaComplessa.query.get_or_404(id_regola)
    db.session.delete(regola)
    db.session.commit()
    return jsonify({'message': 'Regola di dipendenza complessa eliminata con successo'}), 200

# ============================================================================
# GESTIONE PRODOTTI (DIAMETRI) - Sistema Specializzato
# ============================================================================

@admin_bp.route('/products', methods=['GET'])
@jwt_required()
def list_products():
    """
    Endpoint per ottenere la lista dei prodotti (valori del parametro Diametro)
    con le relative regole di dipendenza associate.
    
    Milestone 1.1: Lista Prodotti con Regole Associate
    """
    try:
        # Prima verifichiamo se esiste il parametro "Diametro"
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        
        if not parametro_diametro:
            return jsonify({
                'message': 'Parametro "Diametro" non trovato. Creare prima il parametro.',
                'products': []
            }), 404
        
        # Query ottimizzata con JOIN per evitare N+1 queries
        # Otteniamo tutti i valori del parametro Diametro con le regole associate
        query = db.session.query(
            ValoriParametro.id_valore_parametro,
            ValoriParametro.testo_visualizzato_ui,
            ValoriParametro.descrizione,
            ValoriParametro.foto,
            ValoriParametro.colore,
            ValoriParametro.ordine_visualizzazione,
            ValoriParametro.data_creazione,
            ValoriParametro.data_modifica
        ).filter(
            ValoriParametro.id_parametro == parametro_diametro.id_parametro
        ).order_by(ValoriParametro.ordine_visualizzazione.asc())
        
        valori_diametro = query.all()
        
        products = []
        for valore in valori_diametro:
            # Per ogni valore di diametro, cerchiamo le regole associate
            # Una regola è associata se ha un risultato che punta a questo valore
            regole_associate = db.session.query(
                RegolaDipendenzaComplessa.id,
                RegolaDipendenzaComplessa.nome_regola,
                RegolaDipendenzaComplessa.descrizione,
                RegolaDipendenzaComplessa.attiva
            ).join(
                RisultatiRegola, RegolaDipendenzaComplessa.id == RisultatiRegola.id_regola
            ).filter(
                RisultatiRegola.id_parametro_effetto == parametro_diametro.id_parametro,
                RisultatiRegola.id_valore_effetto_predefinito == valore.id_valore_parametro
            ).all()
            
            # Per ogni regola, otteniamo i prerequisiti (condizioni)
            prerequisiti = {}
            regole_dettagliate = []
            
            for regola in regole_associate:
                # Otteniamo le condizioni della regola per identificare i prerequisiti
                condizioni = db.session.query(
                    CondizioniRegola.id_parametro_condizionante,
                    CondizioniRegola.id_valore_condizione_predefinita,
                    CondizioniRegola.tipo_condizione,
                    Parametri.nome_parametro,
                    ValoriParametro.testo_visualizzato_ui
                ).join(
                    Parametri, CondizioniRegola.id_parametro_condizionante == Parametri.id_parametro
                ).outerjoin(
                    ValoriParametro, CondizioniRegola.id_valore_condizione_predefinita == ValoriParametro.id_valore_parametro
                ).filter(
                    CondizioniRegola.id_regola == regola.id
                ).all()
                
                prerequisiti_regola = {}
                for condizione in condizioni:
                    prerequisiti_regola[condizione.nome_parametro] = {
                        'id_parametro': condizione.id_parametro_condizionante,
                        'id_valore': condizione.id_valore_condizione_predefinita,
                        'nome_valore': condizione.testo_visualizzato_ui,
                        'tipo_condizione': condizione.tipo_condizione
                    }
                
                regole_dettagliate.append({
                    'id': regola.id,
                    'nome_regola': regola.nome_regola,
                    'descrizione': regola.descrizione,
                    'attiva': regola.attiva,
                    'prerequisiti': prerequisiti_regola
                })
            
            # Costruiamo l'oggetto prodotto
            product = {
                'id': valore.id_valore_parametro,
                'nome_prodotto': valore.testo_visualizzato_ui,
                'descrizione': valore.descrizione,
                'foto': valore.foto,
                'colore': valore.colore,
                'ordine_visualizzazione': valore.ordine_visualizzazione,
                'data_creazione': valore.data_creazione.isoformat() if valore.data_creazione else None,
                'data_modifica': valore.data_modifica.isoformat() if valore.data_modifica else None,
                'regole_associate': regole_dettagliate,
                'parametro_diametro_id': parametro_diametro.id_parametro
            }
            
            products.append(product)
        
        return jsonify({
            'products': products,
            'parametro_diametro': {
                'id': parametro_diametro.id_parametro,
                'nome': parametro_diametro.nome_parametro,
                'tipo_controllo_ui': parametro_diametro.tipo_controllo_ui.name
            },
            'total_products': len(products)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Errore in list_products: {str(e)}")
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/products/prerequisites', methods=['GET'])
@jwt_required()
def get_products_prerequisites():
    """
    Endpoint per ottenere i prerequisiti necessari per la creazione di prodotti.
    
    Milestone 1.4: Endpoint di Supporto per Prerequisiti
    """
    try:
        # Otteniamo i parametri prerequisiti con i loro valori
        prerequisiti = {}
        
        # 1. Lavorazioni (filtrata su "Elemento Singolo Avvitato")
        param_lavorazioni = Parametri.query.filter_by(nome_parametro="Lavorazioni").first()
        if param_lavorazioni:
            valore_elemento_singolo = ValoriParametro.query.filter_by(
                id_parametro=param_lavorazioni.id_parametro,
                testo_visualizzato_ui="Elemento Singolo Avvitato"
            ).first()
            
            if valore_elemento_singolo:
                prerequisiti['lavorazione'] = {
                    'id_parametro': param_lavorazioni.id_parametro,
                    'nome_parametro': param_lavorazioni.nome_parametro,
                    'valore_predefinito': {
                        'id': valore_elemento_singolo.id_valore_parametro,
                        'nome': valore_elemento_singolo.testo_visualizzato_ui
                    }
                }
        
        # 2. Tecnica
        param_tecnica = Parametri.query.filter_by(nome_parametro="Tecnica").first()
        if param_tecnica:
            valori_tecnica = ValoriParametro.query.filter_by(
                id_parametro=param_tecnica.id_parametro
            ).order_by(ValoriParametro.ordine_visualizzazione).all()
            
            prerequisiti['tecnica'] = {
                'id_parametro': param_tecnica.id_parametro,
                'nome_parametro': param_tecnica.nome_parametro,
                'valori_disponibili': [
                    {
                        'id': v.id_valore_parametro,
                        'nome': v.testo_visualizzato_ui,
                        'descrizione': v.descrizione,
                        'ordine': v.ordine_visualizzazione
                    } for v in valori_tecnica
                ]
            }
        
        # 3. Tipologia Impianto
        param_tipologia = Parametri.query.filter_by(nome_parametro="Tipologia Impianto").first()
        if param_tipologia:
            valori_tipologia = ValoriParametro.query.filter_by(
                id_parametro=param_tipologia.id_parametro
            ).order_by(ValoriParametro.ordine_visualizzazione).all()
            
            prerequisiti['tipologia_impianto'] = {
                'id_parametro': param_tipologia.id_parametro,
                'nome_parametro': param_tipologia.nome_parametro,
                'valori_disponibili': [
                    {
                        'id': v.id_valore_parametro,
                        'nome': v.testo_visualizzato_ui,
                        'descrizione': v.descrizione,
                        'ordine': v.ordine_visualizzazione
                    } for v in valori_tipologia
                ]
            }
        
        # 4. Connessione
        param_connessione = Parametri.query.filter_by(nome_parametro="Connessione").first()
        if param_connessione:
            valori_connessione = ValoriParametro.query.filter_by(
                id_parametro=param_connessione.id_parametro
            ).order_by(ValoriParametro.ordine_visualizzazione).all()
            
            prerequisiti['connessione'] = {
                'id_parametro': param_connessione.id_parametro,
                'nome_parametro': param_connessione.nome_parametro,
                'valori_disponibili': [
                    {
                        'id': v.id_valore_parametro,
                        'nome': v.testo_visualizzato_ui,
                        'descrizione': v.descrizione,
                        'ordine': v.ordine_visualizzazione
                    } for v in valori_connessione
                ]
            }
        
        # Verifichiamo se il parametro Diametro esiste
        param_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        diametro_info = None
        if param_diametro:
            diametro_info = {
                'id_parametro': param_diametro.id_parametro,
                'nome_parametro': param_diametro.nome_parametro,
                'tipo_controllo_ui': param_diametro.tipo_controllo_ui.name
            }
        
        # Contiamo i prerequisiti mancanti
        prerequisiti_mancanti = []
        if 'lavorazione' not in prerequisiti:
            prerequisiti_mancanti.append('Lavorazioni (con valore "Elemento Singolo Avvitato")')
        if 'tecnica' not in prerequisiti:
            prerequisiti_mancanti.append('Tecnica')
        if 'tipologia_impianto' not in prerequisiti:
            prerequisiti_mancanti.append('Tipologia Impianto')
        if 'connessione' not in prerequisiti:
            prerequisiti_mancanti.append('Connessione')
        
        return jsonify({
            'prerequisiti': prerequisiti,
            'parametro_diametro': diametro_info,
            'prerequisiti_mancanti': prerequisiti_mancanti,
            'sistema_pronto': len(prerequisiti_mancanti) == 0 and diametro_info is not None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Errore in get_products_prerequisites: {str(e)}")
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/products', methods=['POST'])
@jwt_required()
def create_product():
    """
    Endpoint per creare un nuovo prodotto con regola di dipendenza automatica.
    
    Milestone 1.2: Creazione Prodotto con Regola Automatica
    """
    try:
        # Gestione dati form (per supportare upload file)
        if request.content_type and 'multipart/form-data' in request.content_type:
            data = request.form.to_dict()
            # Parsing dei prerequisiti se passati come JSON string
            import json
            if 'prerequisiti' in data:
                try:
                    data['prerequisiti'] = json.loads(data['prerequisiti'])
                except json.JSONDecodeError:
                    return jsonify({'message': 'Formato prerequisiti non valido'}), 400
        else:
            data = request.get_json()
        
        if not data:
            return jsonify({'message': 'Dati non forniti'}), 400
        
        # Validazione campi obbligatori
        nome_prodotto = data.get('nome_prodotto')
        prerequisiti = data.get('prerequisiti', {})
        
        if not nome_prodotto:
            return jsonify({'message': 'nome_prodotto è obbligatorio'}), 400
        
        if not prerequisiti:
            return jsonify({'message': 'prerequisiti sono obbligatori'}), 400
        
        # Validazione prerequisiti richiesti
        prerequisiti_richiesti = ['id_lavorazione', 'id_tipologia_impianto', 'id_connessione']
        for req in prerequisiti_richiesti:
            if req not in prerequisiti:
                return jsonify({'message': f'{req} è obbligatorio nei prerequisiti'}), 400
        
        # Validazione speciale per tecniche (può essere singola o multipla)
        if 'id_tecniche' not in prerequisiti and 'id_tecnica' not in prerequisiti:
            return jsonify({'message': 'id_tecniche è obbligatorio nei prerequisiti'}), 400
        
        # Verifica che il parametro Diametro esista
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        if not parametro_diametro:
            return jsonify({'message': 'Parametro "Diametro" non trovato. Creare prima il parametro.'}), 404
        
        # Verifica che tutti i prerequisiti esistano
        prerequisiti_valori = {}
        tecniche_ids = []
        
        for key, valore_id in prerequisiti.items():
            if key == 'id_tecniche':
                # Gestione array di tecniche
                if isinstance(valore_id, list):
                    tecniche_ids = valore_id
                else:
                    tecniche_ids = [valore_id]  # Backward compatibility
                
                # Verifica che tutte le tecniche esistano
                for tecnica_id in tecniche_ids:
                    valore = ValoriParametro.query.get(tecnica_id)
                    if not valore:
                        return jsonify({'message': f'Valore tecnica {tecnica_id} non trovato'}), 404
                        
            elif key == 'id_tecnica':
                # Backward compatibility per singola tecnica
                tecniche_ids = [valore_id]
                valore = ValoriParametro.query.get(valore_id)
                if not valore:
                    return jsonify({'message': f'Valore tecnica {valore_id} non trovato'}), 404
                    
            elif key.startswith('id_'):
                valore = ValoriParametro.query.get(valore_id)
                if not valore:
                    return jsonify({'message': f'Valore prerequisito {valore_id} non trovato'}), 404
                prerequisiti_valori[key] = valore
        
        # Inizia transazione atomica
        with db.session.begin_nested():
            # 1. Crea il valore del parametro Diametro
            # Calcola ordine di visualizzazione
            max_order = db.session.query(db.func.max(ValoriParametro.ordine_visualizzazione)).filter_by(
                id_parametro=parametro_diametro.id_parametro
            ).scalar()
            ordine_visualizzazione = data.get('ordine_visualizzazione', (max_order or 0) + 1)
            
            nuovo_valore_diametro = ValoriParametro(
                id_parametro=parametro_diametro.id_parametro,
                testo_visualizzato_ui=nome_prodotto,
                descrizione=data.get('descrizione'),
                colore=data.get('colore'),
                ordine_visualizzazione=ordine_visualizzazione
            )
            db.session.add(nuovo_valore_diametro)
            db.session.flush()  # Per ottenere l'ID
            
            # Gestione upload foto
            if 'foto' in request.files:
                foto_file = request.files['foto']
                if foto_file.filename != '':
                    filename = secure_filename(foto_file.filename)
                    upload_folder = os.path.join(
                        current_app.root_path, 'public', 'images', 'valoriparametro', 
                        str(nuovo_valore_diametro.id_valore_parametro)
                    )
                    os.makedirs(upload_folder, exist_ok=True)
                    file_path = os.path.join(upload_folder, filename)
                    foto_file.save(file_path)
                    nuovo_valore_diametro.foto = os.path.join(
                        'images', 'valoriparametro', 
                        str(nuovo_valore_diametro.id_valore_parametro), filename
                    )
            
            # 2. Crea le regole di dipendenza automatiche (una per ogni tecnica)
            regole_create = []
            
            for i, tecnica_id in enumerate(tecniche_ids):
                tecnica_valore = ValoriParametro.query.get(tecnica_id)
                if not tecnica_valore:
                    continue
                    
                nome_regola = f"Regola Prodotto: {nome_prodotto} - {tecnica_valore.testo_visualizzato_ui}"
                descrizione_regola = f"Regola automatica per il prodotto {nome_prodotto} con tecnica {tecnica_valore.testo_visualizzato_ui}"
                
                nuova_regola = RegolaDipendenzaComplessa(
                    nome_regola=nome_regola,
                    descrizione=descrizione_regola,
                    attiva=True,
                    logica_combinazione_condizioni='AND'
                )
                db.session.add(nuova_regola)
                db.session.flush()  # Per ottenere l'ID
                
                # 3. Crea le condizioni (prerequisiti) per questa regola
                ordine_condizione = 0
                
                # Aggiungi condizione per lavorazione
                if 'id_lavorazione' in prerequisiti_valori:
                    condizione = CondizioniRegola(
                        id_regola=nuova_regola.id,
                        id_parametro_condizionante=prerequisiti_valori['id_lavorazione'].id_parametro,
                        id_valore_condizione_predefinita=prerequisiti_valori['id_lavorazione'].id_valore_parametro,
                        tipo_condizione='EQUALS',
                        ordine_valutazione=ordine_condizione
                    )
                    db.session.add(condizione)
                    ordine_condizione += 1
                
                # Aggiungi condizione per questa tecnica specifica
                condizione_tecnica = CondizioniRegola(
                    id_regola=nuova_regola.id,
                    id_parametro_condizionante=tecnica_valore.id_parametro,
                    id_valore_condizione_predefinita=tecnica_valore.id_valore_parametro,
                    tipo_condizione='EQUALS',
                    ordine_valutazione=ordine_condizione
                )
                db.session.add(condizione_tecnica)
                ordine_condizione += 1
                
                # Aggiungi condizioni per altri prerequisiti
                for key, valore in prerequisiti_valori.items():
                    if key not in ['id_lavorazione']:  # Già gestita sopra
                        condizione = CondizioniRegola(
                            id_regola=nuova_regola.id,
                            id_parametro_condizionante=valore.id_parametro,
                            id_valore_condizione_predefinita=valore.id_valore_parametro,
                            tipo_condizione='EQUALS',
                            ordine_valutazione=ordine_condizione
                        )
                        db.session.add(condizione)
                        ordine_condizione += 1
                
                # 4. Crea il risultato (filtra valori per mostrare solo questo prodotto)
                risultato = RisultatiRegola(
                    id_regola=nuova_regola.id,
                    id_parametro_effetto=parametro_diametro.id_parametro,
                    tipo_effetto='FILTER_VALUES',
                    id_valore_effetto_predefinito=nuovo_valore_diametro.id_valore_parametro
                )
                db.session.add(risultato)
                
                regole_create.append({
                    'id': nuova_regola.id,
                    'nome_regola': nuova_regola.nome_regola,
                    'descrizione': nuova_regola.descrizione,
                    'attiva': nuova_regola.attiva
                })
        
        # Commit della transazione
        db.session.commit()
        
        return jsonify({
            'message': 'Prodotto creato con successo',
            'product': {
                'id': nuovo_valore_diametro.id_valore_parametro,
                'nome_prodotto': nuovo_valore_diametro.testo_visualizzato_ui,
                'descrizione': nuovo_valore_diametro.descrizione,
                'foto': nuovo_valore_diametro.foto,
                'colore': nuovo_valore_diametro.colore,
                'ordine_visualizzazione': nuovo_valore_diametro.ordine_visualizzazione
            },
            'regole': regole_create,
            'tecniche_count': len(tecniche_ids)
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore in create_product: {str(e)}")
        return jsonify({'message': f'Errore durante la creazione del prodotto: {str(e)}'}), 500

@admin_bp.route('/products/<int:product_id>', methods=['PUT'])
@jwt_required()
def update_product(product_id):
    """
    Endpoint per aggiornare un prodotto esistente e le sue regole associate.
    
    Milestone 1.3: Aggiornamento Prodotto e Regole
    """
    try:
        # Verifica che il parametro Diametro esista
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        if not parametro_diametro:
            return jsonify({'message': 'Parametro "Diametro" non trovato.'}), 404
        
        # Verifica che il prodotto esista e appartenga al parametro Diametro
        valore_diametro = ValoriParametro.query.filter_by(
            id_valore_parametro=product_id,
            id_parametro=parametro_diametro.id_parametro
        ).first()
        
        if not valore_diametro:
            return jsonify({'message': 'Prodotto non trovato.'}), 404
        
        # Gestione dati form (per supportare upload file)
        if request.content_type and 'multipart/form-data' in request.content_type:
            data = request.form.to_dict()
            # Parsing dei prerequisiti se passati come JSON string
            import json
            if 'prerequisiti' in data:
                try:
                    data['prerequisiti'] = json.loads(data['prerequisiti'])
                except json.JSONDecodeError:
                    return jsonify({'message': 'Formato prerequisiti non valido'}), 400
        else:
            data = request.get_json()
        
        if not data:
            return jsonify({'message': 'Dati non forniti'}), 400
        
        # Inizia transazione atomica
        with db.session.begin_nested():
            # 1. Aggiorna il valore del parametro Diametro
            if 'nome_prodotto' in data:
                valore_diametro.testo_visualizzato_ui = data['nome_prodotto']
            if 'descrizione' in data:
                valore_diametro.descrizione = data['descrizione']
            if 'colore' in data:
                valore_diametro.colore = data['colore']
            if 'ordine_visualizzazione' in data:
                valore_diametro.ordine_visualizzazione = data['ordine_visualizzazione']
            
            # Gestione upload foto
            if 'foto' in request.files:
                foto_file = request.files['foto']
                if foto_file.filename != '':
                    filename = secure_filename(foto_file.filename)
                    upload_folder = os.path.join(
                        current_app.root_path, 'public', 'images', 'valoriparametro', 
                        str(valore_diametro.id_valore_parametro)
                    )
                    os.makedirs(upload_folder, exist_ok=True)
                    file_path = os.path.join(upload_folder, filename)
                    foto_file.save(file_path)
                    valore_diametro.foto = os.path.join(
                        'images', 'valoriparametro', 
                        str(valore_diametro.id_valore_parametro), filename
                    )
            
            # 2. Aggiorna le regole se sono stati forniti nuovi prerequisiti
            if 'prerequisiti' in data:
                prerequisiti = data['prerequisiti']
                
                # Validazione prerequisiti richiesti
                prerequisiti_richiesti = ['id_lavorazione', 'id_tipologia_impianto', 'id_connessione']
                for req in prerequisiti_richiesti:
                    if req not in prerequisiti:
                        return jsonify({'message': f'{req} è obbligatorio nei prerequisiti'}), 400
                
                # Validazione speciale per tecniche (può essere singola o multipla)
                if 'id_tecniche' not in prerequisiti and 'id_tecnica' not in prerequisiti:
                    return jsonify({'message': 'id_tecniche è obbligatorio nei prerequisiti'}), 400
                
                # Verifica che tutti i prerequisiti esistano
                prerequisiti_valori = {}
                tecniche_ids = []
                
                for key, valore_id in prerequisiti.items():
                    if key == 'id_tecniche':
                        # Gestione array di tecniche
                        if isinstance(valore_id, list):
                            tecniche_ids = valore_id
                        else:
                            tecniche_ids = [valore_id]  # Backward compatibility
                        
                        # Verifica che tutte le tecniche esistano
                        for tecnica_id in tecniche_ids:
                            valore = ValoriParametro.query.get(tecnica_id)
                            if not valore:
                                return jsonify({'message': f'Valore tecnica {tecnica_id} non trovato'}), 404
                                
                    elif key == 'id_tecnica':
                        # Backward compatibility per singola tecnica
                        tecniche_ids = [valore_id]
                        valore = ValoriParametro.query.get(valore_id)
                        if not valore:
                            return jsonify({'message': f'Valore tecnica {valore_id} non trovato'}), 404
                            
                    elif key.startswith('id_'):
                        valore = ValoriParametro.query.get(valore_id)
                        if not valore:
                            return jsonify({'message': f'Valore prerequisito {valore_id} non trovato'}), 404
                        prerequisiti_valori[key] = valore
                
                # Elimina tutte le regole esistenti associate a questo prodotto
                regole_associate = db.session.query(RegolaDipendenzaComplessa).join(
                    RisultatiRegola, RegolaDipendenzaComplessa.id == RisultatiRegola.id_regola
                ).filter(
                    RisultatiRegola.id_parametro_effetto == parametro_diametro.id_parametro,
                    RisultatiRegola.id_valore_effetto_predefinito == valore_diametro.id_valore_parametro
                ).all()
                
                # Elimina tutte le regole esistenti
                for regola in regole_associate:
                    db.session.delete(regola)  # Cascade eliminerà automaticamente condizioni e risultati
                
                db.session.flush()  # Assicurati che le eliminazioni siano processate
                
                # Crea nuove regole per ogni tecnica selezionata
                regole_create = []
                
                for i, tecnica_id in enumerate(tecniche_ids):
                    tecnica_valore = ValoriParametro.query.get(tecnica_id)
                    if not tecnica_valore:
                        continue
                        
                    nome_regola = f"Regola Prodotto: {valore_diametro.testo_visualizzato_ui} - {tecnica_valore.testo_visualizzato_ui}"
                    descrizione_regola = f"Regola automatica per il prodotto {valore_diametro.testo_visualizzato_ui} con tecnica {tecnica_valore.testo_visualizzato_ui}"
                    
                    nuova_regola = RegolaDipendenzaComplessa(
                        nome_regola=nome_regola,
                        descrizione=descrizione_regola,
                        attiva=True,
                        logica_combinazione_condizioni='AND'
                    )
                    db.session.add(nuova_regola)
                    db.session.flush()  # Per ottenere l'ID
                    
                    # Crea le condizioni (prerequisiti) per questa regola
                    ordine_condizione = 0
                    
                    # Aggiungi condizione per lavorazione
                    if 'id_lavorazione' in prerequisiti_valori:
                        condizione = CondizioniRegola(
                            id_regola=nuova_regola.id,
                            id_parametro_condizionante=prerequisiti_valori['id_lavorazione'].id_parametro,
                            id_valore_condizione_predefinita=prerequisiti_valori['id_lavorazione'].id_valore_parametro,
                            tipo_condizione='EQUALS',
                            ordine_valutazione=ordine_condizione
                        )
                        db.session.add(condizione)
                        ordine_condizione += 1
                    
                    # Aggiungi condizione per questa tecnica specifica
                    condizione_tecnica = CondizioniRegola(
                        id_regola=nuova_regola.id,
                        id_parametro_condizionante=tecnica_valore.id_parametro,
                        id_valore_condizione_predefinita=tecnica_valore.id_valore_parametro,
                        tipo_condizione='EQUALS',
                        ordine_valutazione=ordine_condizione
                    )
                    db.session.add(condizione_tecnica)
                    ordine_condizione += 1
                    
                    # Aggiungi condizioni per altri prerequisiti
                    for key, valore in prerequisiti_valori.items():
                        if key not in ['id_lavorazione']:  # Già gestita sopra
                            condizione = CondizioniRegola(
                                id_regola=nuova_regola.id,
                                id_parametro_condizionante=valore.id_parametro,
                                id_valore_condizione_predefinita=valore.id_valore_parametro,
                                tipo_condizione='EQUALS',
                                ordine_valutazione=ordine_condizione
                            )
                            db.session.add(condizione)
                            ordine_condizione += 1
                    
                    # Crea il risultato (filtra valori per mostrare solo questo prodotto)
                    risultato = RisultatiRegola(
                        id_regola=nuova_regola.id,
                        id_parametro_effetto=parametro_diametro.id_parametro,
                        tipo_effetto='FILTER_VALUES',
                        id_valore_effetto_predefinito=valore_diametro.id_valore_parametro
                    )
                    db.session.add(risultato)
                    
                    regole_create.append({
                        'id': nuova_regola.id,
                        'nome_regola': nuova_regola.nome_regola,
                        'descrizione': nuova_regola.descrizione,
                        'attiva': nuova_regola.attiva
                    })
        
        # Commit della transazione
        db.session.commit()
        
        # Prepara la risposta
        response_data = {
            'message': 'Prodotto aggiornato con successo',
            'product': {
                'id': valore_diametro.id_valore_parametro,
                'nome_prodotto': valore_diametro.testo_visualizzato_ui,
                'descrizione': valore_diametro.descrizione,
                'foto': valore_diametro.foto,
                'colore': valore_diametro.colore,
                'ordine_visualizzazione': valore_diametro.ordine_visualizzazione
            }
        }
        
        # Aggiungi informazioni sulle regole se sono state aggiornate
        if 'prerequisiti' in data and 'regole_create' in locals():
            response_data['regole'] = regole_create
            response_data['tecniche_count'] = len(tecniche_ids)
        
        return jsonify(response_data), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore in update_product: {str(e)}")
        return jsonify({'message': f'Errore durante l\'aggiornamento del prodotto: {str(e)}'}), 500

@admin_bp.route('/products/<int:product_id>', methods=['DELETE'])
@jwt_required()
def delete_product(product_id):
    """
    Endpoint per eliminare un prodotto e le sue regole associate.
    
    Milestone 1.3: Eliminazione Prodotto e Regole
    """
    try:
        # Verifica che il parametro Diametro esista
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        if not parametro_diametro:
            return jsonify({'message': 'Parametro "Diametro" non trovato.'}), 404
        
        # Verifica che il prodotto esista e appartenga al parametro Diametro
        valore_diametro = ValoriParametro.query.filter_by(
            id_valore_parametro=product_id,
            id_parametro=parametro_diametro.id_parametro
        ).first()
        
        if not valore_diametro:
            return jsonify({'message': 'Prodotto non trovato.'}), 404
        
        # Inizia transazione atomica
        with db.session.begin_nested():
            # 1. Trova e elimina le regole associate a questo prodotto
            regole_associate = db.session.query(RegolaDipendenzaComplessa).join(
                RisultatiRegola, RegolaDipendenzaComplessa.id == RisultatiRegola.id_regola
            ).filter(
                RisultatiRegola.id_parametro_effetto == parametro_diametro.id_parametro,
                RisultatiRegola.id_valore_effetto_predefinito == valore_diametro.id_valore_parametro
            ).all()
            
            regole_eliminate = []
            for regola in regole_associate:
                regole_eliminate.append({
                    'id': regola.id,
                    'nome_regola': regola.nome_regola
                })
                db.session.delete(regola)  # Cascade eliminerà automaticamente condizioni e risultati
            
            # 2. Elimina il valore del parametro Diametro
            nome_prodotto = valore_diametro.testo_visualizzato_ui
            db.session.delete(valore_diametro)
        
        # Commit della transazione
        db.session.commit()
        
        return jsonify({
            'message': f'Prodotto "{nome_prodotto}" eliminato con successo',
            'regole_eliminate': regole_eliminate,
            'product_id': product_id
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore in delete_product: {str(e)}")
        return jsonify({'message': f'Errore durante l\'eliminazione del prodotto: {str(e)}'}), 500

@admin_bp.route('/products/<int:product_id>', methods=['GET'])
@jwt_required()
def get_product(product_id):
    """
    Endpoint per ottenere i dettagli di un singolo prodotto con le sue regole.
    
    Milestone 1.3: Dettaglio Singolo Prodotto
    """
    try:
        # Verifica che il parametro Diametro esista
        parametro_diametro = Parametri.query.filter_by(nome_parametro="Diametro").first()
        if not parametro_diametro:
            return jsonify({'message': 'Parametro "Diametro" non trovato.'}), 404
        
        # Verifica che il prodotto esista e appartenga al parametro Diametro
        valore_diametro = ValoriParametro.query.filter_by(
            id_valore_parametro=product_id,
            id_parametro=parametro_diametro.id_parametro
        ).first()
        
        if not valore_diametro:
            return jsonify({'message': 'Prodotto non trovato.'}), 404
        
        # Ottieni le regole associate
        regole_associate = db.session.query(
            RegolaDipendenzaComplessa.id,
            RegolaDipendenzaComplessa.nome_regola,
            RegolaDipendenzaComplessa.descrizione,
            RegolaDipendenzaComplessa.attiva
        ).join(
            RisultatiRegola, RegolaDipendenzaComplessa.id == RisultatiRegola.id_regola
        ).filter(
            RisultatiRegola.id_parametro_effetto == parametro_diametro.id_parametro,
            RisultatiRegola.id_valore_effetto_predefinito == valore_diametro.id_valore_parametro
        ).all()
        
        # Per ogni regola, ottieni i prerequisiti
        regole_dettagliate = []
        for regola in regole_associate:
            condizioni = db.session.query(
                CondizioniRegola.id_parametro_condizionante,
                CondizioniRegola.id_valore_condizione_predefinita,
                CondizioniRegola.tipo_condizione,
                Parametri.nome_parametro,
                ValoriParametro.testo_visualizzato_ui
            ).join(
                Parametri, CondizioniRegola.id_parametro_condizionante == Parametri.id_parametro
            ).outerjoin(
                ValoriParametro, CondizioniRegola.id_valore_condizione_predefinita == ValoriParametro.id_valore_parametro
            ).filter(
                CondizioniRegola.id_regola == regola.id
            ).all()
            
            prerequisiti_regola = {}
            for condizione in condizioni:
                prerequisiti_regola[condizione.nome_parametro] = {
                    'id_parametro': condizione.id_parametro_condizionante,
                    'id_valore': condizione.id_valore_condizione_predefinita,
                    'nome_valore': condizione.testo_visualizzato_ui,
                    'tipo_condizione': condizione.tipo_condizione
                }
            
            regole_dettagliate.append({
                'id': regola.id,
                'nome_regola': regola.nome_regola,
                'descrizione': regola.descrizione,
                'attiva': regola.attiva,
                'prerequisiti': prerequisiti_regola
            })
        
        product = {
            'id': valore_diametro.id_valore_parametro,
            'nome_prodotto': valore_diametro.testo_visualizzato_ui,
            'descrizione': valore_diametro.descrizione,
            'foto': valore_diametro.foto,
            'colore': valore_diametro.colore,
            'ordine_visualizzazione': valore_diametro.ordine_visualizzazione,
            'data_creazione': valore_diametro.data_creazione.isoformat() if valore_diametro.data_creazione else None,
            'data_modifica': valore_diametro.data_modifica.isoformat() if valore_diametro.data_modifica else None,
            'regole_associate': regole_dettagliate,
            'parametro_diametro_id': parametro_diametro.id_parametro
        }
        
        return jsonify({'product': product}), 200
        
    except Exception as e:
        current_app.logger.error(f"Errore in get_product: {str(e)}")
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500


# =============================================================================
# CRUD CLIENTI
# =============================================================================

@admin_bp.route('/clienti', methods=['GET'])
@admin_required()
def list_clienti():
    """Lista clienti con filtri, ricerca e paginazione"""
    try:
        # Parametri di query
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 25, type=int), 100)
        search = request.args.get('search', '').strip()
        stato = request.args.get('stato', '')  # 'attivo', 'disattivo', '' (tutti)
        citta = request.args.get('citta', '').strip()
        sort_by = request.args.get('sort_by', 'data_creazione')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Query base
        query = Cliente.query
        
        # Filtro ricerca (nome, cognome, username, email, ragione sociale)
        if search:
            search_filter = or_(
                Cliente.nome.ilike(f'%{search}%'),
                Cliente.cognome.ilike(f'%{search}%'),
                Cliente.username.ilike(f'%{search}%'),
                Cliente.email.ilike(f'%{search}%'),
                Cliente.ragione_sociale.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)
        
        # Filtro stato
        if stato == 'attivo':
            query = query.filter(Cliente.attivo == True)
        elif stato == 'disattivo':
            query = query.filter(Cliente.attivo == False)
        
        # Filtro città
        if citta:
            query = query.filter(Cliente.citta.ilike(f'%{citta}%'))
        
        # Ordinamento
        if sort_by == 'nome':
            order_column = Cliente.nome
        elif sort_by == 'cognome':
            order_column = Cliente.cognome
        elif sort_by == 'email':
            order_column = Cliente.email
        elif sort_by == 'citta':
            order_column = Cliente.citta
        elif sort_by == 'ultimo_accesso':
            order_column = Cliente.ultimo_accesso
        else:
            order_column = Cliente.data_creazione
        
        if sort_order == 'desc':
            order_column = order_column.desc()
        
        query = query.order_by(order_column)
        
        # Paginazione
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        clienti = pagination.items
        
        return jsonify({
            'clienti': clienti_list_schema.dump(clienti),
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'filters': {
                'search': search,
                'stato': stato,
                'citta': citta,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        }), 200
        
    except Exception as e:
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti', methods=['POST'])
@admin_required()
def create_cliente():
    """Crea un nuovo cliente"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'message': 'Dati JSON richiesti'}), 400
        
        # Validazione dati
        try:
            validated_data = cliente_create_schema.load(data)
        except ValidationError as err:
            return jsonify({'message': 'Errori di validazione', 'errors': err.messages}), 400
        
        # Verifica unicità username e email
        existing_username = Cliente.query.filter_by(username=validated_data['username']).first()
        if existing_username:
            return jsonify({'message': 'Username già esistente'}), 409
        
        existing_email = Cliente.query.filter_by(email=validated_data['email']).first()
        if existing_email:
            return jsonify({'message': 'Email già esistente'}), 409
        
        # Crea nuovo cliente
        password = validated_data.pop('password')
        cliente = Cliente(**validated_data)
        cliente.set_password(password)
        
        db.session.add(cliente)
        db.session.commit()
        
        return jsonify({
            'message': 'Cliente creato con successo',
            'cliente': cliente_response_schema.dump(cliente)
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/<int:cliente_id>', methods=['GET'])
@admin_required()
def get_cliente(cliente_id):
    """Ottieni dettagli di un cliente specifico"""
    try:
        cliente = Cliente.query.get(cliente_id)
        if not cliente:
            return jsonify({'message': 'Cliente non trovato'}), 404
        
        return jsonify({
            'cliente': cliente_response_schema.dump(cliente)
        }), 200
        
    except Exception as e:
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/<int:cliente_id>', methods=['PUT'])
@admin_required()
def update_cliente(cliente_id):
    """Aggiorna un cliente esistente"""
    try:
        cliente = Cliente.query.get(cliente_id)
        if not cliente:
            return jsonify({'message': 'Cliente non trovato'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'message': 'Dati JSON richiesti'}), 400
        
        # Validazione dati
        try:
            validated_data = cliente_update_schema.load(data)
        except ValidationError as err:
            return jsonify({'message': 'Errori di validazione', 'errors': err.messages}), 400
        
        # Verifica unicità username e email (se modificati)
        if 'username' in validated_data and validated_data['username'] != cliente.username:
            existing_username = Cliente.query.filter_by(username=validated_data['username']).first()
            if existing_username:
                return jsonify({'message': 'Username già esistente'}), 409
        
        if 'email' in validated_data and validated_data['email'] != cliente.email:
            existing_email = Cliente.query.filter_by(email=validated_data['email']).first()
            if existing_email:
                return jsonify({'message': 'Email già esistente'}), 409
        
        # Aggiorna campi
        for field, value in validated_data.items():
            setattr(cliente, field, value)
        
        cliente.data_ultima_modifica = datetime.datetime.now()
        db.session.commit()
        
        return jsonify({
            'message': 'Cliente aggiornato con successo',
            'cliente': cliente_response_schema.dump(cliente)
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/<int:cliente_id>', methods=['DELETE'])
@admin_required()
def delete_cliente(cliente_id):
    """Elimina un cliente"""
    try:
        cliente = Cliente.query.get(cliente_id)
        if not cliente:
            return jsonify({'message': 'Cliente non trovato'}), 404
        
        # Salva info per il messaggio di conferma
        cliente_info = f"{cliente.username} ({cliente.denominazione})"
        
        db.session.delete(cliente)
        db.session.commit()
        
        return jsonify({
            'message': f'Cliente {cliente_info} eliminato con successo'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/<int:cliente_id>/toggle-status', methods=['PUT'])
@admin_required()
def toggle_cliente_status(cliente_id):
    """Attiva/disattiva un cliente"""
    try:
        cliente = Cliente.query.get(cliente_id)
        if not cliente:
            return jsonify({'message': 'Cliente non trovato'}), 404
        
        # Toggle dello stato
        cliente.attivo = not cliente.attivo
        cliente.data_ultima_modifica = datetime.datetime.now()
        db.session.commit()
        
        status = "attivato" if cliente.attivo else "disattivato"
        
        return jsonify({
            'message': f'Cliente {status} con successo',
            'cliente': {
                'id_cliente': cliente.id_cliente,
                'username': cliente.username,
                'denominazione': cliente.denominazione,
                'attivo': cliente.attivo
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/<int:cliente_id>/reset-password', methods=['PUT'])
@admin_required()
def reset_cliente_password(cliente_id):
    """Reset password di un cliente"""
    try:
        cliente = Cliente.query.get(cliente_id)
        if not cliente:
            return jsonify({'message': 'Cliente non trovato'}), 404
        
        data = request.get_json()
        if not data or 'new_password' not in data:
            return jsonify({'message': 'Nuova password richiesta'}), 400
        
        new_password = data['new_password']
        if len(new_password) < 6:
            return jsonify({'message': 'La password deve essere di almeno 6 caratteri'}), 400
        
        cliente.set_password(new_password)
        cliente.data_ultima_modifica = datetime.datetime.now()
        db.session.commit()
        
        return jsonify({
            'message': 'Password resettata con successo'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500

@admin_bp.route('/clienti/stats', methods=['GET'])
@admin_required()
def get_clienti_stats():
    """Statistiche sui clienti"""
    try:
        total_clienti = Cliente.query.count()
        clienti_attivi = Cliente.query.filter_by(attivo=True).count()
        clienti_disattivi = Cliente.query.filter_by(attivo=False).count()
        
        # Clienti registrati nell'ultimo mese
        one_month_ago = datetime.datetime.now() - datetime.timedelta(days=30)
        nuovi_clienti_mese = Cliente.query.filter(Cliente.data_creazione >= one_month_ago).count()
        
        # Clienti che hanno fatto accesso nell'ultima settimana
        one_week_ago = datetime.datetime.now() - datetime.timedelta(days=7)
        accessi_settimana = Cliente.query.filter(
            and_(Cliente.ultimo_accesso >= one_week_ago, Cliente.ultimo_accesso.isnot(None))
        ).count()
        
        return jsonify({
            'stats': {
                'total_clienti': total_clienti,
                'clienti_attivi': clienti_attivi,
                'clienti_disattivi': clienti_disattivi,
                'nuovi_clienti_ultimo_mese': nuovi_clienti_mese,
                'accessi_ultima_settimana': accessi_settimana,
                'percentuale_attivi': round((clienti_attivi / total_clienti * 100) if total_clienti > 0 else 0, 1)
            }
        }), 200
        
    except Exception as e:
        return jsonify({'message': f'Errore interno del server: {str(e)}'}), 500