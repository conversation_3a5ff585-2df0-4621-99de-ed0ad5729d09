# from flask_admin import Admin
# from flask_admin.contrib.sqla import ModelView
# from wtforms_sqlalchemy.fields import QuerySelectField
# from app.models import Parametri, ValoriParametro, TipoControlloUI, RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola
# # Importa l'istanza db dal nuovo file extensions
# from app.extensions import db
# from flask_wtf import FlaskForm
# from wtforms import StringField, TextAreaField, BooleanField, SelectField
# from wtforms.validators import DataRequired

# # Definizione esplicita del form per Lavorazioni
# class ParametriForm(FlaskForm):
#     nome_parametro = StringField('Nome Parametro', validators=[DataRequired()])
#     tipo_controllo_ui = SelectField('Tipo Controllo UI', choices=[(tag.name, tag.value) for tag in TipoControlloUI], validators=[DataRequired()])
#     descrizione = TextAreaField('Descrizione')
#     foto = StringField('Foto')
#     is_root = BooleanField('Is Root')
#     attivo = BooleanField('Attivo')

# # View per Parametri
# class ParametriView(ModelView):
#     form = ParametriForm # Usa la form esplicita per Parametri
#     column_list = ['codice_parametro', 'nome_parametro', 'tipo_controllo_ui', 'descrizione', 'placeholder_ui', 'unita_misura', 'validazione_regex', 'attivo'] # Specifica i campi da mostrare
    
#     def __init__(self, session, **kwargs):
#         super(ParametriView, self).__init__(Parametri, session, **kwargs)


# class CondizioniRegolaForm(FlaskForm):
#     id_regola = QuerySelectField('Regola', query_factory=lambda: db.session.query(RegolaDipendenzaComplessa).all(), get_label='nome_regola', allow_blank=False)
#     id_parametro_condizionante = QuerySelectField('Parametro Condizionante', query_factory=lambda: db.session.query(Parametri).all(), get_label='nome_parametro', allow_blank=False)
#     id_valore_condizione_predefinita = QuerySelectField('Valore Condizione Predefinita', query_factory=lambda: db.session.query(ValoriParametro).all(), get_label='testo_visualizzato_ui', allow_blank=True)
#     valore_condizione_libero = StringField('Valore Condizione Libero')
#     tipo_condizione = StringField('Tipo Condizione', validators=[DataRequired()])
#     ordine_valutazione = StringField('Ordine Valutazione', validators=[DataRequired()])

# class CondizioniRegolaView(ModelView):
#     form = CondizioniRegolaForm
#     column_list = ('regola', 'parametro_condizionante', 'valore_parametro_condizionante', 'valore_condizione_libero', 'tipo_condizione', 'ordine_valutazione')
    
#     def __init__(self, session, **kwargs):
#         super(CondizioniRegolaView, self).__init__(CondizioniRegola, session, **kwargs)

# class RisultatiRegolaForm(FlaskForm):
#     id_regola = QuerySelectField('Regola', query_factory=lambda: db.session.query(RegolaDipendenzaComplessa).all(), get_label='nome_regola', allow_blank=False)
#     id_parametro_effetto = QuerySelectField('Parametro Effetto', query_factory=lambda: db.session.query(Parametri).all(), get_label='nome_parametro', allow_blank=False)
#     tipo_effetto = StringField('Tipo Effetto', validators=[DataRequired()])
#     id_valore_effetto_predefinito = QuerySelectField('Valore Effetto Predefinito', query_factory=lambda: db.session.query(ValoriParametro).all(), get_label='testo_visualizzato_ui', allow_blank=True)
#     valore_effetto_libero = StringField('Valore Effetto Libero')

# class RisultatiRegolaView(ModelView):
#     form = RisultatiRegolaForm
#     column_list = ('regola', 'parametro_effetto', 'tipo_effetto', 'valore_effetto_associato', 'valore_effetto_libero')
    
#     def __init__(self, session, **kwargs):
#         super(RisultatiRegolaView, self).__init__(RisultatiRegola, session, **kwargs)


# def init_admin(app, db):
#     admin = Admin(app, name='Admin Area', template_mode='bootstrap3')

#     # Add views for each model
#     admin.add_view(ModelView(Utente, db.session, name="Utenti"))
#     admin.add_view(ParametriView(db.session, name="Parametri"))
#     admin.add_view(ValoriParametroView(db.session, name="Valori Parametro"))
#     admin.add_view(ModelView(RegolaDipendenzaComplessa, db.session, name="Regole Dipendenza Complessa"))
#     admin.add_view(CondizioniRegolaView(db.session, name="Condizioni Regola"))
#     admin.add_view(RisultatiRegolaView(db.session, name="Risultati Regola"))
    
#     return admin