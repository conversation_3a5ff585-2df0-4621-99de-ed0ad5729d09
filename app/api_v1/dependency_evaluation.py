from flask import request, jsonify
from app.extensions import db
from app.models import RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, Parametri, ValoriParametro
from sqlalchemy.orm import joinedload
from marshmallow import Schema, fields, validate, ValidationError
from app.schemas.parameter_schema import ParametroSchema
from . import bp_v1

# Definire schemi Marshmallow per input/output
class InputValueSchema(Schema):
    parameter_id = fields.Integer(required=True)
    value_id = fields.Integer(required=True)

class EvaluatePayloadSchema(Schema):
    current_parameters = fields.List(fields.Nested(InputValueSchema), required=True)

# Funzione helper ottimizzata per determinare se un parametro ha dipendenze figlie
def _parametro_ha_dipendenze_figlie(id_parametro, param_ids_with_conditions_set):
    """
    Determina se un parametro può essere la condizione di una qualsiasi CondizioniRegola.
    Utilizza un set pre-caricato di ID per lookup efficiente.
    """
    return id_parametro in param_ids_with_conditions_set

@bp_v1.route('/dependency_evaluation/evaluate', methods=['POST'])
def evaluate_and_get_dependent_parameters():
    try:
        payload = EvaluatePayloadSchema().load(request.json)
        current_parameters_data = payload['current_parameters']
    except ValidationError as e:
        return jsonify({"error": e.messages}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 400

    # Debug logging
    print(f"=== BACKEND DEBUG evaluate_and_get_dependent_parameters ===")
    print(f"Received current_parameters_data: {current_parameters_data}")

    # Dizionario per un lookup efficiente dei valori correnti dei parametri
    current_param_values_map = {p['parameter_id']: p['value_id'] for p in current_parameters_data}
    print(f"current_param_values_map: {current_param_values_map}")

    # Pre-carica tutti gli id dei parametri che sono presenti in CondizioniRegola
    # per il calcolo efficiente di has_dependent_children
    param_ids_with_conditions = db.session.query(CondizioniRegola.id_parametro_condizionante).distinct().all()
    param_ids_with_conditions_set = {p.id_parametro_condizionante for p in param_ids_with_conditions}

    # Recupera tutte le regole di dipendenza complesse con le loro condizioni e risultati
    # Utilizza joinedload per eseguire un'unica query e prevenire problemi di N+1
    all_rules = RegolaDipendenzaComplessa.query.options(
        joinedload(RegolaDipendenzaComplessa.condizioni),
        joinedload(RegolaDipendenzaComplessa.risultati)
    ).filter(RegolaDipendenzaComplessa.attiva == True).all() # Considera solo le regole attive

    dependent_parameters_map = {} # Usa un dizionario per evitare duplicati e facilitare l'aggiornamento

    for rule in all_rules:
        # Valuta se la regola è soddisfatta
        rule_satisfied = True
        print(f"Evaluating rule: {rule.nome_regola} (ID: {rule.id})")
        for condition in rule.condizioni:
            current_param_id = condition.id_parametro_condizionante
            current_value_id = current_param_values_map.get(current_param_id)
            
            # Logica di valutazione della condizione basata sul tipo_condizione
            condition_met = False
            
            if condition.tipo_condizione == "EQUALS" or condition.tipo_condizione == "EQ":
                if condition.id_valore_condizione_predefinita is not None:
                    condition_met = current_value_id == condition.id_valore_condizione_predefinita
                elif condition.valore_condizione_libero is not None:
                    condition_met = str(current_value_id) == condition.valore_condizione_libero
                    
            elif condition.tipo_condizione == "HAS_VALUE":
                # Verifica che il parametro abbia un valore (non None e non vuoto)
                condition_met = current_value_id is not None and current_value_id != ""
                
            elif condition.tipo_condizione == "NOT_EQUALS" or condition.tipo_condizione == "NEQ":
                if condition.id_valore_condizione_predefinita is not None:
                    condition_met = current_value_id != condition.id_valore_condizione_predefinita
                elif condition.valore_condizione_libero is not None:
                    condition_met = str(current_value_id) != condition.valore_condizione_libero
                    
            else:
                # Fallback per compatibilità con il codice esistente
                if condition.id_valore_condizione_predefinita is not None:
                    condition_met = current_value_id == condition.id_valore_condizione_predefinita
                elif condition.valore_condizione_libero is not None:
                    condition_met = str(current_value_id) == condition.valore_condizione_libero
                else:
                    # Condizione basata sulla semplice presenza del parametro
                    condition_met = current_value_id is not None
            
            print(f"  Condition: param {current_param_id} (value: {current_value_id}) {condition.tipo_condizione} -> {condition_met}")
            
            if not condition_met:
                rule_satisfied = False
                break
        
        print(f"  Rule {rule.id} satisfied: {rule_satisfied}")
        
        if rule_satisfied:
            for result in rule.risultati:
                # Gestisci diversi tipi di effetto
                if result.tipo_effetto in ['SHOW', 'MOSTRA']:
                    param_effetto = db.session.get(Parametri, result.id_parametro_effetto)
                    if param_effetto:
                        # Inizializza con i dati di base del parametro
                        param_data = ParametroSchema().dump(param_effetto)
                        
                        # Aggiungi il campo has_dependent_children usando la funzione helper
                        param_data['has_dependent_children'] = _parametro_ha_dipendenze_figlie(param_effetto.id_parametro, param_ids_with_conditions_set)
                        
                        # Aggiungi/aggiorna il parametro nel dizionario dei risultati
                        if param_effetto.id_parametro not in dependent_parameters_map:
                            dependent_parameters_map[param_effetto.id_parametro] = param_data
                        
                        print(f"  Added parameter {param_effetto.nome_parametro} (ID: {param_effetto.id_parametro}) due to effect {result.tipo_effetto}")
                
                elif result.tipo_effetto in ['FILTER_VALUES', 'FILTRA_VALORI']:
                    param_effetto = db.session.get(Parametri, result.id_parametro_effetto)
                    if param_effetto:
                        # Inizializza con i dati di base del parametro se non esiste già
                        if param_effetto.id_parametro not in dependent_parameters_map:
                            param_data = ParametroSchema().dump(param_effetto)
                            param_data['has_dependent_children'] = _parametro_ha_dipendenze_figlie(param_effetto.id_parametro, param_ids_with_conditions_set)
                            dependent_parameters_map[param_effetto.id_parametro] = param_data
                        
                        # Inizializza la lista dei valori filtrati se non esiste già
                        if 'filtered_value_ids' not in dependent_parameters_map[param_effetto.id_parametro]:
                            dependent_parameters_map[param_effetto.id_parametro]['filtered_value_ids'] = set()
                        
                        # Aggiungi l'ID del valore da filtrare
                        if result.id_valore_effetto_predefinito is not None:
                            dependent_parameters_map[param_effetto.id_parametro]['filtered_value_ids'].add(result.id_valore_effetto_predefinito)
                        
                        # print(f"  Added filter value {result.id_valore_effetto_predefinito} for parameter {param_effetto.nome_parametro} (ID: {param_effetto.id_parametro})")
                
                # Altri tipi di effetto potrebbero essere gestiti qui in futuro
                else:
                    print(f"  Unhandled effect type: {result.tipo_effetto}")
    
    # Applica i filtri ai valori dei parametri
    for param_id, param_data in dependent_parameters_map.items():
        if 'filtered_value_ids' in param_data:
            filtered_ids = param_data['filtered_value_ids']
            if filtered_ids:  # Se ci sono valori da filtrare
                # Filtra i valori per mostrare solo quelli specificati
                param_data['valori'] = [v for v in param_data['valori'] if v['id_valore_parametro'] in filtered_ids]
            # Rimuovi il campo temporaneo
            del param_data['filtered_value_ids']
    
    # Converte il dizionario dei risultati in una lista per la risposta
    dependent_parameters = list(dependent_parameters_map.values())
    
    # Ordina i parametri per ordine_visualizzazione per garantire un ordine stabile
    dependent_parameters.sort(key=lambda p: p.get('ordine_visualizzazione', 999))
    
    print(f"Final dependent_parameters count: {len(dependent_parameters)}")
    print(f"Final dependent_parameters: {[p.get('nome_parametro', 'Unknown') for p in dependent_parameters]}")
    print(f"=== END BACKEND DEBUG ===")
    
    return jsonify({"dependent_parameters": dependent_parameters}), 200