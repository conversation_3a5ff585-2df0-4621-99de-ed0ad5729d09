from flask import jsonify
from app.extensions import db
from app.models import Parametri, CondizioniRegola
from app.schemas.parameter_schema import ParametroSchema
from . import bp_v1

@bp_v1.route('/parameters/root', methods=['GET'])
def get_root_parameters():
    """Recupera tutti i parametri root (lavorazioni) disponibili"""
    # Recupera tutti i parametri root dal database
    root_parameters = db.session.query(Parametri).filter(Parametri.is_root == True, Parametri.attivo == True).all()

    # Pre-carica gli id dei parametri che sono presenti in CondizioniRegola per ottimizzare has_dependent_children
    param_ids_with_conditions = db.session.query(CondizioniRegola.id_parametro_condizionante).distinct().all()
    param_ids_with_conditions_set = {p.id_parametro_condizionante for p in param_ids_with_conditions}

    serialized_data = []
    for param in root_parameters:
        # Serializza il parametro usando ParametroSchema
        param_data = ParametroSchema().dump(param)
        
        # Aggiungi il campo has_dependent_children
        param_data['has_dependent_children'] = param.id_parametro in param_ids_with_conditions_set
        serialized_data.append(param_data)
    
    # Ordina per ordine_visualizzazione per garantire un ordine stabile
    serialized_data.sort(key=lambda p: p.get('ordine_visualizzazione', 999))
    
    return jsonify(serialized_data), 200

@bp_v1.route('/parameters/<int:parameter_id>', methods=['GET'])
def get_parameter_details(parameter_id):
    """Recupera i dettagli completi di un parametro con i suoi valori"""
    parameter = db.session.get(Parametri, parameter_id)
    if not parameter or not parameter.attivo:
        return jsonify({"error": "Parametro non trovato"}), 404
    
    # Pre-carica gli id dei parametri che sono presenti in CondizioniRegola
    param_ids_with_conditions = db.session.query(CondizioniRegola.id_parametro_condizionante).distinct().all()
    param_ids_with_conditions_set = {p.id_parametro_condizionante for p in param_ids_with_conditions}
    
    # Serializza il parametro con tutti i suoi valori
    param_data = ParametroSchema().dump(parameter)
    param_data['has_dependent_children'] = parameter.id_parametro in param_ids_with_conditions_set
    
    return jsonify(param_data), 200

@bp_v1.route('/parameters/<int:parameter_id>/initial-config', methods=['GET'])
def get_initial_configuration(parameter_id):
    """Recupera la configurazione iniziale per un parametro root (lavorazione)"""
    from app.models import RegolaDipendenzaComplessa, RisultatiRegola
    from sqlalchemy.orm import joinedload
    
    # Verifica che il parametro esista e sia root
    parameter = db.session.get(Parametri, parameter_id)
    if not parameter or not parameter.attivo or not parameter.is_root:
        return jsonify({"error": "Parametro root non trovato"}), 404
    
    # Pre-carica gli id dei parametri che sono presenti in CondizioniRegola
    param_ids_with_conditions = db.session.query(CondizioniRegola.id_parametro_condizionante).distinct().all()
    param_ids_with_conditions_set = {p.id_parametro_condizionante for p in param_ids_with_conditions}
    
    # Trova tutti i parametri che sono effetti diretti di regole che hanno come condizione
    # la selezione di questo parametro root (senza valore specifico)
    initial_parameters = []
    
    # Query per trovare regole che hanno come condizione questo parametro
    rules_with_root_condition = db.session.query(RegolaDipendenzaComplessa).options(
        joinedload(RegolaDipendenzaComplessa.condizioni),
        joinedload(RegolaDipendenzaComplessa.risultati)
    ).join(CondizioniRegola).filter(
        CondizioniRegola.id_parametro_condizionante == parameter_id,
        RegolaDipendenzaComplessa.attiva == True
    ).all()
    
    # Raccogli tutti i parametri effetto che dovrebbero essere mostrati inizialmente
    effect_param_ids = set()
    for rule in rules_with_root_condition:
        for result in rule.risultati:
            if result.tipo_effetto == 'MOSTRA':
                effect_param_ids.add(result.id_parametro_effetto)
    
    # Recupera i parametri effetto
    if effect_param_ids:
        effect_parameters = db.session.query(Parametri).filter(
            Parametri.id_parametro.in_(effect_param_ids),
            Parametri.attivo == True
        ).all()
        
        for param in effect_parameters:
            param_data = ParametroSchema().dump(param)
            param_data['has_dependent_children'] = param.id_parametro in param_ids_with_conditions_set
            initial_parameters.append(param_data)
    
    # Ordina per ordine di visualizzazione se disponibile
    initial_parameters.sort(key=lambda x: x.get('ordine_visualizzazione', 0))
    
    return jsonify(initial_parameters), 200