from functools import wraps
from flask import jsonify
from flask_jwt_extended import jwt_required, get_jwt

def admin_required():
    """Decorator per verificare che l'utente sia un admin"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            claims = get_jwt()
            user_type = claims.get('user_type')
            
            # Se non c'è user_type, assumiamo sia un token admin (retrocompatibilità)
            if user_type is None or user_type == 'admin':
                return f(*args, **kwargs)
            else:
                return jsonify({"msg": "Accesso riservato agli amministratori"}), 403
                
        return decorated_function
    return decorator

def client_required():
    """Decorator per verificare che l'utente sia un cliente"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            claims = get_jwt()
            user_type = claims.get('user_type')
            
            if user_type == 'client':
                return f(*args, **kwargs)
            else:
                return jsonify({"msg": "Accesso riservato ai clienti"}), 403
                
        return decorated_function
    return decorator

def any_authenticated():
    """Decorator per verificare che l'utente sia autenticato (admin o cliente)"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            return f(*args, **kwargs)
                
        return decorated_function
    return decorator