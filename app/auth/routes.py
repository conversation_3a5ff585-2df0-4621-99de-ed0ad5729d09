from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, create_refresh_token
from app.models import Utente
from app.extensions import db

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

@auth_bp.route('/login', methods=['POST'])
def login():
    username = request.json.get('username', None)
    password = request.json.get('password', None)

    utente = Utente.query.filter_by(username=username).first()

    if utente and utente.check_password(password):
        # Aggiungi claim per identificare il tipo di utente
        additional_claims = {
            "user_type": "admin",
            "utente_id": utente.id_utente,
            "username": utente.username
        }
        
        access_token = create_access_token(
            identity=str(utente.id_utente),
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(
            identity=str(utente.id_utente),
            additional_claims=additional_claims
        )
        return jsonify(access_token=access_token, refresh_token=refresh_token, user={'id': utente.id_utente, 'username': utente.username, 'nome_completo': utente.nome_completo}), 200
    else:
        return jsonify({"msg": "Credenziali non valide"}), 401

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    # Per semplicità, il logout è lato client, che rimuove il token.
    # Se fosse richiesta una blacklist server-side, andrebbe implementata qui.
    return jsonify({"msg": "Logout effettuato con successo"}), 200

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def me():
    current_user_id = get_jwt_identity()
    utente = Utente.query.get(current_user_id)
    if utente:
        return jsonify(id=utente.id_utente, username=utente.username, nome_completo=utente.nome_completo), 200
    return jsonify({"msg": "Utente non trovato"}), 404

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    from flask_jwt_extended import get_jwt
    
    current_user_id = get_jwt_identity()
    claims = get_jwt()
    
    # Mantieni i claim esistenti nel nuovo token
    additional_claims = {
        "user_type": claims.get("user_type", "admin"),
        "utente_id": claims.get("utente_id"),
        "username": claims.get("username")
    }
    
    new_access_token = create_access_token(
        identity=current_user_id,
        additional_claims=additional_claims
    )
    return jsonify(access_token=new_access_token), 200