import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente dal file .env PRIMA di importare l'app,
# nel caso in cui la configurazione dell'app dipenda da esse.
load_dotenv()

from app import app # Importa l'istanza app da app/__init__.py

if __name__ == '__main__':
    # Ottieni la porta dalla variabile d'ambiente o usa 5000 come default
    port = int(os.getenv('FLASK_RUN_PORT', 5001))
    # Avvia l'applicazione Flask
    # debug=True è utile per lo sviluppo, host='0.0.0.0' la rende accessibile esternamente
    app.run(debug=True, host='0.0.0.0', port=port)