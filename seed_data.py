from app import app, db
from app.models import Parametri, ValoriParametro, Utente, RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, TipoControlloUI
from werkzeug.security import generate_password_hash, check_password_hash

with app.app_context():
    print("Inizio seeding database...")

    # Seeding Utente Amministratore
    admin_username = "admin"
    admin_password = "secure_admin_password"  # In un'app reale, utilizzare variabili d'ambiente
    admin_email = "<EMAIL>"

    admin_user = Utente.query.filter_by(username=admin_username).first()
    if not admin_user:
        admin_user = Utente(username=admin_username, email=admin_email, attivo=True)
        admin_user.set_password(admin_password)
        db.session.add(admin_user)
        db.session.commit()
        print(f"Aggiunto utente amministratore: {admin_username}")
    else:
        print(f"Utente amministratore '{admin_username}' esistente, salto.")

    # Seeding Parametro "Lavorazioni" con is_root=True
    lavorazioni_param_data = {
        "nome_parametro": "Lavorazioni",
        "tipo_controllo_ui": TipoControlloUI.SELECT,
        "is_root": True,
        "attivo": True
    }
    lavorazioni_param_obj = Parametri.query.filter_by(nome_parametro=lavorazioni_param_data["nome_parametro"]).first()
    if not lavorazioni_param_obj:
        lavorazioni_param_obj = Parametri(**lavorazioni_param_data)
        db.session.add(lavorazioni_param_obj)
        db.session.commit()  # Commit per ottenere l'ID
        print(f"Aggiunto Parametro Root: {lavorazioni_param_data['nome_parametro']}")
    else:
        print(f"Parametro Root '{lavorazioni_param_data['nome_parametro']}' esistente, salto.")

    # Seeding ValoriParametro per "Lavorazioni"
    valori_lavorazioni = [
        {"testo_visualizzato_ui": "Elemento Singolo/Intarsio", "ordine_visualizzazione": 1},
        {"testo_visualizzato_ui": "Ponte", "ordine_visualizzazione": 2},
        {"testo_visualizzato_ui": "Elemento Singolo Avvitato", "ordine_visualizzazione": 3},
    ]
    if lavorazioni_param_obj:
        for val_data in valori_lavorazioni:
            val_obj = ValoriParametro.query.filter_by(
                id_parametro=lavorazioni_param_obj.id_parametro,
                testo_visualizzato_ui=val_data["testo_visualizzato_ui"]
            ).first()
            if not val_obj:
                val_obj = ValoriParametro(id_parametro=lavorazioni_param_obj.id_parametro, **val_data)
                db.session.add(val_obj)
                print(f"Aggiunto ValoreParametro: {val_data['testo_visualizzato_ui']} per {lavorazioni_param_obj.nome_parametro}")
            else:
                print(f"ValoreParametro '{val_data['testo_visualizzato_ui']}' per '{lavorazioni_param_obj.nome_parametro}' esistente, salto.")
        db.session.commit()
    else:
        print("Impossibile aggiungere ValoriParametro per 'Lavorazioni': Parametro root non trovato.")

    # Seeding Parametro "Tipologia Impianto"
    tipo_impianto_param_data = {
        "nome_parametro": "Tipologia Impianto",
        "tipo_controllo_ui": TipoControlloUI.SELECT,
        "is_root": False,
        "attivo": True
    }
    tipo_impianto_param_obj = Parametri.query.filter_by(nome_parametro=tipo_impianto_param_data["nome_parametro"]).first()
    if not tipo_impianto_param_obj:
        tipo_impianto_param_obj = Parametri(**tipo_impianto_param_data)
        db.session.add(tipo_impianto_param_obj)
        db.session.commit()
        print(f"Aggiunto Parametro: {tipo_impianto_param_data['nome_parametro']}")
    else:
        print(f"Parametro '{tipo_impianto_param_data['nome_parametro']}' esistente, salto.")

    # Seeding ValoriParametro per "Tipologia Impianto"
    valori_tipo_impianto = [
        {"testo_visualizzato_ui": "Impianto Tipo A", "ordine_visualizzazione": 1},
        {"testo_visualizzato_ui": "Impianto Tipo B", "ordine_visualizzazione": 2},
    ]
    if tipo_impianto_param_obj:
        for val_data in valori_tipo_impianto:
            val_obj = ValoriParametro.query.filter_by(
                id_parametro=tipo_impianto_param_obj.id_parametro,
                testo_visualizzato_ui=val_data["testo_visualizzato_ui"]
            ).first()
            if not val_obj:
                val_obj = ValoriParametro(id_parametro=tipo_impianto_param_obj.id_parametro, **val_data)
                db.session.add(val_obj)
                print(f"Aggiunto ValoreParametro: {val_data['testo_visualizzato_ui']} per {tipo_impianto_param_obj.nome_parametro}")
            else:
                print(f"ValoreParametro '{val_data['testo_visualizzato_ui']}' per '{tipo_impianto_param_obj.nome_parametro}' esistente, salto.")
        db.session.commit()

    # Seeding RegolaDipendenzaComplessa:
    # Esempio: se "Lavorazioni" è "Elemento Singolo Avvitato", allora "Tipologia Impianto" deve essere "Impianto Tipo A"
    lavorazioni_param = Parametri.query.filter_by(nome_parametro="Lavorazioni").first()
    elem_singolo_avvitato_val = ValoriParametro.query.filter_by(
        id_parametro=lavorazioni_param.id_parametro,
        testo_visualizzato_ui="Elemento Singolo Avvitato"
    ).first()

    tipo_impianto_param = Parametri.query.filter_by(nome_parametro="Tipologia Impianto").first()
    impianto_tipo_a_val = ValoriParametro.query.filter_by(
        id_parametro=tipo_impianto_param.id_parametro,
        testo_visualizzato_ui="Impianto Tipo A"
    ).first()

    if all([lavorazioni_param, elem_singolo_avvitato_val, tipo_impianto_param, impianto_tipo_a_val]):
        regola_descrizione = "Se Lavorazioni='Elemento Singolo Avvitato', allora Tipologia Impianto='Impianto Tipo A'"
        regola_obj = RegolaDipendenzaComplessa.query.filter_by(descrizione=regola_descrizione).first()

        if not regola_obj:
            regola_obj = RegolaDipendenzaComplessa(
                nome_regola="Dipendenza Lavorazione-Impianto",
                descrizione=regola_descrizione,
                attiva=True,
                logica_combinazione_condizioni="AND"
            )
            db.session.add(regola_obj)
            db.session.commit()  # Commit per ottenere l'ID della regola

            # Creazione della condizione collegata alla regola
            condizione1 = CondizioniRegola(
                id_regola=regola_obj.id,
                id_parametro_condizionante=lavorazioni_param.id_parametro,
                id_valore_condizione_predefinita=elem_singolo_avvitato_val.id_valore_parametro,
                tipo_condizione="EQUALS"
            )
            db.session.add(condizione1)

            # Creazione del risultato collegato alla regola
            risultato1 = RisultatiRegola(
                id_regola=regola_obj.id,
                id_parametro_effetto=tipo_impianto_param.id_parametro,
                id_valore_effetto_predefinito=impianto_tipo_a_val.id_valore_parametro,
                tipo_effetto="SET_VALUE"
            )
            db.session.add(risultato1)

            db.session.commit()
            print(f"Aggiunta Regola Dipendenza Complessa: '{regola_descrizione}'")
        else:
            print(f"Regola Dipendenza Complessa '{regola_descrizione}' esistente, salto.")
    else:
        print("Impossibile creare Regola Dipendenza Complessa: parametri o valori necessari non trovati.")

    print("Seeding database completato.")