"""Initial migration.

Revision ID: 5c44f996e03f
Revises: 
Create Date: 2025-06-17 15:16:06.821476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c44f996e03f'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('parametri',
    sa.Column('id_parametro', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('nome_parametro', sa.String(length=255), nullable=False),
    sa.Column('tipo_controllo_ui', sa.Enum('SELECT', 'RADIO', 'CHECKBOX_GROUP', 'INPUT_TEXT', 'INPUT_NUMBER', 'TEXTAREA', name='tipocontrolloui'), nullable=False),
    sa.Column('descrizione', sa.TEXT(), nullable=True),
    sa.Column('foto', sa.String(length=255), nullable=True),
    sa.Column('is_root', sa.Boolean(), nullable=False),
    sa.Column('attivo', sa.Boolean(), nullable=False),
    sa.Column('data_creazione', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('data_modifica', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id_parametro')
    )
    op.create_table('regole_dipendenza_complessa',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('nome_regola', sa.String(length=255), nullable=False),
    sa.Column('descrizione', sa.TEXT(), nullable=True),
    sa.Column('attiva', sa.Boolean(), nullable=False),
    sa.Column('logica_combinazione_condizioni', sa.String(length=10), nullable=False),
    sa.Column('data_creazione', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('data_modifica', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('utente',
    sa.Column('id_utente', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=80), nullable=False),
    sa.Column('hashed_password', sa.String(length=128), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('nome_completo', sa.String(length=255), nullable=True),
    sa.Column('attivo', sa.Boolean(), nullable=False),
    sa.Column('data_creazione', sa.DateTime(), nullable=True),
    sa.Column('data_ultima_modifica', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id_utente'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_table('valoriparametro',
    sa.Column('id_valore_parametro', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('id_parametro', sa.Integer(), nullable=False),
    sa.Column('testo_visualizzato_ui', sa.String(length=255), nullable=False),
    sa.Column('ordine_visualizzazione', sa.Integer(), nullable=False),
    sa.Column('data_creazione', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('data_modifica', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('foto', sa.String(length=255), nullable=True),
    sa.Column('colore', sa.String(length=50), nullable=True),
    sa.Column('descrizione', sa.TEXT(), nullable=True),
    sa.ForeignKeyConstraint(['id_parametro'], ['parametri.id_parametro'], ),
    sa.PrimaryKeyConstraint('id_valore_parametro')
    )
    op.create_table('condizioni_regola',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('id_regola', sa.Integer(), nullable=False),
    sa.Column('id_parametro_condizionante', sa.Integer(), nullable=False),
    sa.Column('id_valore_condizione_predefinita', sa.Integer(), nullable=True),
    sa.Column('valore_condizione_libero', sa.String(length=255), nullable=True),
    sa.Column('tipo_condizione', sa.String(length=50), nullable=False),
    sa.Column('ordine_valutazione', sa.Integer(), nullable=False),
    sa.Column('data_creazione', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('data_modifica', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['id_parametro_condizionante'], ['parametri.id_parametro'], ),
    sa.ForeignKeyConstraint(['id_regola'], ['regole_dipendenza_complessa.id'], ),
    sa.ForeignKeyConstraint(['id_valore_condizione_predefinita'], ['valoriparametro.id_valore_parametro'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('risultati_regola',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('id_regola', sa.Integer(), nullable=False),
    sa.Column('id_parametro_effetto', sa.Integer(), nullable=False),
    sa.Column('tipo_effetto', sa.String(length=50), nullable=False),
    sa.Column('id_valore_effetto_predefinito', sa.Integer(), nullable=True),
    sa.Column('valore_effetto_libero', sa.String(length=255), nullable=True),
    sa.Column('data_creazione', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('data_modifica', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['id_parametro_effetto'], ['parametri.id_parametro'], ),
    sa.ForeignKeyConstraint(['id_regola'], ['regole_dipendenza_complessa.id'], ),
    sa.ForeignKeyConstraint(['id_valore_effetto_predefinito'], ['valoriparametro.id_valore_parametro'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('risultati_regola')
    op.drop_table('condizioni_regola')
    op.drop_table('valoriparametro')
    op.drop_table('utente')
    op.drop_table('regole_dipendenza_complessa')
    op.drop_table('parametri')
    # ### end Alembic commands ###
