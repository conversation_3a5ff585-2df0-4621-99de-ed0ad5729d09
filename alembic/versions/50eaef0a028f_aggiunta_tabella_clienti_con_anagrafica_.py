"""Aggiunta tabella clienti con anagrafica completa

Revision ID: 50eaef0a028f
Revises: 6d24f4c5fffc
Create Date: 2025-07-03 12:13:39.671145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50eaef0a028f'
down_revision: Union[str, None] = '6d24f4c5fffc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('clienti',
    sa.Column('id_cliente', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=80), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('nome', sa.String(length=100), nullable=False),
    sa.Column('cognome', sa.String(length=100), nullable=False),
    sa.Column('ragione_sociale', sa.String(length=255), nullable=True),
    sa.Column('partita_iva', sa.String(length=20), nullable=True),
    sa.Column('codice_fiscale', sa.String(length=16), nullable=True),
    sa.Column('telefono', sa.String(length=20), nullable=True),
    sa.Column('cellulare', sa.String(length=20), nullable=True),
    sa.Column('indirizzo', sa.String(length=255), nullable=True),
    sa.Column('citta', sa.String(length=100), nullable=True),
    sa.Column('cap', sa.String(length=10), nullable=True),
    sa.Column('provincia', sa.String(length=5), nullable=True),
    sa.Column('nazione', sa.String(length=100), nullable=False),
    sa.Column('iban', sa.String(length=34), nullable=True),
    sa.Column('modalita_pagamento', sa.String(length=100), nullable=True),
    sa.Column('codice_sdi', sa.String(length=7), nullable=True),
    sa.Column('indirizzo_pec', sa.String(length=255), nullable=True),
    sa.Column('note', sa.TEXT(), nullable=True),
    sa.Column('attivo', sa.Boolean(), nullable=False),
    sa.Column('data_creazione', sa.DateTime(), nullable=True),
    sa.Column('data_ultima_modifica', sa.DateTime(), nullable=True),
    sa.Column('ultimo_accesso', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id_cliente'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_unique_constraint(None, 'regole_dipendenza_complessa', ['nome_regola'])
    op.drop_constraint(op.f('risultati_regola_ibfk_2'), 'risultati_regola', type_='foreignkey')
    op.drop_constraint(op.f('risultati_regola_ibfk_3'), 'risultati_regola', type_='foreignkey')
    op.drop_constraint(op.f('risultati_regola_ibfk_1'), 'risultati_regola', type_='foreignkey')
    op.create_foreign_key(None, 'risultati_regola', 'parametri', ['id_parametro_effetto'], ['id_parametro'])
    op.create_foreign_key(None, 'risultati_regola', 'regole_dipendenza_complessa', ['id_regola'], ['id'])
    op.create_foreign_key(None, 'risultati_regola', 'valoriparametro', ['id_valore_effetto_predefinito'], ['id_valore_parametro'])
    op.drop_constraint(op.f('valoriparametro_ibfk_1'), 'valoriparametro', type_='foreignkey')
    op.create_foreign_key(None, 'valoriparametro', 'parametri', ['id_parametro'], ['id_parametro'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'valoriparametro', type_='foreignkey')
    op.create_foreign_key(op.f('valoriparametro_ibfk_1'), 'valoriparametro', 'parametri', ['id_parametro'], ['id_parametro'], ondelete='CASCADE')
    op.drop_constraint(None, 'risultati_regola', type_='foreignkey')
    op.drop_constraint(None, 'risultati_regola', type_='foreignkey')
    op.drop_constraint(None, 'risultati_regola', type_='foreignkey')
    op.create_foreign_key(op.f('risultati_regola_ibfk_1'), 'risultati_regola', 'parametri', ['id_parametro_effetto'], ['id_parametro'], ondelete='CASCADE')
    op.create_foreign_key(op.f('risultati_regola_ibfk_3'), 'risultati_regola', 'valoriparametro', ['id_valore_effetto_predefinito'], ['id_valore_parametro'], ondelete='CASCADE')
    op.create_foreign_key(op.f('risultati_regola_ibfk_2'), 'risultati_regola', 'regole_dipendenza_complessa', ['id_regola'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'regole_dipendenza_complessa', type_='unique')
    op.drop_table('clienti')
    # ### end Alembic commands ###
