"""add_ordine_visualizzazione_to_parametri

Revision ID: 6d24f4c5fffc
Revises: 2691dbd91a8d
Create Date: 2025-06-27 14:07:51.127920

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6d24f4c5fffc'
down_revision: Union[str, None] = '2691dbd91a8d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Aggiungi il campo ordine_visualizzazione alla tabella parametri
    op.add_column('parametri', sa.Column('ordine_visualizzazione', sa.Integer(), nullable=False, server_default='0'))
    
    # Imposta valori di ordinamento iniziali basati sull'ID per mantenere l'ordine attuale
    op.execute("""
        UPDATE parametri 
        SET ordine_visualizzazione = CASE 
            WHEN nome_parametro = 'Lavorazioni' THEN 1
            WHEN nome_parametro = 'Tipologia dente' THEN 2
            WHEN nome_parametro = 'Tipologia Impianto' THEN 3
            WHEN nome_parametro = 'Tecnica' THEN 4
            WHEN nome_parametro = 'Connessione' THEN 5
            WHEN nome_parametro = 'Diametro' THEN 6
            ELSE id_parametro + 100
        END
    """)


def downgrade() -> None:
    """Downgrade schema."""
    # Rimuovi il campo ordine_visualizzazione dalla tabella parametri
    op.drop_column('parametri', 'ordine_visualizzazione')
