#!/usr/bin/env python3
"""
Script per creare clienti di test nel database
Crea 5 clienti con dati realistici italiani per testing e sviluppo
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Aggiungi il path dell'app per gli import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from app.models import Cliente
from app.extensions import db

# Dati di test realistici italiani
CLIENTI_TEST = [
    {
        "username": "mario.rossi",
        "email": "<EMAIL>",
        "password": "password123",
        "nome": "<PERSON>",
        "cognome": "<PERSON>",
        "ragione_sociale": None,
        "partita_iva": "12345678901",
        "codice_fiscale": "****************",
        "telefono": "02-1234567",
        "cellulare": "333-1234567",
        "indirizzo": "Via Roma 123",
        "citta": "<PERSON>",
        "cap": "20100",
        "provincia": "MI",
        "nazione": "Italia",
        "iban": "***************************",
        "modalita_pagamento": "Bonifico bancario",
        "codice_sdi": "ABCD123",
        "indirizzo_pec": "<EMAIL>",
        "note": "Cliente privato di Milano, affidabile nei pagamenti",
        "attivo": True
    },
    {
        "username": "giulia.bianchi",
        "email": "<EMAIL>",
        "password": "password123",
        "nome": "Giulia",
        "cognome": "Bianchi",
        "ragione_sociale": "Studio Dentistico Bianchi SRL",
        "partita_iva": "98765432109",
        "codice_fiscale": "****************",
        "telefono": "06-9876543",
        "cellulare": "347-9876543",
        "indirizzo": "Via del Corso 456",
        "citta": "Roma",
        "cap": "00186",
        "provincia": "RM",
        "nazione": "Italia",
        "iban": "***************************",
        "modalita_pagamento": "RID/SDD",
        "codice_sdi": "EFGH456",
        "indirizzo_pec": "<EMAIL>",
        "note": "Studio dentistico con 3 dentisti, ordini frequenti",
        "attivo": True
    },
    {
        "username": "luca.verdi",
        "email": "<EMAIL>",
        "password": "password123",
        "nome": "Luca",
        "cognome": "Verdi",
        "ragione_sociale": None,
        "partita_iva": "11223344556",
        "codice_fiscale": "****************",
        "telefono": "011-5555555",
        "cellulare": "320-5555555",
        "indirizzo": "Corso Vittorio Emanuele 789",
        "citta": "Torino",
        "cap": "10121",
        "provincia": "TO",
        "nazione": "Italia",
        "iban": "***************************",
        "modalita_pagamento": "Bonifico bancario",
        "codice_sdi": "IJKL789",
        "indirizzo_pec": "<EMAIL>",
        "note": "Odontotecnico freelance, specializzato in protesi",
        "attivo": True
    },
    {
        "username": "anna.ferrari",
        "email": "<EMAIL>",
        "password": "password123",
        "nome": "Anna",
        "cognome": "Ferrari",
        "ragione_sociale": "Dental Lab Ferrari & Associati",
        "partita_iva": "55667788990",
        "codice_fiscale": "****************",
        "telefono": "051-7777777",
        "cellulare": "339-7777777",
        "indirizzo": "Via Indipendenza 321",
        "citta": "Bologna",
        "cap": "40121",
        "provincia": "BO",
        "nazione": "Italia",
        "iban": "***************************",
        "modalita_pagamento": "Carta di credito",
        "codice_sdi": "MNOP012",
        "indirizzo_pec": "<EMAIL>",
        "note": "Laboratorio odontotecnico con 5 tecnici specializzati",
        "attivo": True
    },
    {
        "username": "marco.esposito",
        "email": "<EMAIL>",
        "password": "password123",
        "nome": "Marco",
        "cognome": "Esposito",
        "ragione_sociale": None,
        "partita_iva": "99887766554",
        "codice_fiscale": "****************",
        "telefono": "081-3333333",
        "cellulare": "366-3333333",
        "indirizzo": "Via Toledo 654",
        "citta": "Napoli",
        "cap": "80134",
        "provincia": "NA",
        "nazione": "Italia",
        "iban": "***************************",
        "modalita_pagamento": "Contanti",
        "codice_sdi": "QRST345",
        "indirizzo_pec": "<EMAIL>",
        "note": "Cliente occasionale, preferisce pagamenti in contanti",
        "attivo": False  # Cliente disattivato per test
    }
]

def create_test_clients():
    """Crea i clienti di test nel database"""
    
    print("🚀 CREAZIONE CLIENTI DI TEST")
    print("=" * 50)
    
    with app.app_context():
        created_count = 0
        updated_count = 0
        
        for cliente_data in CLIENTI_TEST:
            username = cliente_data["username"]
            
            # Verifica se il cliente esiste già
            existing_cliente = Cliente.query.filter_by(username=username).first()
            
            if existing_cliente:
                print(f"⚠️  Cliente '{username}' già esistente, aggiorno i dati...")
                
                # Aggiorna i dati esistenti
                for key, value in cliente_data.items():
                    if key != 'password':  # Non aggiornare la password
                        setattr(existing_cliente, key, value)
                
                existing_cliente.data_ultima_modifica = datetime.now()
                updated_count += 1
                
            else:
                print(f"✅ Creazione cliente '{username}'...")
                
                # Crea nuovo cliente
                password = cliente_data.pop('password')
                cliente = Cliente(**cliente_data)
                cliente.set_password(password)
                
                # Simula date di creazione diverse per test
                days_ago = random.randint(1, 90)
                cliente.data_creazione = datetime.now() - timedelta(days=days_ago)
                
                # Simula ultimo accesso per alcuni clienti
                if random.choice([True, False]):
                    access_days_ago = random.randint(1, days_ago)
                    cliente.ultimo_accesso = datetime.now() - timedelta(days=access_days_ago)
                
                db.session.add(cliente)
                created_count += 1
        
        # Salva le modifiche
        try:
            db.session.commit()
            print(f"\n🎉 OPERAZIONE COMPLETATA!")
            print(f"   📊 Clienti creati: {created_count}")
            print(f"   🔄 Clienti aggiornati: {updated_count}")
            print(f"   📈 Totale clienti: {created_count + updated_count}")
            
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ ERRORE durante il salvataggio: {str(e)}")
            return False
    
    return True

def show_test_credentials():
    """Mostra le credenziali di test create"""
    
    print("\n📋 CREDENZIALI CLIENTI DI TEST")
    print("=" * 50)
    
    with app.app_context():
        clienti = Cliente.query.filter(
            Cliente.username.in_([c["username"] for c in CLIENTI_TEST])
        ).all()
        
        for cliente in clienti:
            status = "🟢 ATTIVO" if cliente.attivo else "🔴 DISATTIVO"
            print(f"\n👤 {cliente.denominazione}")
            print(f"   Username: {cliente.username}")
            print(f"   Email: {cliente.email}")
            print(f"   Password: password123")
            print(f"   Stato: {status}")
            print(f"   Città: {cliente.citta}")
            print(f"   Tipo: {'Azienda' if cliente.ragione_sociale else 'Privato'}")

def show_statistics():
    """Mostra statistiche sui clienti"""
    
    print("\n📊 STATISTICHE CLIENTI")
    print("=" * 30)
    
    with app.app_context():
        total = Cliente.query.count()
        attivi = Cliente.query.filter_by(attivo=True).count()
        disattivi = Cliente.query.filter_by(attivo=False).count()
        
        # Clienti per città
        citta_stats = db.session.query(
            Cliente.citta, 
            db.func.count(Cliente.id_cliente)
        ).group_by(Cliente.citta).all()
        
        print(f"📈 Totale clienti: {total}")
        print(f"🟢 Clienti attivi: {attivi}")
        print(f"🔴 Clienti disattivi: {disattivi}")
        print(f"💯 Percentuale attivi: {round(attivi/total*100, 1)}%")
        
        print(f"\n🏙️  Distribuzione per città:")
        for citta, count in citta_stats:
            print(f"   {citta}: {count} clienti")

def test_client_login():
    """Testa il login di un cliente"""
    
    print("\n🧪 TEST LOGIN CLIENTE")
    print("=" * 25)
    
    from app import app
    
    with app.test_client() as client:
        # Test login con il primo cliente
        test_cliente = CLIENTI_TEST[0]
        
        login_data = {
            'username': test_cliente['username'],
            'password': 'password123'
        }
        
        response = client.post('/api/client/auth/login', 
                              json=login_data,
                              headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.get_json()
            print(f"✅ Login riuscito per {test_cliente['username']}")
            print(f"   Token presente: {'access_token' in data}")
            print(f"   User data: {data.get('user', {}).get('denominazione')}")
        else:
            print(f"❌ Login fallito: {response.get_json()}")

def main():
    """Funzione principale"""
    
    print("🎯 SCRIPT CREAZIONE CLIENTI DI TEST")
    print("Questo script crea 5 clienti con dati realistici italiani")
    print("Password per tutti i clienti: password123")
    print()
    
    # Verifica connessione database
    try:
        with app.app_context():
            with db.engine.connect() as conn:
                conn.execute(db.text('SELECT 1'))
        print("✅ Connessione database OK")
    except Exception as e:
        print(f"❌ Errore connessione database: {e}")
        return
    
    # Crea i clienti
    if create_test_clients():
        show_test_credentials()
        show_statistics()
        test_client_login()
        
        print(f"\n🚀 SCRIPT COMPLETATO CON SUCCESSO!")
        print(f"\n💡 COME USARE I CLIENTI DI TEST:")
        print(f"   1. Accedi al frontend admin: http://localhost:3000/admin/clienti")
        print(f"   2. Testa il login cliente: http://localhost:3000/login")
        print(f"   3. Usa username/email e password 'password123'")
        print(f"   4. Verifica le funzionalità CRUD nell'admin")
        
    else:
        print(f"\n❌ SCRIPT FALLITO!")

if __name__ == "__main__":
    main()