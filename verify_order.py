#!/usr/bin/env python3
"""
Script per verificare l'ordine di visualizzazione dei parametri
"""

import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

from app import app
from app.extensions import db
from app.models import Parametri

def verify_parameter_order():
    with app.app_context():
        print("=== VERIFICA ORDINE PARAMETRI ===")
        
        # Recupera tutti i parametri ordinati per ordine_visualizzazione
        parametri = Parametri.query.order_by(Parametri.ordine_visualizzazione).all()
        
        print(f"Parametri ordinati per ordine_visualizzazione:")
        for p in parametri:
            print(f"  {p.ordine_visualizzazione}: {p.nome_parametro} (ID: {p.id_parametro}, Root: {p.is_root})")
        
        print(f"\n=== FINE VERIFICA ===")

if __name__ == "__main__":
    verify_parameter_order()