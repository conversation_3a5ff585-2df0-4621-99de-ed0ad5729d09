#!/usr/bin/env python3
"""
Script per testare tutte le API dei clienti con i dati di test
Verifica che tutte le funzionalità CRUD funzionino correttamente
"""

import sys
import os

# Aggiungi il path dell'app per gli import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app

def test_admin_api():
    """Testa tutte le API admin per i clienti"""
    
    print("🧪 TEST COMPLETO API ADMIN CLIENTI")
    print("=" * 50)
    
    with app.test_client() as client:
        # Login admin
        admin_login = client.post('/api/auth/login', 
                                 json={'username': 'admin', 'password': 'admin'})
        
        if admin_login.status_code != 200:
            print("❌ Errore login admin")
            return False
            
        admin_token = admin_login.get_json()['access_token']
        headers = {'Authorization': f'Bearer {admin_token}'}
        
        print("✅ Admin login riuscito")
        
        # Test 1: Lista clienti
        print("\n1️⃣ Test lista clienti...")
        list_response = client.get('/api/admin/clienti', headers=headers)
        assert list_response.status_code == 200
        list_data = list_response.get_json()
        print(f"   ✅ Lista: {len(list_data['clienti'])} clienti trovati")
        
        # Test 2: Lista con filtri
        print("\n2️⃣ Test filtri...")
        filter_response = client.get('/api/admin/clienti?search=Mario&stato=attivo', headers=headers)
        assert filter_response.status_code == 200
        filter_data = filter_response.get_json()
        print(f"   ✅ Filtri: {len(filter_data['clienti'])} clienti filtrati")
        
        # Test 3: Statistiche
        print("\n3️⃣ Test statistiche...")
        stats_response = client.get('/api/admin/clienti/stats', headers=headers)
        assert stats_response.status_code == 200
        stats_data = stats_response.get_json()['stats']
        print(f"   ✅ Stats: {stats_data['total_clienti']} totali, {stats_data['clienti_attivi']} attivi")
        
        # Test 4: Dettagli cliente
        print("\n4️⃣ Test dettagli cliente...")
        if list_data['clienti']:
            cliente_id = list_data['clienti'][0]['id_cliente']
            detail_response = client.get(f'/api/admin/clienti/{cliente_id}', headers=headers)
            assert detail_response.status_code == 200
            detail_data = detail_response.get_json()['cliente']
            print(f"   ✅ Dettagli: {detail_data['denominazione']}")
        
        # Test 5: Toggle status
        print("\n5️⃣ Test toggle status...")
        if list_data['clienti']:
            cliente_id = list_data['clienti'][0]['id_cliente']
            toggle_response = client.put(f'/api/admin/clienti/{cliente_id}/toggle-status', headers=headers)
            assert toggle_response.status_code == 200
            print(f"   ✅ Toggle status: {toggle_response.get_json()['message']}")
            
            # Ripristina stato originale
            client.put(f'/api/admin/clienti/{cliente_id}/toggle-status', headers=headers)
        
        # Test 6: Reset password
        print("\n6️⃣ Test reset password...")
        if list_data['clienti']:
            cliente_id = list_data['clienti'][0]['id_cliente']
            reset_data = {'new_password': 'nuovapassword123'}
            reset_response = client.put(f'/api/admin/clienti/{cliente_id}/reset-password', 
                                       json=reset_data, headers=headers)
            assert reset_response.status_code == 200
            print(f"   ✅ Reset password: {reset_response.get_json()['message']}")
        
        print(f"\n🎉 TUTTI I TEST API ADMIN SUPERATI!")
        return True

def test_client_api():
    """Testa le API di autenticazione clienti"""
    
    print("\n🧪 TEST API AUTENTICAZIONE CLIENTI")
    print("=" * 40)
    
    with app.test_client() as client:
        # Test login clienti
        clienti_test = [
            {'username': 'mario.rossi', 'password': 'password123'},
            {'username': '<EMAIL>', 'password': 'password123'},  # Test login con email
            {'username': 'luca.verdi', 'password': 'password123'},
        ]
        
        for i, cliente_data in enumerate(clienti_test, 1):
            print(f"\n{i}️⃣ Test login {cliente_data['username']}...")
            
            # Login
            login_response = client.post('/api/client/auth/login', json=cliente_data)
            if login_response.status_code == 200:
                tokens = login_response.get_json()
                access_token = tokens['access_token']
                refresh_token = tokens['refresh_token']
                user_data = tokens['user']
                
                print(f"   ✅ Login: {user_data['denominazione']}")
                
                # Test endpoint /me
                headers = {'Authorization': f'Bearer {access_token}'}
                me_response = client.get('/api/client/auth/me', headers=headers)
                assert me_response.status_code == 200
                me_data = me_response.get_json()
                print(f"   ✅ /me: {me_data['nome_completo']}")
                
                # Test refresh token
                refresh_headers = {'Authorization': f'Bearer {refresh_token}'}
                refresh_response = client.post('/api/client/auth/refresh', headers=refresh_headers)
                assert refresh_response.status_code == 200
                print(f"   ✅ Refresh token funzionante")
                
                # Test aggiornamento profilo
                profile_data = {'note': f'Aggiornato via test API {i}'}
                profile_response = client.put('/api/client/auth/profile', 
                                            json=profile_data, headers=headers)
                assert profile_response.status_code == 200
                print(f"   ✅ Aggiornamento profilo")
                
            else:
                print(f"   ❌ Login fallito: {login_response.get_json()}")
        
        print(f"\n🎉 TUTTI I TEST API CLIENTI SUPERATI!")
        return True

def test_authorization():
    """Testa la separazione delle autorizzazioni"""
    
    print("\n🧪 TEST SEPARAZIONE AUTORIZZAZIONI")
    print("=" * 35)
    
    with app.test_client() as client:
        # Login admin e cliente
        admin_login = client.post('/api/auth/login', 
                                 json={'username': 'admin', 'password': 'admin'})
        admin_token = admin_login.get_json()['access_token']
        admin_headers = {'Authorization': f'Bearer {admin_token}'}
        
        client_login = client.post('/api/client/auth/login', 
                                  json={'username': 'mario.rossi', 'password': 'password123'})
        client_token = client_login.get_json()['access_token']
        client_headers = {'Authorization': f'Bearer {client_token}'}
        
        print("✅ Login admin e cliente riusciti")
        
        # Test 1: Cliente non può accedere ad admin endpoints
        print("\n1️⃣ Test cliente -> admin endpoints...")
        admin_access = client.get('/api/admin/clienti', headers=client_headers)
        assert admin_access.status_code == 403
        print("   ✅ Accesso negato correttamente")
        
        # Test 2: Admin non può accedere a client endpoints
        print("\n2️⃣ Test admin -> client endpoints...")
        client_access = client.get('/api/client/auth/me', headers=admin_headers)
        assert client_access.status_code == 403
        print("   ✅ Accesso negato correttamente")
        
        # Test 3: Admin può accedere ad admin endpoints
        print("\n3️⃣ Test admin -> admin endpoints...")
        admin_to_admin = client.get('/api/admin/clienti', headers=admin_headers)
        assert admin_to_admin.status_code == 200
        print("   ✅ Accesso consentito correttamente")
        
        # Test 4: Cliente può accedere a client endpoints
        print("\n4️⃣ Test cliente -> client endpoints...")
        client_to_client = client.get('/api/client/auth/me', headers=client_headers)
        assert client_to_client.status_code == 200
        print("   ✅ Accesso consentito correttamente")
        
        print(f"\n🎉 TUTTI I TEST AUTORIZZAZIONI SUPERATI!")
        return True

def main():
    """Funzione principale"""
    
    print("🎯 TEST COMPLETO SISTEMA CLIENTI")
    print("Verifica tutte le funzionalità implementate")
    print()
    
    try:
        # Test API Admin
        if not test_admin_api():
            print("❌ Test API Admin falliti")
            return
        
        # Test API Clienti
        if not test_client_api():
            print("❌ Test API Clienti falliti")
            return
        
        # Test Autorizzazioni
        if not test_authorization():
            print("❌ Test Autorizzazioni falliti")
            return
        
        print(f"\n🏆 TUTTI I TEST SUPERATI CON SUCCESSO!")
        print(f"\n📋 RIEPILOGO FUNZIONALITÀ TESTATE:")
        print(f"   ✅ CRUD completo clienti (admin)")
        print(f"   ✅ Filtri, ricerca e paginazione")
        print(f"   ✅ Statistiche e dashboard")
        print(f"   ✅ Autenticazione clienti")
        print(f"   ✅ Gestione profilo cliente")
        print(f"   ✅ Refresh token")
        print(f"   ✅ Separazione autorizzazioni admin/cliente")
        print(f"   ✅ Toggle status e reset password")
        print(f"   ✅ Validazioni e gestione errori")
        
        print(f"\n🚀 SISTEMA PRONTO PER L'USO!")
        
    except AssertionError as e:
        print(f"\n❌ TEST FALLITO: {e}")
    except Exception as e:
        print(f"\n❌ ERRORE DURANTE I TEST: {e}")

if __name__ == "__main__":
    main()