
# Project Agent Guide

## Project Overview

This is a **dental laboratory management system** for dynamic configuration of dental work options and parameter dependencies. The system consists of a Flask backend with RESTful APIs and a React TypeScript frontend for administration.

### Purpose
- Manage dental work parameters and their values
- Define complex dependency rules between parameters
- Provide dynamic UI configuration based on parameter relationships
- Secure admin interface for CRUD operations

### Tech Stack
- **Backend**: Python Flask, SQLAlchemy, Alembic, Flask-JWT-Extended, Marshmallow
- **Database**: MySQL
- **Frontend**: React + Vite + TypeScript + HeroUI + TanStack Query + React Hook Form + Zod
- **Authentication**: JWT with refresh tokens

## Project Structure

### Backend (`app/`)
```
app/
├── __init__.py          # Flask app initialization, CORS, JWT config
├── models.py            # SQLAlchemy models (Parametri, ValoriParametro, RegolaDipendenzaComplessa, etc.)
├── extensions.py        # Database instance
├── api_v1/              # Public API endpoints
├── api_admin/           # Admin CRUD endpoints
├── auth/                # Authentication routes
└── schemas/             # Marshmallow schemas
```

### Frontend (`frontend2/`)
```
frontend2/src/
├── components/          # Reusable UI components
├── pages/               # Page components
├── layouts/             # Layout components (AdminLayout, AuthLayout)
├── contexts/            # React contexts (AuthContext)
├── hooks/               # Custom hooks for API calls
├── services/            # API service layer
├── types/               # TypeScript type definitions
└── schemas/             # Zod validation schemas
```

### Database Migrations (`alembic/`)
- Uses Alembic for database schema versioning
- Migration files in `alembic/versions/`

## Key Files and Directories

### Essential Backend Files
- `run.py` - Application entry point
- `app/__init__.py` - Flask app configuration
- `app/models.py` - Database models with complex relationships
- `app/api_admin/routes.py` - Admin API endpoints
- `app/api_v1/routes.py` - Public API endpoints
- `app/auth/routes.py` - Authentication endpoints

### Essential Frontend Files
- `frontend2/src/main.tsx` - React app entry point
- `frontend2/src/App.tsx` - Main app component with routing
- `frontend2/src/contexts/AuthContext.tsx` - Authentication state management
- `frontend2/src/services/api.ts` - Axios API client
- `frontend2/package.json` - Dependencies and scripts

### Configuration Files
- `.env` - Environment variables (DB connection, JWT secrets)
- `requirements.txt` - Python dependencies
- `alembic.ini` - Database migration configuration

## Database Models

### Core Entities
- **Utente**: User management with password hashing
- **Parametri**: Configurable parameters with UI control types
- **ValoriParametro**: Parameter values with display properties
- **RegolaDipendenzaComplessa**: Complex dependency rules
- **CondizioniRegola**: Rule conditions
- **RisultatiRegola**: Rule results/effects

### Key Relationships
- Parameters have multiple values (one-to-many)
- Rules have multiple conditions and results (one-to-many)
- Conditions reference parameters and values
- Results reference parameters and values

## API Endpoints

### Public API (`/api/v1/`)
- Parameter evaluation and dependency resolution
- Used by end-user interfaces

### Admin API (`/api/admin/`)
- Full CRUD for all entities
- Protected by JWT authentication
- Supports file uploads for images

### Authentication (`/api/auth/`)
- Login/logout with JWT tokens
- Token refresh mechanism

## Development Best Practices

### Backend Development
- Use Marshmallow schemas for request/response validation
- Implement proper error handling with appropriate HTTP status codes
- Use database transactions for complex operations
- Follow Flask blueprint pattern for modular code
- Use SQLAlchemy relationships and cascade operations

### Frontend Development
- Use TypeScript for type safety
- Implement proper error boundaries
- Use TanStack Query for server state management
- Validate forms with Zod schemas
- Use React Hook Form for form management
- Implement protected routes with authentication checks

### Database Operations
- Use Alembic for all schema changes
- Test migrations in development before applying to production
- Use proper foreign key constraints and cascading deletes
- Index frequently queried columns

### Security Considerations
- All admin endpoints require JWT authentication
- Use CORS configuration for cross-origin requests
- Hash passwords using Werkzeug security
- Validate all input data using schemas
- Use environment variables for sensitive configuration

## Environment Setup

### Backend Setup
1. Install Python dependencies: `pip install -r requirements.txt`
2. Configure `.env` file with database and JWT settings
3. Run database migrations: `alembic upgrade head`
4. Start server: `python run.py`

### Frontend Setup
1. Navigate to `frontend2/`
2. Install dependencies: `npm install`
3. Configure environment variables in `frontend2/.env`
4. Start development server: `npm run dev`

## Common Development Patterns

### Adding New API Endpoints
1. Define route in appropriate blueprint
2. Create Marshmallow schema for validation
3. Add corresponding frontend hook in `hooks/`
4. Update TypeScript types in `types/`

### Adding New Database Models
1. Define model in `app/models.py`
2. Create migration: `alembic revision --autogenerate -m "description"`
3. Review and apply migration: `alembic upgrade head`
4. Update API endpoints and schemas

### Frontend Form Development
1. Create Zod schema for validation
2. Define TypeScript types
3. Use React Hook Form with zodResolver
4. Implement proper error handling and loading states

#### Key Learnings for Future Reference

#### HeroUI Select Component: Summary of Integration Tips

1. **Key vs Value**:
   - Uses `selectedKeys` instead of traditional `value`, expecting an array of strings.
   - Example: `selectedKeys={field.value ? [\`\${field.value}\`] : []}`

2. **String Key Requirement**:
   - All keys (for both `selectedKeys` and `key` on `SelectItem`) must be strings.
   - Convert numeric IDs: `key={\`\${item.id}\`}`

3. **SelectItem Behavior**:
   - Each option must use `<SelectItem key="...">Label</SelectItem>`

4. **Selection Change Handling**:
   - `onSelectionChange` receives a `Set`; extract value using `Array.from(keys)[0]`
   - Convert back to number with `parseInt(...)` if needed.

5. **Controller Pattern (React Hook Form)**:
   - Use `<Controller>` to wrap `HeroUI <Select>`, handling string→number conversion on change.

6. **Zod Schema for Validation**:
   ```ts
   z.union([z.string(), z.number()])
     .transform(val => Number(val))
     .pipe(
       z.number().int('Must be an integer').min(1, 'Selection required')
     )
   ```

7. **Empty Selection**:
   - When no selection is made, the `Set` is empty. Handle with a fallback: `field.onChange(0)`

8. **Multiple Selection**:
   - Use `selectionMode="multiple"` and convert `Set` to array of numbers:
     ```ts
     Array.from(keys).map(k => parseInt(k.toString(), 10))
     ```

1. **Type Handling in Form Libraries**: When using React Hook Form with Zod and UI component libraries like HeroUI, be aware that form values may be passed as strings even when they represent numeric data.  
2. **Zod Schema Design**: For form fields that should be numbers but might come from select components:  
   - Use `z.union([z.string(), z.number()])` to accept both types  
   - Apply a `transform` to convert to numbers  
   - Then pipe to number validation  
3. **HeroUI Select Behavior**: The HeroUI `Select` component:  
   - Expects string keys for both `selectedKeys` prop and `key` prop on `SelectItem` components  
   - Returns selected values as strings, even if they represent numeric IDs  
4. **Debugging Approach**: Adding `console.log` at key points in the data flow (form submission, validation, transformation) is an effective way to identify type mismatch issues.
5. Per usare i comandi npm devi prima: unset LD_LIBRARY_PATH esempio per build: unset LD_LIBRARY_PATH && cd frontend-cliente && npm run build
6. Per usare python devi attivare .venv/bin/activate

## Known Issues and Considerations

### Current Development Status
- Basic CRUD operations are implemented
- Dependency rule management needs frontend alignment with backend API
- Documentation is in progress (see `documentazione/documenazione_api_admin.md`)

### Frontend-Backend Alignment
- The dependency rules frontend needs to be rewritten to match backend API structure
- Field naming conventions differ between current frontend and backend
- Missing required fields in current frontend implementation

## File Upload Handling
- Backend supports file uploads for parameter and value images
- Files are stored in `app/public/images/` directory structure
- Frontend should use FormData for file uploads

## Authentication Flow
- JWT access tokens expire in 15 minutes (configurable)
- Refresh tokens expire in 30 days (configurable)
- Frontend maintains authentication state in AuthContext
- Protected routes redirect to login when unauthenticated

## Testing and Quality Assurance
- Use proper error handling in all API endpoints
- Validate all user inputs
- Test database migrations thoroughly
- Implement proper loading and error states in frontend
- Use TypeScript strict mode for better type safety
