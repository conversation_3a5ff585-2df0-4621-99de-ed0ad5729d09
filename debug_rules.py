#!/usr/bin/env python3
"""
Script per debuggare le regole di dipendenza nel database
"""

import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

from app import app
from app.extensions import db
from app.models import RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, Parametri, ValoriParametro
from sqlalchemy.orm import joinedload

def debug_rules():
    with app.app_context():
        print("=== DEBUG REGOLE DI DIPENDENZA ===")
        
        # Recupera tutti i parametri per riferimento
        parametri = {p.id_parametro: p for p in Parametri.query.all()}
        valori = {v.id_valore_parametro: v for v in ValoriParametro.query.all()}
        
        print(f"\nParametri disponibili:")
        for p in parametri.values():
            print(f"  {p.id_parametro}: {p.nome_parametro} (root: {p.is_root})")
            for v in p.valori:
                print(f"    - {v.id_valore_parametro}: {v.testo_visualizzato_ui}")
        
        # Recupera tutte le regole attive
        rules = RegolaDipendenzaComplessa.query.options(
            joinedload(RegolaDipendenzaComplessa.condizioni),
            joinedload(RegolaDipendenzaComplessa.risultati)
        ).filter(RegolaDipendenzaComplessa.attiva == True).all()
        
        print(f"\n=== REGOLE ATTIVE ({len(rules)}) ===")
        
        for rule in rules:
            print(f"\nRegola {rule.id}: {rule.nome_regola}")
            print(f"  Descrizione: {rule.descrizione}")
            print(f"  Logica: {rule.logica_combinazione_condizioni}")
            
            print(f"  Condizioni:")
            for condition in rule.condizioni:
                param_name = parametri.get(condition.id_parametro_condizionante, {}).nome_parametro if condition.id_parametro_condizionante in parametri else "Unknown"
                
                if condition.id_valore_condizione_predefinita:
                    valore_name = valori.get(condition.id_valore_condizione_predefinita, {}).testo_visualizzato_ui if condition.id_valore_condizione_predefinita in valori else "Unknown"
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} '{valore_name}' (ID: {condition.id_valore_condizione_predefinita})")
                elif condition.valore_condizione_libero:
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} '{condition.valore_condizione_libero}'")
                else:
                    print(f"    - {param_name} (ID: {condition.id_parametro_condizionante}) {condition.tipo_condizione} (presenza)")
            
            print(f"  Risultati:")
            for result in rule.risultati:
                param_name = parametri.get(result.id_parametro_effetto, {}).nome_parametro if result.id_parametro_effetto in parametri else "Unknown"
                
                if result.id_valore_effetto_predefinito:
                    valore_name = valori.get(result.id_valore_effetto_predefinito, {}).testo_visualizzato_ui if result.id_valore_effetto_predefinito in valori else "Unknown"
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto}) -> '{valore_name}' (ID: {result.id_valore_effetto_predefinito})")
                elif result.valore_effetto_libero:
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto}) -> '{result.valore_effetto_libero}'")
                else:
                    print(f"    - {result.tipo_effetto} {param_name} (ID: {result.id_parametro_effetto})")
        
        print(f"\n=== FINE DEBUG ===")

if __name__ == "__main__":
    debug_rules()