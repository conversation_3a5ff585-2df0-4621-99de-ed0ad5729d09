#!/usr/bin/env python3
"""
Script per aggiungere la regola mancante per mostrare Connessione quando si seleziona Incollato
"""

import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

from app import app
from app.extensions import db
from app.models import RegolaDipendenzaComplessa, CondizioniRegola, RisultatiRegola, Parametri, ValoriParametro

def add_incollato_connection_rule():
    with app.app_context():
        print("=== AGGIUNTA REGOLA PER INCOLLATO ===")
        
        # Verifica se la regola esiste già
        existing_rule = RegolaDipendenzaComplessa.query.filter_by(
            nome_regola="Mostra Connessione per Tipologia e Tecnica - Incollato"
        ).first()
        
        if existing_rule:
            print("La regola esiste già!")
            return
        
        # Trova i parametri e valori necessari
        param_tipologia_impianto = Parametri.query.filter_by(nome_parametro="Tipologia Impianto").first()
        param_tecnica = Parametri.query.filter_by(nome_parametro="Tecnica").first()
        param_connessione = Parametri.query.filter_by(nome_parametro="Connessione").first()
        
        valore_incollato = ValoriParametro.query.filter_by(testo_visualizzato_ui="Incollato").first()
        valore_interna = ValoriParametro.query.filter_by(testo_visualizzato_ui="Interna").first()
        valore_mua = ValoriParametro.query.filter_by(testo_visualizzato_ui="M.U.A. originale").first()
        
        if not all([param_tipologia_impianto, param_tecnica, param_connessione, valore_incollato, valore_interna, valore_mua]):
            print("Errore: Non tutti i parametri/valori necessari sono stati trovati")
            print(f"param_tipologia_impianto: {param_tipologia_impianto}")
            print(f"param_tecnica: {param_tecnica}")
            print(f"param_connessione: {param_connessione}")
            print(f"valore_incollato: {valore_incollato}")
            print(f"valore_interna: {valore_interna}")
            print(f"valore_mua: {valore_mua}")
            return
        
        try:
            # Crea la nuova regola
            new_rule = RegolaDipendenzaComplessa(
                nome_regola="Mostra Connessione per Tipologia e Tecnica - Incollato",
                descrizione="Mostra il parametro Connessione quando si seleziona Incollato",
                attiva=True,
                logica_combinazione_condizioni="AND"
            )
            db.session.add(new_rule)
            db.session.flush()  # Per ottenere l'ID della regola
            
            # Aggiungi le condizioni
            # Condizione 1: Tipologia Impianto HAS_VALUE
            condition1 = CondizioniRegola(
                id_regola=new_rule.id,
                id_parametro_condizionante=param_tipologia_impianto.id_parametro,
                tipo_condizione="HAS_VALUE",
                ordine_valutazione=1
            )
            db.session.add(condition1)
            
            # Condizione 2: Tecnica = Incollato
            condition2 = CondizioniRegola(
                id_regola=new_rule.id,
                id_parametro_condizionante=param_tecnica.id_parametro,
                id_valore_condizione_predefinita=valore_incollato.id_valore_parametro,
                tipo_condizione="EQUALS",
                ordine_valutazione=2
            )
            db.session.add(condition2)
            
            # Aggiungi i risultati (mostra i valori di Connessione)
            # Risultato 1: Mostra Interna
            result1 = RisultatiRegola(
                id_regola=new_rule.id,
                id_parametro_effetto=param_connessione.id_parametro,
                tipo_effetto="FILTER_VALUES",
                id_valore_effetto_predefinito=valore_interna.id_valore_parametro
            )
            db.session.add(result1)
            
            # Risultato 2: Mostra M.U.A. originale
            result2 = RisultatiRegola(
                id_regola=new_rule.id,
                id_parametro_effetto=param_connessione.id_parametro,
                tipo_effetto="FILTER_VALUES",
                id_valore_effetto_predefinito=valore_mua.id_valore_parametro
            )
            db.session.add(result2)
            
            # Salva tutto
            db.session.commit()
            
            print(f"✅ Regola creata con successo! ID: {new_rule.id}")
            print(f"Nome: {new_rule.nome_regola}")
            print(f"Condizioni:")
            print(f"  - Tipologia Impianto HAS_VALUE")
            print(f"  - Tecnica = Incollato")
            print(f"Risultati:")
            print(f"  - FILTER_VALUES Connessione -> Interna")
            print(f"  - FILTER_VALUES Connessione -> M.U.A. originale")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione della regola: {e}")

if __name__ == "__main__":
    add_incollato_connection_rule()