<!DOCTYPE html>
<html>
<head>
    <title>Debug Login Test</title>
</head>
<body>
    <h1>Debug Login Test</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing API call to:', 'https://dea3d-api.prismanet.com/api/client/auth/login');
                
                const response = await fetch('https://dea3d-api.prismanet.com/api/client/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'mario.rossi',
                        password: 'password123'
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>Status: ${response.status}</p>
                    <p>User: ${data.user?.denominazione}</p>
                    <p>Token: ${data.access_token ? 'Present' : 'Missing'}</p>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>${error.message}</p>
                    <p>Check console for details</p>
                `;
            }
        }
    </script>
</body>
</html>