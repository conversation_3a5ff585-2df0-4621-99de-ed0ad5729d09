<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Test API Backend</h1>
    
    <div class="section">
        <h2>1. Test Root Parameters</h2>
        <button onclick="testRootParameters()">Get Root Parameters</button>
        <div id="root-result" class="result"></div>
    </div>

    <div class="section">
        <h2>2. Test Parameter Details</h2>
        <button onclick="testParameterDetails(1)">Get Parameter 1 Details</button>
        <div id="details-result" class="result"></div>
    </div>

    <div class="section">
        <h2>3. Test Initial Configuration</h2>
        <button onclick="testInitialConfig(1)">Get Initial Config for Parameter 1</button>
        <div id="config-result" class="result"></div>
    </div>

    <div class="section">
        <h2>4. Test Dependency Evaluation</h2>
        <button onclick="testDependencyEvaluation()">Evaluate Dependencies (Ponte)</button>
        <div id="dependency-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://*************:5001/api/v1';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        async function testRootParameters() {
            const resultDiv = document.getElementById('root-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const data = await makeRequest(`${API_BASE}/parameters/root`);
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }

        async function testParameterDetails(id) {
            const resultDiv = document.getElementById('details-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const data = await makeRequest(`${API_BASE}/parameters/${id}`);
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }

        async function testInitialConfig(id) {
            const resultDiv = document.getElementById('config-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const data = await makeRequest(`${API_BASE}/parameters/${id}/initial-config`);
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }

        async function testDependencyEvaluation() {
            const resultDiv = document.getElementById('dependency-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const data = await makeRequest(`${API_BASE}/dependency_evaluation/evaluate`, {
                    method: 'POST',
                    body: JSON.stringify({
                        current_parameters: [
                            { parameter_id: 1, value_id: 1 } // Lavorazioni -> Ponte
                        ]
                    })
                });
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>