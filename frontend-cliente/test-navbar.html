<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Nuovo Navbar DEA3D</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            background: #f8f9fa;
        }
        .navbar { 
            background: white; 
            border-bottom: 1px solid #e5e7eb; 
            padding: 12px 24px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .navbar-section { 
            display: flex; 
            align-items: center; 
            gap: 16px; 
        }
        .logo { 
            font-size: 20px; 
            font-weight: bold; 
            color: #0066cc; 
            text-decoration: none;
        }
        .phone-section { 
            display: flex; 
            align-items: center; 
            gap: 8px; 
            color: #374151;
            text-decoration: none;
        }
        .phone-section:hover { 
            color: #0066cc; 
        }
        .icon-button { 
            background: none; 
            border: none; 
            padding: 8px; 
            border-radius: 6px; 
            cursor: pointer; 
            color: #6b7280;
            transition: all 0.2s;
        }
        .icon-button:hover { 
            background: #f3f4f6; 
            color: #0066cc;
        }
        .icon-button.logout:hover { 
            color: #dc2626;
        }
        .login-button { 
            background: #0066cc; 
            color: white; 
            border: none; 
            padding: 8px 16px; 
            border-radius: 6px; 
            cursor: pointer; 
            font-weight: 500;
        }
        .login-button:hover { 
            background: #0052a3; 
        }
        .content { 
            padding: 40px 24px; 
            text-align: center; 
        }
        .demo-section { 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .demo-section h3 { 
            margin-top: 0; 
            color: #1f2937;
        }
        .responsive-note { 
            background: #fef3c7; 
            border: 1px solid #f59e0b; 
            padding: 12px; 
            border-radius: 6px; 
            margin: 20px 0;
        }
        @media (max-width: 640px) {
            .navbar { padding: 12px 16px; }
            .phone-text-full { display: none; }
            .phone-text-short { display: inline; }
        }
        @media (min-width: 641px) {
            .phone-text-full { display: inline; }
            .phone-text-short { display: none; }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin: 20px 0; color: #1f2937;">Test Nuovo Navbar DEA3D</h1>
    
    <!-- Navbar per Utenti NON Autenticati (Homepage/Login) -->
    <div class="demo-section">
        <h3>🏠 Navbar per Homepage e Login (NON autenticati)</h3>
        <div class="navbar">
            <!-- Sinistra - Logo -->
            <div class="navbar-section">
                <a href="/" class="logo">DEA3D</a>
            </div>
            
            <!-- Centro - Telefono -->
            <div class="navbar-section">
                <a href="tel:+390123456789" class="phone-section">
                    <span>📞</span>
                    <span class="phone-text-full">+39 ************</span>
                    <span class="phone-text-short">************</span>
                </a>
            </div>
            
            <!-- Destra - Bottone Login -->
            <div class="navbar-section">
                <button class="login-button">Accedi</button>
            </div>
        </div>
    </div>

    <!-- Navbar per Utenti Autenticati (Dashboard e altre pagine) -->
    <div class="demo-section">
        <h3>🔐 Navbar per Dashboard e Pagine Autenticate</h3>
        <div class="navbar">
            <!-- Sinistra - Logo -->
            <div class="navbar-section">
                <a href="/dashboard" class="logo">DEA3D</a>
            </div>
            
            <!-- Centro - Telefono -->
            <div class="navbar-section">
                <a href="tel:+390123456789" class="phone-section">
                    <span>📞</span>
                    <span class="phone-text-full">+39 ************</span>
                    <span class="phone-text-short">************</span>
                </a>
            </div>
            
            <!-- Destra - Profilo e Logout -->
            <div class="navbar-section">
                <button class="icon-button" title="Profilo Utente">
                    <span>👤</span>
                </button>
                <button class="icon-button logout" title="Logout">
                    <span>🚪</span>
                </button>
            </div>
        </div>
    </div>

    <div class="responsive-note">
        <strong>📱 Nota Responsive:</strong> Su schermi piccoli, il numero di telefono viene abbreviato per risparmiare spazio.
    </div>

    <!-- Specifiche Implementate -->
    <div class="demo-section">
        <h3>✅ Specifiche Implementate</h3>
        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li><strong>Larghezza Full:</strong> Il navbar occupa tutta la larghezza dello schermo</li>
            <li><strong>Logo a Sinistra:</strong> Immagine logo da <code>public/images/logo.png</code> con fallback testuale</li>
            <li><strong>Centro - Telefono:</strong> Icona telefono + numero cliccabile per chiamare</li>
            <li><strong>Destra - Icone:</strong> Profilo utente e logout per utenti autenticati</li>
            <li><strong>Condizionale:</strong> Diverso comportamento per pagine autenticate/non autenticate</li>
            <li><strong>Responsive:</strong> Adattamento per schermi piccoli</li>
            <li><strong>Tooltips:</strong> Suggerimenti al passaggio del mouse sulle icone</li>
            <li><strong>Navigazione:</strong> Logo porta alla homepage o dashboard a seconda del contesto</li>
        </ul>
    </div>

    <!-- File Creati/Modificati -->
    <div class="demo-section">
        <h3>📁 File Creati/Modificati</h3>
        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li><code>components/icons.tsx</code> - Aggiunte icone PhoneIcon, LogoutIcon, UserIcon</li>
            <li><code>components/navbar.tsx</code> - Nuovo navbar base (sostituito)</li>
            <li><code>components/ConditionalNavbar.tsx</code> - Navbar condizionale per autenticati/non autenticati</li>
            <li><code>app/layout.tsx</code> - Aggiornato per usare ConditionalNavbar</li>
            <li><code>app/profile/page.tsx</code> - Nuova pagina profilo utente</li>
            <li><code>public/images/</code> - Cartella creata per il logo</li>
        </ul>
    </div>

    <!-- Funzionalità -->
    <div class="demo-section">
        <h3>🎯 Funzionalità del Navbar</h3>
        <div style="text-align: left; max-width: 600px; margin: 0 auto;">
            <h4>Per Utenti NON Autenticati (/, /login):</h4>
            <ul>
                <li>Logo DEA3D (porta alla homepage)</li>
                <li>Numero telefono cliccabile</li>
                <li>Bottone "Accedi" (porta al login)</li>
            </ul>
            
            <h4>Per Utenti Autenticati (dashboard, altre pagine):</h4>
            <ul>
                <li>Logo DEA3D (porta alla dashboard)</li>
                <li>Numero telefono cliccabile</li>
                <li>Icona profilo (porta a /profile)</li>
                <li>Icona logout (torna alla homepage)</li>
            </ul>
        </div>
    </div>

    <!-- Prossimi Passi -->
    <div class="demo-section">
        <h3>🚀 Prossimi Passi</h3>
        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li>Aggiungere il file <code>logo.png</code> in <code>public/images/</code></li>
            <li>Personalizzare il numero di telefono nel navbar</li>
            <li>Implementare logica di logout reale</li>
            <li>Completare la pagina profilo utente</li>
            <li>Aggiungere animazioni e transizioni</li>
            <li>Testare su diversi dispositivi</li>
        </ul>
    </div>
</body>
</html>