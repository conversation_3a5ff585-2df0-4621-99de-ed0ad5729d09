import { Parametro, EvaluationRequest, EvaluationResponse } from '../types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://dea3d-api.prismanet.com';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string, options?: RequestInit): Promise<T> {
  const url = `${API_BASE_URL}/api/v1${endpoint}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new ApiError(response.status, `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API Error for ${endpoint}:`, error);
    throw error;
  }
}

export const apiService = {
  /**
   * Recupera tutti i parametri root (lavorazioni) disponibili
   */
  async getRootParameters(): Promise<Parametro[]> {
    return fetchApi<Parametro[]>('/parameters/root');
  },

  /**
   * Recupera i dettagli completi di un parametro con i suoi valori
   */
  async getParameterDetails(parameterId: number): Promise<Parametro> {
    return fetchApi<Parametro>(`/parameters/${parameterId}`);
  },

  /**
   * Recupera la configurazione iniziale per un parametro root
   */
  async getInitialConfiguration(rootParameterId: number): Promise<Parametro[]> {
    return fetchApi<Parametro[]>(`/parameters/${rootParameterId}/initial-config`);
  },

  /**
   * Valuta le dipendenze e ottiene i parametri dipendenti
   */
  async evaluateDependencies(request: EvaluationRequest): Promise<EvaluationResponse> {
    return fetchApi<EvaluationResponse>('/dependency_evaluation/evaluate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
};

export default apiService;