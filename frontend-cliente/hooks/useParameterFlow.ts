import { useState, useCallback, useEffect } from 'react';
import { Parametro, ParameterValue, SelectedParameters, ParameterSelection } from '../types/api';
import apiService from '../services/api';

interface UseParameterFlowReturn {
  // Stato
  rootParameters: Parametro[];
  selectedRootParameter: Parametro | null;
  currentParameters: Parametro[];
  selectedValues: SelectedParameters;
  loading: boolean;
  error: string | null;

  // Azioni
  selectRootParameter: (parameter: Parametro) => Promise<void>;
  updateParameterValue: (parameterId: number, value: ParameterValue) => Promise<void>;
  resetFlow: () => void;
}

export function useParameterFlow(): UseParameterFlowReturn {
  const [rootParameters, setRootParameters] = useState<Parametro[]>([]);
  const [selectedRootParameter, setSelectedRootParameter] = useState<Parametro | null>(null);
  const [currentParameters, setCurrentParameters] = useState<Parametro[]>([]);
  const [selectedValues, setSelectedValues] = useState<SelectedParameters>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carica i parametri root all'inizializzazione
  useEffect(() => {
    loadRootParameters();
  }, []);

  const loadRootParameters = async () => {
    try {
      setLoading(true);
      setError(null);
      const parameters = await apiService.getRootParameters();
      setRootParameters(parameters);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore nel caricamento dei parametri root');
    } finally {
      setLoading(false);
    }
  };

  const selectRootParameter = useCallback(async (parameter: Parametro) => {
    try {
      setLoading(true);
      setError(null);
      
      // Imposta il parametro root selezionato
      setSelectedRootParameter(parameter);
      
      // Carica la configurazione iniziale
      const initialConfig = await apiService.getInitialConfiguration(parameter.id_parametro);
      setCurrentParameters(initialConfig);
      
      // Reset dei valori selezionati
      setSelectedValues({});
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore nel caricamento della configurazione iniziale');
    } finally {
      setLoading(false);
    }
  }, []);

  const updateParameterValue = useCallback(async (parameterId: number, value: ParameterValue) => {
    try {
      setLoading(true);
      setError(null);

      // Aggiorna i valori selezionati
      const newSelectedValues = {
        ...selectedValues,
        [parameterId]: value,
      };
      setSelectedValues(newSelectedValues);

      // Trova il parametro che è stato modificato
      const changedParameter = currentParameters.find(p => p.id_parametro === parameterId);
      
      // Se il parametro ha dipendenze, valuta le nuove dipendenze
      if (changedParameter?.has_dependent_children) {
        // Prepara la richiesta per la valutazione delle dipendenze
        const currentSelections: ParameterSelection[] = Object.entries(newSelectedValues)
          .filter(([_, val]) => val !== undefined && val !== null && val !== '')
          .map(([paramId, val]) => ({
            parameter_id: parseInt(paramId),
            value_id: typeof val === 'number' ? val : 
                     typeof val === 'string' ? parseInt(val) || 0 : 
                     Array.isArray(val) && val.length > 0 ? val[0] : 0
          }))
          .filter(selection => selection.value_id > 0); // Filtra valori validi

        if (currentSelections.length > 0) {
          const evaluationResponse = await apiService.evaluateDependencies({
            current_parameters: currentSelections
          });

          // Aggiorna i parametri correnti con quelli dipendenti
          setCurrentParameters(prev => {
            const existingParamIds = new Set(prev.map(p => p.id_parametro));
            const newParams = evaluationResponse.dependent_parameters.filter(
              p => !existingParamIds.has(p.id_parametro)
            );
            return [...prev, ...newParams];
          });
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore nell\'aggiornamento del parametro');
    } finally {
      setLoading(false);
    }
  }, [selectedValues, currentParameters]);

  const resetFlow = useCallback(() => {
    setSelectedRootParameter(null);
    setCurrentParameters([]);
    setSelectedValues({});
    setError(null);
  }, []);

  return {
    rootParameters,
    selectedRootParameter,
    currentParameters,
    selectedValues,
    loading,
    error,
    selectRootParameter,
    updateParameterValue,
    resetFlow,
  };
}