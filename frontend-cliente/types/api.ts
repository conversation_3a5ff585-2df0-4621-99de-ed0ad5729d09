// Tipi basati sulla struttura del backend

export interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
  ordine_visualizzazione: number;
  foto?: string;
  colore?: string;
  descrizione?: string;
}

export interface Parametro {
  id_parametro: number;
  nome_parametro: string;
  tipo_controllo_ui: string;
  descrizione?: string;
  foto?: string;
  is_root: boolean;
  attivo: boolean;
  valori: ValoreParametro[];
  has_dependent_children: boolean;
}

// Tipi per le richieste API
export interface ParameterSelection {
  parameter_id: number;
  value_id: number;
}

export interface EvaluationRequest {
  current_parameters: ParameterSelection[];
}

export interface EvaluationResponse {
  dependent_parameters: Parametro[];
}

// Tipi per il controllo UI
export type TipoControlloUI = 
  | 'TipoControlloUI.SELECT'
  | 'TipoControlloUI.RADIO'
  | 'TipoControlloUI.CHECKBOX_GROUP'
  | 'TipoControlloUI.INPUT_TEXT'
  | 'TipoControlloUI.INPUT_NUMBER'
  | 'TipoControlloUI.TEXTAREA';

// Tipi per i valori selezionati
export type ParameterValue = number | string | number[];

export interface SelectedParameters {
  [parameterId: number]: ParameterValue;
}