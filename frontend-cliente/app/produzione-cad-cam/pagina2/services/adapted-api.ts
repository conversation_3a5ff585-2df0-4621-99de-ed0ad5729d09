import apiService from '../../../../services/api';
import { BackendAdapter } from '../adapters/backend-adapter';
import { Lavorazione, ParametroConfigurazione, ConfigurazioneDinamicaResponse } from '../types';

/**
 * Servizio API adattato che mantiene l'interfaccia esistente ma usa il backend nuovo
 */

export async function getLavorazioni(): Promise<Lavorazione[]> {
  try {
    const rootParameters = await apiService.getRootParameters();
    return BackendAdapter.parametriToLavorazioni(rootParameters);
  } catch (error) {
    console.error('Error fetching lavorazioni:', error);
    throw error;
  }
}

export async function getConfigurazioneIniziale(
  idLavorazione: number
): Promise<ParametroConfigurazione[]> {
  try {
    // idLavorazione ora corrisponde a id_valore_parametro
    // Dobbiamo trovare il parametro root e valutare le dipendenze per questo valore
    
    // Prima otteniamo tutti i parametri root per trovare quello che contiene questo valore
    const rootParameters = await apiService.getRootParameters();
    let rootParameterId: number | null = null;
    
    for (const rootParam of rootParameters) {
      const hasValue = rootParam.valori.some(v => v.id_valore_parametro === idLavorazione);
      if (hasValue) {
        rootParameterId = rootParam.id_parametro;
        break;
      }
    }
    
    if (!rootParameterId) {
      console.warn(`Could not find root parameter for value ID ${idLavorazione}`);
      return [];
    }
    
    // Ora valutiamo le dipendenze per questa selezione
    try {
      // console.log('=== DEBUG getConfigurazioneIniziale ===');
      // console.log('idLavorazione:', idLavorazione);
      // console.log('rootParameterId:', rootParameterId);
      
      const evaluationResponse = await apiService.evaluateDependencies({
        current_parameters: [
          {
            parameter_id: rootParameterId,
            value_id: idLavorazione
          }
        ]
      });
      
      // console.log('Initial evaluationResponse:', evaluationResponse);
      // console.log('Initial dependent_parameters:', evaluationResponse.dependent_parameters);
      // console.log('=== END DEBUG INITIAL ===');
      
      return BackendAdapter.parametriToParametriConfigurazione(evaluationResponse.dependent_parameters);
    } catch (evalError) {
      console.warn('Could not evaluate dependencies for initial config:', evalError);
      return [];
    }
  } catch (error) {
    console.error('Error fetching configurazione iniziale:', error);
    throw error;
  }
}

export async function postConfigurazioneDinamica(
  idLavorazione: number,
  selezioniCorrenti: Array<{
    id_parametro: number;
    id_valore_selezionato: number | string;
  }>,
  idParametriAttualmenteVisualizzati: number[],
  idParametroTrigger: number,
  idValoreSelezionatoPrecedente: number | null
): Promise<ConfigurazioneDinamicaResponse> {
  try {
    // idLavorazione corrisponde a id_valore_parametro del parametro root
    // Dobbiamo trovare il parametro root e includerlo nelle selezioni
    
    const rootParameters = await apiService.getRootParameters();
    let rootParameterId: number | null = null;
    
    for (const rootParam of rootParameters) {
      const hasValue = rootParam.valori.some(v => v.id_valore_parametro === idLavorazione);
      if (hasValue) {
        rootParameterId = rootParam.id_parametro;
        break;
      }
    }
    
    if (!rootParameterId) {
      console.warn(`Could not find root parameter for value ID ${idLavorazione}`);
      return {
        nuovi_parametri_dipendenti: [],
        aggiornamenti_valori_parametri_visibili: [],
        id_parametri_da_rimuovere: []
      };
    }
    
    // Converte le selezioni nel formato del backend
    const backendSelections = BackendAdapter.frontendSelectionsToBackendSelections(selezioniCorrenti);
    
    // Aggiungi sempre la selezione del parametro root
    const allSelections = [
      {
        parameter_id: rootParameterId,
        value_id: idLavorazione
      },
      ...backendSelections
    ];
    
    // Rimuovi duplicati se presenti
    const uniqueSelections = allSelections.filter((selection, index, self) => 
      index === self.findIndex(s => s.parameter_id === selection.parameter_id)
    );

    // Debug logging
    // console.log('=== DEBUG postConfigurazioneDinamica ===');
    // console.log('idLavorazione:', idLavorazione);
    // console.log('selezioniCorrenti:', selezioniCorrenti);
    // console.log('idParametroTrigger:', idParametroTrigger);
    // console.log('rootParameterId:', rootParameterId);
    // console.log('uniqueSelections:', uniqueSelections);

    // Chiama l'API di valutazione del backend
    const evaluationResponse = await apiService.evaluateDependencies({
      current_parameters: uniqueSelections
    });

    // console.log('evaluationResponse:', evaluationResponse);
    // console.log('dependent_parameters:', evaluationResponse.dependent_parameters);
    // console.log('=== END DEBUG ===');

    // Converte la risposta nel formato atteso dal frontend
    return BackendAdapter.evaluationResponseToConfigurazioneDinamica(evaluationResponse, idParametriAttualmenteVisualizzati);
  } catch (error) {
    console.error('Error posting configurazione dinamica:', error);
    throw error;
  }
}