// Importa i tipi necessari dalla directory padre
import { Lavorazione, ParametroConfigurazione } from '../../types';

// Definizione della nuova interfaccia per un singolo elemento in teethWorks
interface SelectedWorkEntry {
    lavorazione: Lavorazione;
    parameters: Record<number, number | string | (number | boolean)[]>;
    dynamicParameterDefinitions: ParametroConfigurazione[] | null;
}

export interface TeethMapProps {
    onTeethSelect: (toothId: string) => void;
    onBridgeSelect: (bridgeId: string) => void;
    selectedTeeth: string[];
    selectedBridges: string[];
    // Aggiorna il tipo di teethWorks per usare SelectedWorkEntry
    teethWorks: Record<string, SelectedWorkEntry>;
}