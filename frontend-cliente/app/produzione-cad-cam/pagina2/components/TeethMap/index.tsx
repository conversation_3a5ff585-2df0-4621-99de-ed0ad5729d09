'use client'

import { teethData } from '../../data';
import { TeethMapProps } from './types';

export const TeethMap: React.FC<TeethMapProps> = ({ onTeethSelect, onBridgeSelect, selectedTeeth, selectedBridges, teethWorks }) => {
    const convertCoordsToPoints = (coords: string): string => {
        return coords.split(',').reduce((acc: string[], curr: string, i: number, arr: string[]) => {
            if (i % 2 === 0) {
                acc.push(`${curr},${arr[i + 1]}`)
            }
            return acc
        }, []).join(' ')
    }

    const convertCircleCoords = (coords: string): { cx: number, cy: number, r: number } => {
        const [cx, cy, r] = coords.split(',').map(Number)
        return { cx, cy, r }
    }

    return (
        <div className="relative w-[404px] h-[663px]">
            <svg 
                width="404" 
                height="663" 
                viewBox="0 0 404 663" 
                className="absolute top-0 left-0"
            >
                <image 
                    href="/denti_IT.png" 
                    width="404" 
                    height="663" 
                />
                {teethData.map(tooth => (
                    <g key={tooth.id}>
                        {tooth.polygonCoords && (
                            <polygon
                                points={convertCoordsToPoints(tooth.polygonCoords)}
                                className={`fill-transparent hover:fill-blue-500/30 dark:hover:fill-blue-400/40 cursor-pointer transition-colors ${
                                    selectedTeeth.includes(tooth.id) ? 'stroke-blue-500 stroke-2' : 'stroke-transparent'
                                }`}
                                onClick={() => onTeethSelect(tooth.id)}
                                data-params={tooth.dataParams}
                            />
                        )}
                        {tooth.circleCoords && tooth.bridgeDataId && (
                            <circle
                                {...convertCircleCoords(tooth.circleCoords)}
                                className={`fill-transparent hover:fill-green-500/30 dark:hover:fill-green-400/40 cursor-pointer transition-colors ${
                                    selectedBridges.includes(tooth.bridgeDataId) ? 'stroke-green-500 stroke-2' : 'stroke-transparent'
                                }`}
                                onClick={() => onBridgeSelect(tooth.bridgeDataId!)}
                            />
                        )}
                    </g>
                ))}
            </svg>
        </div>
    )
}