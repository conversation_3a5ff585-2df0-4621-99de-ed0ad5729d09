'use client'

import { Lavorazione, ParametroConfigurazione } from '../../types';
import { 
    Card, 
    CardHeader, 
    CardBody, 
    Divider, 
    Chip, 
    Accordion, 
    AccordionItem,
    Badge
} from '@heroui/react';

interface SelectedWorkEntry {
    lavorazione: Lavorazione;
    parameters: Record<number, number | string | (number | boolean)[]>;
    // Aggiungi un campo per le definizioni complete dei parametri dinamici
    dynamicParameterDefinitions: ParametroConfigurazione[] | null;
}

interface SelectedWorksProps {
    selectedTeeth: string[];
    teethWorks: Record<string, SelectedWorkEntry>;
}

export const SelectedWorks: React.FC<SelectedWorksProps> = ({ selectedTeeth, teethWorks }) => {
    return (
        <Card className="w-full max-w-md">
            <CardHeader className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">Lavorazioni Selezionate</h2>
                <Chip color="primary">
                    {selectedTeeth.length}
                </Chip>
            </CardHeader>
            <Divider />
            <CardBody>
                {selectedTeeth.length === 0 ? (
                    <div className="flex justify-center items-center py-6">
                        <p className="text-default-500 italic">Nessuna lavorazione selezionata</p>
                    </div>
                ) : (
                    <Accordion variant="shadow" selectionMode="multiple" defaultExpandedKeys={selectedTeeth.length > 0 ? [selectedTeeth[0]] : []}>
                        {selectedTeeth.map((toothId, index) => {
                            const workEntry = teethWorks[toothId];
                            const displayText = workEntry?.lavorazione?.nome_lavorazione || 'Non specificata';
                            const paramsToDisplay: { label: string; value: string }[] = [];

                            if (workEntry?.parameters && workEntry.dynamicParameterDefinitions) {
                                Object.entries(workEntry.parameters).forEach(([paramId, value]) => {
                                    const paramDefinition = workEntry.dynamicParameterDefinitions?.find(p => p.id_parametro === Number(paramId)) || null;
                                    if (paramDefinition) {
                                        let paramValueText = '';
                                        if (Array.isArray(value)) { // Checkbox group
                                            const selectedValues = value.map(v => Number(v));
                                            const texts = paramDefinition.valori_disponibili
                                                .filter(vd => selectedValues.includes(vd.id_valore_parametro))
                                                .map(vd => vd.testo_visualizzato_ui);
                                            paramValueText = texts.join(', ');
                                        } else if (paramDefinition.tipo_controllo_ui.endsWith('.RADIO') || paramDefinition.tipo_controllo_ui.endsWith('.SELECT')) {
                                            const selectedValue = Number(value);
                                            const foundValue = paramDefinition.valori_disponibili.find(vd => vd.id_valore_parametro === selectedValue);
                                            paramValueText = foundValue?.testo_visualizzato_ui || value.toString();
                                        } else { // INPUT_TEXT, INPUT_NUMBER, TEXTAREA
                                            paramValueText = value.toString();
                                        }
                                        paramsToDisplay.push({ 
                                            label: paramDefinition.nome_parametro, 
                                            value: paramValueText 
                                        });
                                    }
                                });
                            }
                            
                            return (
                                <AccordionItem 
                                    key={toothId} 
                                    title={
                                        <div className="flex justify-between items-center w-full">
                                            <span className="font-medium">Dente {toothId}</span>
                                            <Chip color="primary" variant="flat">{displayText}</Chip>
                                        </div>
                                    }
                                    aria-label={`Dente ${toothId}`}
                                >
                                    {paramsToDisplay.length > 0 ? (
                                        <div className="space-y-2 px-2">
                                            {paramsToDisplay.map((param, idx) => (
                                                <div key={`${toothId}-param-${idx}`} className="flex justify-between items-center">
                                                    <span className="font-medium text-sm">{param.label}:</span>
                                                    <span className="text-sm">{param.value}</span>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-sm text-default-500 italic">Nessun parametro configurato</p>
                                    )}
                                </AccordionItem>
                            );
                        })}
                    </Accordion>
                )}
            </CardBody>
        </Card>
    )
}
