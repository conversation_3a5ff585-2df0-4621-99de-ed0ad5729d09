import React from 'react';
import {
    Input, RadioGroup, Radio, Select, SelectItem, CheckboxGroup, Checkbox, Textarea
} from '@heroui/react';
import { ParametroConfigurazione } from '../../../types';

interface DynamicParameterFormProps {
    dynamicParameters: ParametroConfigurazione[];
    selectedDynamicValues: Record<number, number | string | (number | boolean)[]>;
    onDynamicValueChange: (paramId: number, value: number | string | (number | boolean)[]) => void;
    loadingConfig: boolean;
    configError: string | null;
}

export const DynamicParameterForm: React.FC<DynamicParameterFormProps> = ({
    dynamicParameters,
    selectedDynamicValues,
    onDynamicValueChange,
    loadingConfig,
    configError
}) => {
    if (loadingConfig) {
        return <p>Caricamento configurazione...</p>;
    }

    if (configError) {
        return <p className="text-red-500">{configError}</p>;
    }

    if (!dynamicParameters || dynamicParameters.length === 0) {
        return null;
    }

    return (
        <div className="flex flex-col gap-4 w-1/2">
            <div className="flex flex-col gap-4">
                {dynamicParameters.map(param => {
                    const uiControlType = param.tipo_controllo_ui.replace('TipoControlloUI.', '');
                    return (
                        <div key={param.id_parametro}>
                            {uiControlType === 'SELECT' && (
                                <Select
                                    label={param.nome_parametro}
                                    placeholder={param.placeholder_ui || "Seleziona un'opzione"}
                                    selectedKeys={selectedDynamicValues[param.id_parametro] ? [String(selectedDynamicValues[param.id_parametro])] : []}
                                    onSelectionChange={(keys) => {
                                        const selectedKey = Array.from(keys)[0];
                                        if (selectedKey && selectedKey !== "") {
                                            onDynamicValueChange(param.id_parametro, Number(selectedKey));
                                        }
                                        // Non chiamare onDynamicValueChange se non c'e' selezione
                                    }}
                                >
                                    {param.valori_disponibili.map(valore => (
                                        <SelectItem key={String(valore.id_valore_parametro)}>
                                            {valore.testo_visualizzato_ui}
                                        </SelectItem>
                                    ))}
                                </Select>
                            )}

                            {uiControlType === 'RADIO' && (
                                <RadioGroup
                                    label={param.nome_parametro}
                                    value={selectedDynamicValues[param.id_parametro] ? String(selectedDynamicValues[param.id_parametro]) : ''}
                                    onValueChange={(value) => onDynamicValueChange(param.id_parametro, Number(value))}
                                >
                                    {param.valori_disponibili.map(valore => (
                                        <Radio key={String(valore.id_valore_parametro)} value={String(valore.id_valore_parametro)}>
                                            {valore.testo_visualizzato_ui}
                                        </Radio>
                                    ))}
                                </RadioGroup>
                            )}

                            {uiControlType === 'CHECKBOX_GROUP' && (
                                <CheckboxGroup
                                    label={param.nome_parametro}
                                    value={selectedDynamicValues[param.id_parametro] ? (selectedDynamicValues[param.id_parametro] as (number | boolean)[]).map(String) : []}
                                    onChange={(selected) => onDynamicValueChange(param.id_parametro, selected.map(Number))}
                                >
                                    {param.valori_disponibili.map(valore => (
                                        <Checkbox key={String(valore.id_valore_parametro)} value={String(valore.id_valore_parametro)}>
                                            {valore.testo_visualizzato_ui}
                                        </Checkbox>
                                    ))}
                                </CheckboxGroup>
                            )}

                            {(uiControlType === 'INPUT_TEXT' || uiControlType === 'TESTO') && (
                                <Input
                                    label={param.nome_parametro}
                                    placeholder={param.placeholder_ui || ''}
                                    value={(selectedDynamicValues[param.id_parametro] as string) || ''}
                                    onChange={(e) => onDynamicValueChange(param.id_parametro, e.target.value)}
                                />
                            )}

                            {uiControlType === 'INPUT_NUMBER' && (
                                <Input
                                    type="number"
                                    label={param.nome_parametro}
                                    placeholder={param.placeholder_ui || ''}
                                    value={typeof selectedDynamicValues[param.id_parametro] === 'number' ? String(selectedDynamicValues[param.id_parametro]) : ''}
                                    onChange={(e) => onDynamicValueChange(param.id_parametro, Number(e.target.value))}
                                />
                            )}

                            {uiControlType === 'TEXTAREA' && (
                                <Textarea
                                    label={param.nome_parametro}
                                    placeholder={param.placeholder_ui || ''}
                                    value={(selectedDynamicValues[param.id_parametro] as string) || ''}
                                    onChange={(e) => onDynamicValueChange(param.id_parametro, e.target.value)}
                                />
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};