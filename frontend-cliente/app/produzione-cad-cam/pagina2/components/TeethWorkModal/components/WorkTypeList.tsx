import React from 'react';
import { Button } from '@heroui/react';
import { Lavorazione } from '../../../types';

interface WorkTypeListProps {
    availableWorkTypes: Lavorazione[];
    selectedWork: string | null;
    onWorkSelect: (workId: string) => void;
}

export const WorkTypeList: React.FC<WorkTypeListProps> = ({
    availableWorkTypes,
    selectedWork,
    onWorkSelect
}) => {
    return (
        <div className="grid grid-cols-1 gap-2 flex-1">
            {availableWorkTypes.map((lavorazione) => (
                <Button
                    key={lavorazione.id_lavorazione}
                    variant={selectedWork === lavorazione.id_lavorazione.toString() ? "solid" : "bordered"}
                    onPress={() => onWorkSelect(lavorazione.id_lavorazione.toString())}
                    className="justify-start"
                    color={selectedWork === lavorazione.id_lavorazione.toString() ? "primary" : "default"}
                >
                    {lavorazione.nome_lavorazione}
                </Button>
            ))}
        </div>
    );
};