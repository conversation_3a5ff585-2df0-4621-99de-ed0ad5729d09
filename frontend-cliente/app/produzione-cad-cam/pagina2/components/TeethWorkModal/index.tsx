'use client'

import React, { useState, useEffect, useCallback } from 'react';
import {
    <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
} from '@heroui/react';
import { Lavorazione, ParametroConfigurazione, ConfigurazioneDinamicaResponse } from '../../types';
import { getConfigurazioneIniziale, postConfigurazioneDinamica } from '../../api';
import { WorkTypeList } from './components/WorkTypeList';
import { DynamicParameterForm } from './components/DynamicParameterForm';

// Funzione debounce per ritardare l'esecuzione di una funzione
const debounce = <F extends (...args: any[]) => any>(func: F, delay: number) => {
    let timeoutId: NodeJS.Timeout | null;
    return (...args: Parameters<F>): void => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
            func(...args);
            timeoutId = null;
        }, delay);
    };
};

export interface TeethWorkModalProps {
    isOpen: boolean;
    onClose: () => void;
    // Aggiornata la firma di onSave per includere dependencyTree
    onSave: (
        lavorazione: Lavorazione | null, 
        parameters?: Record<number, number | string | (number | boolean)[]>, 
        allDynamicParameters?: ParametroConfigurazione[],
        dependencyTree?: {
            rootParameterId: number;
            parentToChildMap: Record<number, number[]>;
            parameterTriggerMap: Record<number, {
                parentId: number;
                parentValueId: number;
            }>;
        } | null
    ) => void;
    toothId: string;
    currentWorkType?: string;
    currentParameters?: Record<number, number | string | (number | boolean)[]>;
    // Aggiunti i parametri salvati e la struttura delle dipendenze
    currentDynamicParameters?: ParametroConfigurazione[];
    currentDependencyTree?: {
        rootParameterId: number;
        parentToChildMap: Record<number, number[]>;
        parameterTriggerMap: Record<number, {
            parentId: number;
            parentValueId: number;
        }>;
    } | null;
    availableWorkTypes: Lavorazione[];
}

export const TeethWorkModal: React.FC<TeethWorkModalProps> = ({
    isOpen,
    onClose,
    onSave,
    toothId,
    currentWorkType,
    currentParameters,
    currentDynamicParameters,
    currentDependencyTree,
    availableWorkTypes
}) => {
    const [selectedWork, setSelectedWork] = useState<string | null>(null);
    const [dynamicParameters, setDynamicParameters] = useState<ParametroConfigurazione[] | null>(null);
    const [loadingConfig, setLoadingConfig] = useState<boolean>(false);
    const [configError, setConfigError] = useState<string | null>(null);
    const [selectedDynamicValues, setSelectedDynamicValues] = useState<Record<number, number | string | (number | boolean)[]>>({});
    const [dependencyTree, setDependencyTree] = useState<{
        rootParameterId: number;
        parentToChildMap: Record<number, number[]>;
        parameterTriggerMap: Record<number, {
            parentId: number;
            parentValueId: number;
        }>;
    } | null>(null);

    const handleSave = () => {
        const selectedLavorazione = selectedWork ? availableWorkTypes.find(work => work.id_lavorazione === Number(selectedWork)) || null : null;
        onSave(selectedLavorazione, selectedDynamicValues, dynamicParameters || [], dependencyTree);
        setSelectedWork(null);
        setSelectedDynamicValues({});
        setDependencyTree(null);
    };

    const handleWorkSelect = (workId: string) => {
        setSelectedWork(prevWork => {
            const newWork = prevWork === workId ? null : workId;
            return newWork;
        });
    };

    // Funzione per costruire l'albero delle dipendenze
    const buildDependencyTree = (
        parameters: ParametroConfigurazione[],
        rootParameterId: number
    ) => {
        const parentToChildMap: Record<number, number[]> = {};
        const parameterTriggerMap: Record<number, { parentId: number; parentValueId: number }> = {};

        // Per ora, costruiamo una struttura di base
        // In futuro, questo potrebbe essere migliorato per tracciare le dipendenze reali
        parameters.forEach(param => {
            if (param.ha_dipendenze_figlie) {
                parentToChildMap[param.id_parametro] = [];
            }
        });

        return {
            rootParameterId,
            parentToChildMap,
            parameterTriggerMap
        };
    };

    // Funzione debounce per la chiamata API di configurazione dinamica
    const debouncedPostConfigurazioneDinamica = useCallback(
        debounce(async (
            idLavorazione: number,
            selezioniCorrenti: Array<{ id_parametro: number; id_valore_selezionato: number | string; }>,
            idParametriAttualmenteVisualizzati: number[], // Parametro esistente
            idParametroTrigger: number, // Nuovo parametro
            idValoreSelezionatoPrecedente: number | null // Nuovo parametro
        ) => {
            setLoadingConfig(true);
            setConfigError(null);
            try {
                const response: ConfigurazioneDinamicaResponse = await postConfigurazioneDinamica(
                    idLavorazione,
                    selezioniCorrenti,
                    idParametriAttualmenteVisualizzati, // Passa il parametro esistente
                    idParametroTrigger, // Passa il nuovo id_parametro_trigger
                    idValoreSelezionatoPrecedente // Passa il nuovo id_valore_selezionato_precedente
                );
                
                console.log('Backend Response for Dynamic Config:', JSON.stringify(response, null, 2));

                setDynamicParameters(prevParams => {
                    const updatedParamsMap = new Map<number, ParametroConfigurazione>();
                    if (prevParams) {
                        prevParams.forEach(p => updatedParamsMap.set(p.id_parametro, p));
                    }

                    // 1. Elabora i parametri da rimuovere
                    if (response.id_parametri_da_rimuovere && response.id_parametri_da_rimuovere.length > 0) {
                        response.id_parametri_da_rimuovere.forEach(paramId => {
                            updatedParamsMap.delete(paramId);
                        });
                        console.log('Parameters removed:', response.id_parametri_da_rimuovere);
                    }

                    // 2. Elabora gli aggiornamenti per i parametri esistenti
                    if (response.aggiornamenti_valori_parametri_visibili && response.aggiornamenti_valori_parametri_visibili.length > 0) {
                        response.aggiornamenti_valori_parametri_visibili.forEach(updatedParam => {
                            updatedParamsMap.set(updatedParam.id_parametro, updatedParam);
                        });
                        console.log('Parameters updated:', response.aggiornamenti_valori_parametri_visibili.map(p => p.id_parametro));
                    }

                    // 3. Elabora i nuovi parametri dipendenti
                    if (response.nuovi_parametri_dipendenti && response.nuovi_parametri_dipendenti.length > 0) {
                        response.nuovi_parametri_dipendenti.forEach(newParam => {
                            updatedParamsMap.set(newParam.id_parametro, newParam);
                        });
                        console.log('New parameters added:', response.nuovi_parametri_dipendenti.map(p => p.id_parametro));
                    }

                    const filteredAndOrderedParams = Array.from(updatedParamsMap.values())
                        .sort((a, b) => a.ordine_visualizzazione - b.ordine_visualizzazione);
                    
                    console.log('Updating dynamicParameters. After:', filteredAndOrderedParams);
                    if (JSON.stringify(prevParams) === JSON.stringify(filteredAndOrderedParams)) {
                        console.log('dynamicParameters content is identical, skipping update.');
                        return prevParams;
                    }
                    return filteredAndOrderedParams;
                });

                setSelectedDynamicValues(prevSelectedValues => {
                    const newSelectedValues = { ...prevSelectedValues };

                    // 1. Rimuovi le selezioni per i parametri che non sono più rilevanti
                    if (response.id_parametri_da_rimuovere && response.id_parametri_da_rimuovere.length > 0) {
                        response.id_parametri_da_rimuovere.forEach(paramId => {
                            delete newSelectedValues[paramId];
                        });
                        console.log('Selected values removed for:', response.id_parametri_da_rimuovere);
                    }

                    // 2. Aggiorna o valida le selezioni per i parametri esistenti aggiornati
                    if (response.aggiornamenti_valori_parametri_visibili && response.aggiornamenti_valori_parametri_visibili.length > 0) {
                        response.aggiornamenti_valori_parametri_visibili.forEach(param => {
                            const currentVal = newSelectedValues[param.id_parametro];
                            const availableValues = new Set(param.valori_disponibili.map(v => v.id_valore_parametro));

                            if (param.tipo_controllo_ui.endsWith('.CHECKBOX_GROUP')) {
                                const selectedChecks = Array.isArray(currentVal) ? currentVal.map(Number) : [];
                                const validChecks = selectedChecks.filter(id => availableValues.has(id));
                                if (validChecks.length !== selectedChecks.length || (validChecks.length === 0 && param.valori_disponibili.some(v => v.default_selezionato))) {
                                    newSelectedValues[param.id_parametro] = param.valori_disponibili
                                        .filter(v => v.default_selezionato)
                                        .map(v => v.id_valore_parametro);
                                    if (!Array.isArray(newSelectedValues[param.id_parametro]) || (newSelectedValues[param.id_parametro] as (number | boolean)[])?.length === 0) {
                                        newSelectedValues[param.id_parametro] = validChecks;
                                    }
                                } else {
                                    newSelectedValues[param.id_parametro] = validChecks;
                                }
                            } else if (param.tipo_controllo_ui.endsWith('.RADIO') || param.tipo_controllo_ui.endsWith('.SELECT')) {
                                const selectedSingle = typeof currentVal === 'number' ? currentVal : Number(currentVal);
                                if (!currentVal || !availableValues.has(selectedSingle)) {
                                    // Rimuovi il valore corrente se non è più valido
                                    delete newSelectedValues[param.id_parametro];
                                    // Imposta un valore solo se esplicitamente marcato come default
                                    const defaultValue = param.valori_disponibili.find(v => v.default_selezionato);
                                    if (defaultValue) {
                                        newSelectedValues[param.id_parametro] = defaultValue.id_valore_parametro;
                                    }
                                }
                            } else if (param.tipo_controllo_ui.endsWith('.INPUT_TEXT') || param.tipo_controllo_ui.endsWith('.INPUT_NUMBER') || param.tipo_controllo_ui.endsWith('.TEXTAREA')) {
                                // Non c'è bisogno di modificare i valori di testo/numero/textarea se già impostati
                                if (newSelectedValues[param.id_parametro] === undefined || newSelectedValues[param.id_parametro] === null) {
                                    newSelectedValues[param.id_parametro] = ''; // Default stringa vuota
                                }
                            }
                        });
                        console.log('Selected values updated for existing parameters:', response.aggiornamenti_valori_parametri_visibili.map(p => p.id_parametro));
                    }
                    
                    // 3. Inizializza o valida le selezioni per i nuovi parametri
                    if (response.nuovi_parametri_dipendenti && response.nuovi_parametri_dipendenti.length > 0) {
                       response.nuovi_parametri_dipendenti.forEach(param => {
                            if (newSelectedValues[param.id_parametro] === undefined) { // Inizializza solo se non già presente
                                if (param.tipo_controllo_ui.endsWith('.RADIO') || param.tipo_controllo_ui.endsWith('.SELECT')) {
                                    // Imposta un valore solo se esplicitamente marcato come default
                                    const defaultValue = param.valori_disponibili.find(v => v.default_selezionato);
                                    if (defaultValue) {
                                        newSelectedValues[param.id_parametro] = defaultValue.id_valore_parametro;
                                    }
                                    // Non impostare automaticamente il primo valore se non c'è un default
                                } else if (param.tipo_controllo_ui.endsWith('.CHECKBOX_GROUP')) {
                                    const defaultValues = param.valori_disponibili
                                        .filter(v => v.default_selezionato)
                                        .map(v => v.id_valore_parametro);
                                    newSelectedValues[param.id_parametro] = defaultValues.length > 0 ? defaultValues : [];
                                } else if (param.tipo_controllo_ui.endsWith('.INPUT_TEXT') || param.tipo_controllo_ui.endsWith('.INPUT_NUMBER') || param.tipo_controllo_ui.endsWith('.TEXTAREA')) {
                                    newSelectedValues[param.id_parametro] = ''; // Default stringa vuota
                                }
                            }

                        });
                       console.log('Selected values initialized for new parameters:', response.nuovi_parametri_dipendenti.map(p => p.id_parametro));
                    }
                    
                    console.log('Updating selectedDynamicValues. After:', newSelectedValues);
                    if (JSON.stringify(prevSelectedValues) === JSON.stringify(newSelectedValues)) {
                        console.log('selectedDynamicValues content is identical, skipping update.');
                        return prevSelectedValues;
                    }
                    return newSelectedValues;
                });
            } catch (error) {
                console.error("Errore nella configurazione dinamica:", error);
                setConfigError('Errore nell\'aggiornamento della configurazione dinamica.');
            } finally {
                setLoadingConfig(false);
            }
        }, 500), // Debounce delay di 500ms
        []
    );

    // Funzione per triggerare le dipendenze per i parametri salvati
    const triggerDependenciesForSavedParameters = useCallback(async (
        workId: number,
        savedParameters: Record<number, number | string | (number | boolean)[]>,
        initialParameters: ParametroConfigurazione[]
    ) => {
        console.log('=== TRIGGERING DEPENDENCIES FOR SAVED PARAMETERS ===');
        console.log('savedParameters:', savedParameters);
        console.log('initialParameters:', initialParameters);

        // Trova i parametri che hanno dipendenze figlie e hanno valori salvati
        const parametersWithDependencies = initialParameters.filter(param => 
            param.ha_dipendenze_figlie && 
            savedParameters[param.id_parametro] !== undefined
        );

        console.log('parametersWithDependencies:', parametersWithDependencies);

        // Triggera le dipendenze per ogni parametro con dipendenze
        for (const param of parametersWithDependencies) {
            const savedValue = savedParameters[param.id_parametro];
            console.log(`Triggering dependencies for parameter ${param.id_parametro} with value ${savedValue}`);

            // Prepara le selezioni correnti per la chiamata API
            const selezioniCorrenti: Array<{ id_parametro: number; id_valore_selezionato: number | string; }> = [];
            
            Object.entries(savedParameters).forEach(([paramIdStr, value]) => {
                const paramIdNum = Number(paramIdStr);
                const paramDef = initialParameters.find(p => p.id_parametro === paramIdNum);
                
                if (paramDef && value !== undefined && value !== null && value !== '') {
                    if (paramDef.tipo_controllo_ui.endsWith('.RADIO') || 
                        paramDef.tipo_controllo_ui.endsWith('.SELECT') || 
                        paramDef.tipo_controllo_ui.endsWith('.INPUT_NUMBER')) {
                        if (typeof value === 'number') {
                            selezioniCorrenti.push({
                                id_parametro: paramIdNum,
                                id_valore_selezionato: value,
                            });
                        }
                    } else if (paramDef.tipo_controllo_ui.endsWith('.INPUT_TEXT') || 
                               paramDef.tipo_controllo_ui.endsWith('.TEXTAREA')) {
                        if (typeof value === 'string' && value !== '') {
                            selezioniCorrenti.push({
                                id_parametro: paramIdNum,
                                id_valore_selezionato: value,
                            });
                        }
                    }
                }
            });

            if (selezioniCorrenti.length > 0) {
                console.log('Calling debouncedPostConfigurazioneDinamica for saved parameters');
                await new Promise(resolve => setTimeout(resolve, 100)); // Small delay to avoid rapid calls
                
                debouncedPostConfigurazioneDinamica(
                    workId,
                    selezioniCorrenti,
                    initialParameters.map(p => p.id_parametro),
                    param.id_parametro,
                    null // No previous value for restoration
                );
            }
        }
    }, [debouncedPostConfigurazioneDinamica]);

    // Effetto per il caricamento della configurazione iniziale
    useEffect(() => {
        console.log('useEffect for initial config triggered. selectedWork:', selectedWork);
        if (!selectedWork) {
            console.log('No selectedWork, resetting initial config states.');
            setDynamicParameters(null);
            setSelectedDynamicValues({});
            setConfigError(null);
            setDependencyTree(null);
            return;
        }

        const loadInitialConfiguration = async () => {
            setLoadingConfig(true);
            setConfigError(null);
            try {
                const numericWorkId = Number(selectedWork);
                console.log('Calling getConfigurazioneIniziale for workId:', numericWorkId);
                
                // Se abbiamo parametri salvati, usiamo quelli invece di caricare da zero
                if (currentDynamicParameters && currentDynamicParameters.length > 0) {
                    console.log('Using saved dynamic parameter definitions:', currentDynamicParameters);
                    setDynamicParameters(currentDynamicParameters);
                    
                    // Ripristina la struttura delle dipendenze salvata
                    if (currentDependencyTree) {
                        setDependencyTree(currentDependencyTree);
                    } else {
                        // Costruisci l'albero delle dipendenze se non è salvato
                        const tree = buildDependencyTree(currentDynamicParameters, numericWorkId);
                        setDependencyTree(tree);
                    }
                    
                    // Imposta i valori salvati
                    if (currentParameters) {
                        setSelectedDynamicValues(currentParameters);
                        
                        // Triggera le dipendenze per i parametri salvati
                        console.log('Triggering dependencies for saved parameters...');
                        setTimeout(() => {
                            triggerDependenciesForSavedParameters(
                                numericWorkId, 
                                currentParameters, 
                                currentDynamicParameters
                            );
                        }, 500); // Delay per permettere al componente di stabilizzarsi
                    }
                } else {
                    // Carica la configurazione iniziale dal server
                    const data = await getConfigurazioneIniziale(numericWorkId);
                    console.log('Received initial config data:', data);
                    console.log('Detailed initial config data:', JSON.stringify(data, null, 2));
                    
                    // Log dei parametri con dipendenze
                    data.forEach(param => {
                        if (param.ha_dipendenze_figlie) {
                            console.log(`Parameter ${param.id_parametro} (${param.nome_parametro}) has dependencies`);
                        }
                    });
                    
                    setDynamicParameters(data);
                    console.log('dynamicParameters set to:', data);

                    // Costruisci l'albero delle dipendenze
                    const tree = buildDependencyTree(data, numericWorkId);
                    setDependencyTree(tree);

                    const initialValues: Record<number, number | string | (number | boolean)[]> = {};
                    data.forEach(param => {
                        if (currentParameters && currentParameters[param.id_parametro] !== undefined) {
                            initialValues[param.id_parametro] = currentParameters[param.id_parametro];
                        } else if (param.tipo_controllo_ui.endsWith('.RADIO') || param.tipo_controllo_ui.endsWith('.SELECT')) {
                            const defaultValue = param.valori_disponibili.find(v => v.default_selezionato);
                            if (defaultValue) {
                                initialValues[param.id_parametro] = defaultValue.id_valore_parametro;
                            }
                        } else if (param.tipo_controllo_ui.endsWith('.CHECKBOX_GROUP')) {
                            const defaultValues = param.valori_disponibili
                                .filter(v => v.default_selezionato)
                                .map(v => v.id_valore_parametro);
                            if (defaultValues.length > 0) {
                                initialValues[param.id_parametro] = defaultValues;
                            } else {
                                initialValues[param.id_parametro] = [];
                            }
                        } else if (param.tipo_controllo_ui.endsWith('.INPUT_TEXT') || param.tipo_controllo_ui.endsWith('.INPUT_NUMBER') || param.tipo_controllo_ui.endsWith('.TEXTAREA')) {
                            initialValues[param.id_parametro] = '';
                        }
                    });
                    
                    console.log('Setting initial selectedDynamicValues:', initialValues);
                    const finalValues = currentParameters ? { ...initialValues, ...currentParameters } : initialValues;
                    setSelectedDynamicValues(finalValues);

                    // Se ci sono parametri salvati, triggera le dipendenze
                    if (currentParameters && Object.keys(currentParameters).length > 0) {
                        console.log('Triggering dependencies for saved parameters...');
                        setTimeout(() => {
                            triggerDependenciesForSavedParameters(numericWorkId, currentParameters, data);
                        }, 500);
                    }
                }

            } catch (error) {
                console.error("Errore nel caricamento della configurazione iniziale:", error);
                setConfigError('Errore nel caricamento della configurazione iniziale.');
            } finally {
                setLoadingConfig(false);
                console.log('Initial configuration loading finished.');
            }
        };

        loadInitialConfiguration();
    }, [selectedWork, currentParameters, currentDynamicParameters, currentDependencyTree, triggerDependenciesForSavedParameters]);

    // Effetto per innescare la chiamata API di configurazione dinamica con debounce
    // Funzione per gestire il cambiamento dei valori dei parametri dinamici
    const handleDynamicValueChange = (paramId: number, value: number | string | (number | boolean)[]) => {
        const currentParamDefinition = dynamicParameters?.find(p => p.id_parametro === paramId) || null;

        setSelectedDynamicValues(prev => {
            const previousValue = prev[paramId]; // Recupera il valore precedente
            let finalValue: number | string | (number | boolean)[] = value;

            if (currentParamDefinition?.tipo_controllo_ui?.endsWith('.INPUT_NUMBER') && typeof value === 'string') {
                finalValue = Number(value);
            } else if (currentParamDefinition?.tipo_controllo_ui?.endsWith('.CHECKBOX_GROUP') && Array.isArray(value)) {
                finalValue = value;
            } else if (currentParamDefinition?.tipo_controllo_ui?.endsWith('.RADIO') || currentParamDefinition?.tipo_controllo_ui?.endsWith('.SELECT')) {
                finalValue = Number(value);
            } else if (currentParamDefinition?.tipo_controllo_ui?.endsWith('.INPUT_TEXT') || currentParamDefinition?.tipo_controllo_ui?.endsWith('.TEXTAREA')) {
                finalValue = String(value);
            }

            // Se il parametro modificato è un "genitore" con dipendenze, innesca la chiamata dinamica
            if (currentParamDefinition?.ha_dipendenze_figlie && selectedWork) {
                console.log(`=== FRONTEND DEBUG: Parameter ${paramId} (${currentParamDefinition.nome_parametro}) changed to ${finalValue} ===`);
                
                const selezioniCorrenti: Array<{ id_parametro: number; id_valore_selezionato: number | string; }> = [];
                
                // Aggiungi TUTTI i parametri attualmente selezionati (incluso quello appena modificato)
                const updatedValues = { ...prev, [paramId]: finalValue };
                console.log('Updated values after change:', updatedValues);
                
                Object.entries(updatedValues).forEach(([paramIdStr, value]) => {
                    const paramIdNum = Number(paramIdStr);
                    const paramDef = dynamicParameters?.find(p => p.id_parametro === paramIdNum) || null;
                    
                    if (paramDef && value !== undefined && value !== null && value !== '') {
                        if (paramDef.tipo_controllo_ui.endsWith('.RADIO') || paramDef.tipo_controllo_ui.endsWith('.SELECT') || paramDef.tipo_controllo_ui.endsWith('.INPUT_NUMBER')) {
                            if (typeof value === 'number') {
                                selezioniCorrenti.push({
                                    id_parametro: paramIdNum,
                                    id_valore_selezionato: value,
                                });
                            }
                        } else if (paramDef.tipo_controllo_ui.endsWith('.INPUT_TEXT') || paramDef.tipo_controllo_ui.endsWith('.TEXTAREA')) {
                            if (typeof value === 'string' && value !== '') {
                                selezioniCorrenti.push({
                                    id_parametro: paramIdNum,
                                    id_valore_selezionato: value,
                                });
                            }
                        }
                    }
                });
                
                // Non includere i checkbox in selezioniCorrenti per le dipendenze da triggerare,
                // a meno che non ci sia una logica backend specifica per i gruppi di checkbox come trigger.
                // Il report indica che la chiamata dinamica avviene al cambiamento del valore di un parametro "genitore".
                // I checkbox group tendono ad aggiornare più valori contemporaneamente, rendendo la logica di "genitore" meno diretta.
                // Se i checkbox group agiscono da genitore, la logica andrebbe raffinata per la gestione array.
                // Per ora, ci atteniamo ai tipi menzionati nel report per il trigger delle dipendenze.

                console.log('Selezioni correnti per chiamata API:', selezioniCorrenti);
                
                if (selezioniCorrenti.length > 0) {
                    console.log('Innesco debouncedPostConfigurazioneDinamica da handleDynamicValueChange con:', selezioniCorrenti);
                    debouncedPostConfigurazioneDinamica(
                        Number(selectedWork),
                        selezioniCorrenti,
                        Array.from(dynamicParameters?.map(p => p.id_parametro) || []), // Passa gli ID dei parametri attualmente visualizzati
                        paramId, // id_parametro_trigger
                        typeof previousValue === 'number' ? previousValue : (typeof previousValue === 'string' && !isNaN(Number(previousValue)) ? Number(previousValue) : null) // id_valore_selezionato_precedente
                    );
                } else {
                    console.log('Nessuna selezione valida per chiamata API, salto la chiamata');
                }
            }

            return {
                ...prev,
                [paramId]: finalValue
            };
        });
    };

    // Imposta la lavorazione corrente quando il modale si apre
    useEffect(() => {
        if (isOpen && currentWorkType) {
            setSelectedWork(currentWorkType);
        } else if (isOpen) {
            setSelectedWork(null);
            setSelectedDynamicValues({});
            setDependencyTree(null);
        }
    }, [isOpen, currentWorkType]);


    return (
        <Modal isOpen={isOpen} onClose={onClose} size="5xl">
            <ModalContent className="max-w-5xl w-full">
                <ModalHeader>Seleziona Lavorazione per Dente {toothId}</ModalHeader>
                <ModalBody>
                    <div className="flex gap-4">
                        <WorkTypeList
                            availableWorkTypes={availableWorkTypes}
                            selectedWork={selectedWork}
                            onWorkSelect={handleWorkSelect}
                        />

                        {selectedWork && dynamicParameters && (
                            <DynamicParameterForm
                                dynamicParameters={dynamicParameters}
                                selectedDynamicValues={selectedDynamicValues}
                                onDynamicValueChange={handleDynamicValueChange}
                                loadingConfig={loadingConfig}
                                configError={configError}
                            />
                        )}
                    </div>
                </ModalBody>
                <ModalFooter>
                    <Button color="danger" variant="light" onPress={onClose}>
                        Annulla
                    </Button>
                    <Button color="primary" onPress={handleSave} isDisabled={loadingConfig || !selectedWork}>
                        Salva
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};