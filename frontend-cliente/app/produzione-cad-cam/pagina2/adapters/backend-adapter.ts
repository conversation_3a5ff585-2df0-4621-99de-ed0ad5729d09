import { Parametro, ValoreParametro as BackendValoreParametro } from '../../../../types/api';
import { Lavorazione, ParametroConfigurazione, ValoreParametro, ConfigurazioneDinamicaResponse } from '../types';

/**
 * Adapter per convertire i dati del backend nel formato atteso dal frontend esistente
 */

export class BackendAdapter {
  /**
   * Converte un ValoreParametro del parametro root in una Lavorazione
   */
  static valoreParametroToLavorazione(valore: BackendValoreParametro, parametroRoot: Parametro): Lavorazione {
    return {
      id_lavorazione: valore.id_valore_parametro,
      codice_lavorazione: `LAV_${valore.id_valore_parametro}`,
      nome_lavorazione: valore.testo_visualizzato_ui,
      descrizione: valore.descrizione || ''
    };
  }

  /**
   * Converte i valori dei parametri root in Lavorazioni
   * Ogni valore del parametro "Lavorazioni" diventa una lavorazione separata
   */
  static parametriToLavorazioni(parametri: Parametro[]): Lavorazione[] {
    const lavorazioni: Lavorazione[] = [];
    
    parametri
      .filter(p => p.is_root)
      .forEach(parametroRoot => {
        // Ogni valore del parametro root diventa una lavorazione
        parametroRoot.valori.forEach(valore => {
          lavorazioni.push(this.valoreParametroToLavorazione(valore, parametroRoot));
        });
      });
    
    return lavorazioni;
  }

  /**
   * Converte un ValoreParametro del backend nel formato frontend
   */
  static backendValoreToFrontendValore(valore: BackendValoreParametro): ValoreParametro {
    return {
      id_valore_parametro: valore.id_valore_parametro,
      valore_memorizzato: valore.testo_visualizzato_ui, // Usiamo il testo come valore memorizzato
      testo_visualizzato_ui: valore.testo_visualizzato_ui,
      default_selezionato: false // Il backend non ha questo campo, default a false
    };
  }

  /**
   * Converte un Parametro del backend in ParametroConfigurazione
   */
  static parametroToParametroConfigurazione(parametro: Parametro, ordineVisualizzazione: number = 0): ParametroConfigurazione {
    return {
      id_parametro: parametro.id_parametro,
      codice_parametro: `PARAM_${parametro.id_parametro}`,
      nome_parametro: parametro.nome_parametro,
      tipo_controllo_ui: parametro.tipo_controllo_ui,
      obbligatorio: false, // Il backend non ha questo campo, default a false
      ordine_visualizzazione: ordineVisualizzazione,
      valori_disponibili: parametro.valori.map(v => this.backendValoreToFrontendValore(v)),
      placeholder_ui: undefined,
      unita_misura: undefined,
      ha_dipendenze_figlie: parametro.has_dependent_children
    };
  }

  /**
   * Converte un array di Parametri in ParametroConfigurazione
   */
  static parametriToParametriConfigurazione(parametri: Parametro[]): ParametroConfigurazione[] {
    return parametri.map((p, index) => this.parametroToParametroConfigurazione(p, index));
  }

  /**
   * Converte la risposta di valutazione del backend nel formato atteso dal frontend
   */
  static evaluationResponseToConfigurazioneDinamica(
    evaluationResponse: { dependent_parameters: Parametro[] },
    parametriAttualmenteVisualizzati: number[] = []
  ): ConfigurazioneDinamicaResponse {
    const nuoviParametri = this.parametriToParametriConfigurazione(evaluationResponse.dependent_parameters);
    const nuoviParametriIds = new Set(nuoviParametri.map(p => p.id_parametro));
    
    // Calcola i parametri da rimuovere: quelli attualmente visualizzati ma non piu presenti nella nuova risposta
    const parametriDaRimuovere = parametriAttualmenteVisualizzati.filter(id => !nuoviParametriIds.has(id));
    
    console.log('BackendAdapter - Parametri attualmente visualizzati:', parametriAttualmenteVisualizzati);
    console.log('BackendAdapter - Nuovi parametri IDs:', Array.from(nuoviParametriIds));
    console.log('BackendAdapter - Parametri da rimuovere:', parametriDaRimuovere);
    
    return {
      nuovi_parametri_dipendenti: nuoviParametri,
      aggiornamenti_valori_parametri_visibili: [], // Per ora non gestito
      id_parametri_da_rimuovere: parametriDaRimuovere
    };
  }

  /**
   * Converte i parametri di selezione dal formato frontend al backend
   */
  static frontendSelectionsToBackendSelections(
    selezioniCorrenti: Array<{ id_parametro: number; id_valore_selezionato: number | string }>
  ): Array<{ parameter_id: number; value_id: number }> {
    return selezioniCorrenti
      .filter(s => typeof s.id_valore_selezionato === 'number')
      .map(s => ({
        parameter_id: s.id_parametro,
        value_id: s.id_valore_selezionato as number
      }));
  }
}