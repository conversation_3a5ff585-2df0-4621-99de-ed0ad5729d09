// Interfacce basate sulla documentazione API v1
export interface Lavorazione {
  id_lavorazione: number
  codice_lavorazione: string
  nome_lavorazione: string
  descrizione: string
}

export interface ParametroConfigurazione {
  id_parametro: number
  codice_parametro: string
  nome_parametro: string
  tipo_controllo_ui: string
  obbligatorio: boolean
  ordine_visualizzazione: number
  valori_disponibili: ValoreParametro[]
  placeholder_ui?: string;
  unita_misura?: string;
  ha_dipendenze_figlie: boolean; // Nuovo campo
}

export interface ValoreParametro {
  id_valore_parametro: number
  valore_memorizzato: string
  testo_visualizzato_ui: string
  default_selezionato: boolean
}

export interface ConfigurazioneDinamicaResponse {
  nuovi_parametri_dipendenti: ParametroConfigurazione[]
  aggiornamenti_valori_parametri_visibili: ParametroConfigurazione[]
  id_parametri_da_rimuovere?: number[]; // Nuovo campo
}