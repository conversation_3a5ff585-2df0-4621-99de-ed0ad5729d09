// Definizione del tipo per i tecnici
export interface Tecnico {
    key: number;
    label: string;
}

// Dati per i select
export const tecnici = [
    { key: 1, label: "<PERSON>" },
    { key: 2, label: "<PERSON>" },
];

export const opzioniScannerTipo = [
    { key: "1", label: "Scanner Intraorale" },
    { key: "2", label: "Scanner da laboratorio" },
];

export const opzioniScannerIntraorale = [
    { key: "28", label: "CEREC OMNICAM (DENTSPLY SIRONA)" },
    { key: "29", label: "CS 3500 (CARESTREAM DENTAL)" },
    { key: "42", label: "CS 3600 (CARESTREAM DENTAL)" },
    { key: "30", label: "DWIO (DENTAL WINGS)" },
    { key: "43", label: "EMERALD PLANMECA" },
    { key: "31", label: "ITERO (ALIGN TECHNOLOGY)" },
    { key: "44", label: "MEDIT I500" },
    { key: "32", label: "MIA 3D (DENSYS 3D LTD)" },
    { key: "33", label: "TRIOS (3SHAPE)" },
    { key: "34", label: "TRUE DEFINITION SCANNER (3M ESPE)" },
    { key: "35", label: "ALTRO (SPECIFICARE)" },
];

export const opzioniScannerLaboratorio = [
    { key: "1", label: "3M LAVA" },
    { key: "2", label: "3SHAPE" },
    { key: "3", label: "3SHAPE BEGO" },
    { key: "4", label: "3SHAPE DIADEM" },
    { key: "5", label: "3SHAPE HAEREUS" },
    { key: "6", label: "3SHAPE KAVO" },
    { key: "7", label: "3SHAPE NOBILMETAL" },
    { key: "8", label: "3SHAPE WIELAND" },
    { key: "9", label: "AMANN-GIRRBACH" },
    { key: "10", label: "DENTAL-WINGS 3M LAVA" },
    { key: "11", label: "DENTAL-WINGS CMF" },
    { key: "12", label: "DENTAL-WINGS DIADEM" },
    { key: "13", label: "DENTAL-WINGS STRAUMANN" },
    { key: "14", label: "DENTAL-WINGS WIELAND" },
    { key: "15", label: "EGS 8833" },
    { key: "16", label: "EGS 8853" },
    { key: "17", label: "EGS HERAEUS KULZER CARA" },
    { key: "18", label: "IMETRIC" },
    { key: "19", label: "LASERDENTA" },
    { key: "20", label: "MEDIT T-SERIES" },
    { key: "21", label: "NOBEL-BIOCARE" },
    { key: "22", label: "OPEN-TECHNOLOGIES" },
    { key: "23", label: "RENISHAW" },
    { key: "24", label: "ROLAND SWING HD" },
    { key: "25", label: "SCANSYSTEMS" },
    { key: "26", label: "SHINING 3D" },
    { key: "27", label: "SINERGIA-SCAN NOBILMETAL" }
]

export const opzioniSoftwareModellazione = [
    { key: "1", label: "3 SHAPE" },
    { key: "2", label: "CERCON" },
    { key: "3", label: "DELCAM" },
    { key: "4", label: "DENTAL WINGS" },
    { key: "5", label: "E.G.S." },
    { key: "6", label: "ETKON" },
    { key: "7", label: "EXOCAD" },
    { key: "8", label: "NOBEL BIOCARE" },
    { key: "9", label: "ALTRO (SPECIFICARE)" },
    { key: "10", label: "NESSUNO" },
];

export const opzioniCorriere = [
    { key: "4", label: "DHL" },
    { key: "2", label: "TNT" },
    { key: "3", label: "Za. Go." },
];

export const opzioniFatturazione = [
    { key: "Principale", label: "PRINCIPALE" },
    { key: "Altro", label: "ALTRO (SPECIFICARE)" },
];

export const opzioniIndirizzoSpedizione = [
    { key: "Principale", label: "PRINCIPALE (VIA DEI BOCCHI 211/B - 55013 LAMMARI, LU)" },
    { key: "Altro", label: "ALTRO (SPECIFICARE) (Fermo deposito - Inserire il fermo deposito scelto nelle note)" },
];

export const opzioniPresso = [
    { key: "Presso Laboratorio", label: "Presso laboratorio" },
    { key: "Fermo Deposito", label: "Fermo deposito" },
];

export const opzioniProgettazione = [
    { key: "NO", label: "NO" },
    { key: "SI", label: "SI" },
];