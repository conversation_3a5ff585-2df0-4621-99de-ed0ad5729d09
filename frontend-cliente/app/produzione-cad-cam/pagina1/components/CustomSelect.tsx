'use client';

import { Select, SelectItem } from "@heroui/react";

interface Option {
    key: string | number;
    label: string;
}

interface CustomSelectProps {
    label: string;
    options: Option[];
    value?: string | null;
    onChange?: (value: string) => void;
    isRequired?: boolean;
    defaultSelectedKeys?: string[];
    size?: "sm" | "md" | "lg";
}

export const CustomSelect = ({
    label,
    options,
    value,
    onChange,
    isRequired = false,
    defaultSelectedKeys,
    size
}: CustomSelectProps) => {
    return (
        <Select
            label={label}
            onChange={(e) => onChange?.(e.target.value)}
            isRequired={isRequired}
            defaultSelectedKeys={defaultSelectedKeys}
            size={size}
            value={value || undefined}
        >
            {options.map((option) => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
            ))}
        </Select>
    );
};