import { Link } from "@heroui/link";
import { Button } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { title, subtitle } from "@/components/primitives";

export default function Home() {
  return (
    <div className="flex flex-col min-h-[80vh] ">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-8 py-16 flex-grow">
        <div className="text-center max-w-4xl">
       <div className="logodea">

       </div>
          <div className={subtitle({ class: "mt-6 text-lg" })}>
            La piattaforma digitale per la gestione completa del laboratorio odontotecnico.
            Gestisci ordini, produzioni CAD/CAM e componenti protesici in un'unica soluzione integrata.
          </div>
        </div>

        {/* Features Cards */}
        <div className=" grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-6xl w-full">
          <Card className="border-none light ">
            <CardBody className="text-center p-6">
              <div className="text-4xl m-8">🦷</div>
              <h3 className="text-lg font-semibold mb-2">Produzione CAD/CAM</h3>
              <p className="text-sm text-default-600">
                Gestisci l'intero workflow di produzione digitale con precisione e efficienza
              </p>
            </CardBody>
          </Card>
          
          <Card className="border-none light">
            <CardBody className="text-center p-6">
              <div className="text-4xl m-8">⚙️</div>
              <h3 className="text-lg font-semibold mb-2">Componenti Protesici</h3>
              <p className="text-sm text-default-600">
                Ordina e traccia tutti i componenti necessari per le tue protesi
              </p>
            </CardBody>
          </Card>
          
          <Card className="border-none light">
            <CardBody className="text-center p-6">
              <div className="text-4xl m-8">📊</div>
              <h3 className="text-lg font-semibold mb-2">Dashboard Completa</h3>
              <p className="text-sm text-default-600">
                Monitora progetti, ordini e comunicazioni da un'unica interfaccia
              </p>
            </CardBody>
          </Card>
        </div>

        {/* CTA Button */}
        <div className="mt-12">
          <Button
            as={Link}
            href="/login"
            color="primary"
            size="lg"
            className="px-8 py-6 text-lg font-semibold"
            radius="full"
          >
            Accedi alla Piattaforma
          </Button>
        </div>
      </section>
    </div>
  );
}