"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Divider } from "@heroui/divider";
import { Avatar } from "@heroui/avatar";
import { title } from "@/components/primitives";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { UserIcon } from "@/components/icons";

export default function ProfilePage() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    nome: user?.nome_completo?.split(' ')[0] || '',
    cognome: user?.nome_completo?.split(' ').slice(1).join(' ') || '',
    telefono: '',
    cellulare: '',
    indirizzo: '',
    citta: '',
    cap: '',
    provincia: '',
  });

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000'}/api/client/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${typeof window !== 'undefined' ? localStorage.getItem('access_token') : ''}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setIsEditing(false);
      } else {
        throw new Error('Errore durante aggiornamento');
      }
    } catch (error) {
      console.error('Errore aggiornamento profilo:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex flex-col gap-6 max-w-4xl">
        <div className="flex flex-col gap-2">
          <h1 className={title()}>Il mio Profilo</h1>
          <p className="text-default-600">Gestisci le informazioni del tuo profilo e i dati di contatto.</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader className="flex flex-col items-center gap-4 pb-0">
              <Avatar
                size="lg"
                name={user?.nome_completo}
                icon={<UserIcon size={40} />}
                className="w-24 h-24 bg-primary text-white"
              />
              <div className="text-center">
                <h3 className="text-lg font-semibold">{user?.denominazione}</h3>
                <p className="text-sm text-default-500">@{user?.username}</p>
              </div>
            </CardHeader>
            <CardBody className="pt-4">
              <div className="flex flex-col gap-3 text-sm">
                <div>
                  <span className="font-medium text-default-700">Email:</span>
                  <p className="text-default-600">{user?.email}</p>
                </div>
                <div>
                  <span className="font-medium text-default-700">Username:</span>
                  <p className="text-default-600">{user?.username}</p>
                </div>
                <div>
                  <span className="font-medium text-default-700">Nome completo:</span>
                  <p className="text-default-600">{user?.nome_completo}</p>
                </div>
              </div>
              <Divider className="my-4" />
              <div className="text-xs text-default-400">
                <p>Per modificare username ed email,</p>
                <p>contatta amministratore del sistema.</p>
              </div>
            </CardBody>
          </Card>

          <Card className="lg:col-span-2">
            <CardHeader className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Dati Personali</h3>
              <Button
                color={isEditing ? "danger" : "primary"}
                variant="flat"
                size="sm"
                onPress={() => setIsEditing(!isEditing)}
                isDisabled={isLoading}
              >
                {isEditing ? "Annulla" : "Modifica"}
              </Button>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Nome"
                  value={formData.nome}
                  onChange={(e) => setFormData({...formData, nome: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                />
                <Input
                  label="Cognome"
                  value={formData.cognome}
                  onChange={(e) => setFormData({...formData, cognome: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                />
                <Input
                  label="Telefono"
                  value={formData.telefono}
                  onChange={(e) => setFormData({...formData, telefono: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                  placeholder="02-1234567"
                />
                <Input
                  label="Cellulare"
                  value={formData.cellulare}
                  onChange={(e) => setFormData({...formData, cellulare: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                  placeholder="333-1234567"
                />
                <Input
                  label="Indirizzo"
                  value={formData.indirizzo}
                  onChange={(e) => setFormData({...formData, indirizzo: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                  className="md:col-span-2"
                  placeholder="Via Roma 123"
                />
                <Input
                  label="Citta"
                  value={formData.citta}
                  onChange={(e) => setFormData({...formData, citta: e.target.value})}
                  isReadOnly={!isEditing}
                  variant={isEditing ? "bordered" : "flat"}
                  placeholder="Milano"
                />
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    label="CAP"
                    value={formData.cap}
                    onChange={(e) => setFormData({...formData, cap: e.target.value})}
                    isReadOnly={!isEditing}
                    variant={isEditing ? "bordered" : "flat"}
                    placeholder="20100"
                  />
                  <Input
                    label="Provincia"
                    value={formData.provincia}
                    onChange={(e) => setFormData({...formData, provincia: e.target.value})}
                    isReadOnly={!isEditing}
                    variant={isEditing ? "bordered" : "flat"}
                    placeholder="MI"
                  />
                </div>
              </div>
              
              {isEditing && (
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    variant="flat"
                    onPress={() => setIsEditing(false)}
                    isDisabled={isLoading}
                  >
                    Annulla
                  </Button>
                  <Button
                    color="primary"
                    onPress={handleSave}
                    isLoading={isLoading}
                  >
                    Salva Modifiche
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Sicurezza</h3>
          </CardHeader>
          <CardBody>
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">Cambia Password</h4>
                <p className="text-sm text-default-500">
                  Per motivi di sicurezza, il cambio password deve essere richiesto all amministratore.
                </p>
              </div>
              <Button
                variant="flat"
                color="warning"
                size="sm"
                isDisabled
              >
                Richiedi Cambio Password
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </ProtectedRoute>
  );
}