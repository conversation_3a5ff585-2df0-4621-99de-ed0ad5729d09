"use client";

import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { Link } from "@heroui/link";
import SidebarWithState from "@/components/sidebar-with-state";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Pagine che NON devono avere la sidebar
  const pagesWithoutSidebar = ['/', '/login'];
  const shouldShowSidebar = isClient && !pagesWithoutSidebar.includes(pathname);

  // Durante l'hydration, mostra sempre il layout senza sidebar per evitare mismatch
  if (!isClient) {
    return (
      <>
        <main className="flex-grow min-h-[calc(100vh-120px)] px-6 py-8 dea3dBGColor">
          {children}
        </main>
        <footer className="w-full flex items-center justify-center py-3">
          <Link
            isExternal
            className="flex items-center gap-1 text-current"
            href="https://www.prismanet.com"
            title="prismanet.com homepage"
          >
            <span className="text-default-600">Powered by</span>
            <p className="text-primary">prismanet.com</p>
          </Link>
        </footer>
      </>
    );
  }

  if (shouldShowSidebar) {
    // Layout con sidebar per pagine autenticate
    return (
      <>
        <div className="flex pt-4 h-auto bg-default-50 min-h-[calc(100vh-120px)]">
          {/* Sidebar */}
          <div className="flex-shrink-0">
            <SidebarWithState />
          </div>
          <div className="grow p-12 overflow-auto items-start justify-start">
            {children}
          </div>
        </div>
        <footer className="w-full flex items-center justify-center py-3">
          <Link
            isExternal
            className="flex items-center gap-1 text-current"
            href="https://www.prismanet.com"
            title="prismanet.com homepage"
          >
            <span className="text-default-600">Powered by</span>
            <p className="text-primary">prismanet.com</p>
          </Link>
        </footer>
      </>
    );
  }

  // Layout senza sidebar per homepage e login
  return (
    <>
      <main className="flex-grow min-h-[calc(100vh-120px)] px-6 py-8 dea3dBGColor">
        {children}
      </main>
      <footer className="w-full flex items-center justify-center py-3">
        <Link
          isExternal
          className="flex items-center gap-1 text-current"
          href="https://www.prismanet.com"
          title="prismanet.com homepage"
        >
          <span className="text-default-600">Powered by</span>
          <p className="text-primary">prismanet.com</p>
        </Link>
      </footer>
    </>
  );
}