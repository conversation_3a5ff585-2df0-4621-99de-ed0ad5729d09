# Componente Sidebar

Un componente sidebar di navigazione flessibile e responsive basato su HeroUI.

## Caratteristiche

- ✅ **Responsive**: Si adatta automaticamente alle dimensioni dello schermo
- ✅ **Collassabile**: Può essere compressa per risparmiare spazio
- ✅ **Tooltip**: Mostra tooltip quando è compressa
- ✅ **Stato attivo**: Evidenzia automaticamente la pagina corrente
- ✅ **Icone personalizzabili**: Supporta icone React personalizzate
- ✅ **Header e Footer**: Supporta contenuto personalizzato
- ✅ **Elementi disabilitati**: Supporta elementi non cliccabili
- ✅ **Animazioni fluide**: Transizioni smooth per tutte le interazioni

## Utilizzo Base

```tsx
import { Sidebar } from "@/components/sidebar";
import { SidebarItem } from "@/types";
import { HomeIcon, DashboardIcon } from "@/components/icons";

const sidebarItems: SidebarItem[] = [
  {
    label: "Home",
    href: "/",
    icon: <HomeIcon size={20} />,
  },
  {
    label: "Dashboard", 
    href: "/dashboard",
    icon: <DashboardIcon size={20} />,
  },
];

function MyApp() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <Sidebar
      items={sidebarItems}
      isCollapsed={isCollapsed}
      onToggle={() => setIsCollapsed(!isCollapsed)}
    />
  );
}
```

## Props

### `SidebarProps`

| Prop | Tipo | Default | Descrizione |
|------|------|---------|-------------|
| `items` | `SidebarItem[]` | - | **Richiesto.** Array di elementi da visualizzare |
| `className` | `string` | - | Classi CSS aggiuntive |
| `isCollapsed` | `boolean` | - | Stato di compressione controllato |
| `onToggle` | `() => void` | - | Callback per il toggle della sidebar |
| `header` | `React.ReactNode` | - | Contenuto personalizzato per l'header |
| `footer` | `React.ReactNode` | - | Contenuto personalizzato per il footer |

### `SidebarItem`

| Proprietà | Tipo | Descrizione |
|-----------|------|-------------|
| `label` | `string` | **Richiesto.** Testo da visualizzare |
| `href` | `string` | **Richiesto.** URL di destinazione |
| `icon` | `React.ReactNode` | Icona opzionale |
| `isActive` | `boolean` | Stato attivo manuale (override automatico) |
| `isDisabled` | `boolean` | Rende l'elemento non cliccabile |

## Esempi Avanzati

### Con Header e Footer

```tsx
const header = (
  <div className="flex items-center gap-2">
    <Logo size={24} />
    <span className="font-bold">My App</span>
  </div>
);

const footer = (
  <div className="text-xs text-center">
    © 2024 My Company
  </div>
);

<Sidebar
  items={items}
  header={header}
  footer={footer}
/>
```

### Stato Controllato vs Non Controllato

```tsx
// Controllato (raccomandato)
const [isCollapsed, setIsCollapsed] = useState(false);

<Sidebar
  items={items}
  isCollapsed={isCollapsed}
  onToggle={() => setIsCollapsed(!isCollapsed)}
/>

// Non controllato (gestione interna)
<Sidebar items={items} />
```

### Elementi Disabilitati

```tsx
const items: SidebarItem[] = [
  {
    label: "Disponibile",
    href: "/available",
    icon: <HomeIcon size={20} />,
  },
  {
    label: "Non Disponibile",
    href: "/disabled",
    icon: <SettingsIcon size={20} />,
    isDisabled: true,
  },
];
```

### Stato Attivo Personalizzato

```tsx
const items: SidebarItem[] = [
  {
    label: "Home",
    href: "/",
    icon: <HomeIcon size={20} />,
    isActive: true, // Forza lo stato attivo
  },
];
```

## Icone Disponibili

Il progetto include diverse icone predefinite:

- `HomeIcon`
- `DashboardIcon`
- `SettingsIcon`
- `UsersIcon`
- `DocumentIcon`
- `SearchIcon`

Puoi anche utilizzare icone personalizzate:

```tsx
const customIcon = (
  <svg width="20" height="20" viewBox="0 0 24 24">
    {/* Il tuo SVG */}
  </svg>
);

const items: SidebarItem[] = [
  {
    label: "Custom",
    href: "/custom",
    icon: customIcon,
  },
];
```

## Styling e Personalizzazione

### Classi CSS Personalizzate

```tsx
<Sidebar
  items={items}
  className="border-r-2 border-primary"
/>
```

### Temi e Varianti

Il componente utilizza le classi di design system di HeroUI:

- `bg-default-100` per gli hover
- `bg-primary-50` per gli stati attivi
- `text-primary-600` per il testo attivo
- `text-default-600` per il testo normale

## Responsive Design

La sidebar si comporta in modo responsive:

- **Desktop**: Larghezza fissa (256px espansa, 64px compressa)
- **Mobile**: Si adatta automaticamente al contenitore
- **Transizioni**: Animazioni fluide per tutte le modifiche di stato

## Accessibilità

- ✅ Supporto completo per screen reader
- ✅ Navigazione da tastiera
- ✅ Tooltip informativi quando compressa
- ✅ Contrasti colore conformi WCAG
- ✅ Focus indicators visibili

## Demo

Visita `/sidebar-demo` per vedere il componente in azione con tutti gli esempi e le configurazioni possibili.

## Dipendenze

- `@heroui/card`
- `@heroui/button`
- `@heroui/link`
- `@heroui/divider`
- `@heroui/tooltip`
- `next/link`
- `next/navigation`
- `clsx`