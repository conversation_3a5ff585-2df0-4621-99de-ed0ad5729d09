"use client";

import { useState } from "react";

import { Sidebar } from "@/components/sidebar";
import { SidebarItem } from "@/types";
import {
  HomeIcon,
  DashboardIcon,
  SettingsIcon,
  UsersIcon,
  DocumentIcon,
  Logo,
} from "@/components/icons";

export const SidebarExample = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Esempio semplice di configurazione della sidebar
  const navigationItems: SidebarItem[] = [
    {
      label: "Home",
      href: "/",
      icon: <HomeIcon size={20} />,
    },
    {
      label: "Dashboard",
      href: "/dashboard",
      icon: <DashboardIcon size={20} />,
    },
    {
      label: "Utenti",
      href: "/users",
      icon: <UsersIcon size={20} />,
    },
    {
      label: "Documenti",
      href: "/documents",
      icon: <DocumentIcon size={20} />,
    },
    {
      label: "Impostazioni",
      href: "/settings",
      icon: <SettingsIcon size={20} />,
    },
  ];

  const appHeader = (
    <div className="flex items-center gap-2">
      <Logo size={24} />
      <span className="font-bold text-lg">DEA3D</span>
    </div>
  );

  return (
    <Sidebar
      items={navigationItems}
      isCollapsed={isCollapsed}
      onToggle={() => setIsCollapsed(!isCollapsed)}
      header={appHeader}
      className="h-screen"
    />
  );
};

export default SidebarExample;