"use client";

import { useState } from "react";
import { Sidebar } from "@/components/sidebar";
import { SidebarItem } from "@/types";
import {
  HomeIcon,
  DashboardIcon,
  UsersIcon,
  DocumentIcon,
  SettingsIcon,
  Logo,
} from "@/components/icons"; // Se non hai questo file puoi importarli singolarmente o toglierli

const sidebarItems: SidebarItem[] = [
  {
    label: "Ordini",
    href: "/",
  },
  {
    label: "Produzione CAD/CAM",
    href: "/produzione-cad-cam/pagina1",
  },
];

const sidebarHeader = (
  <div className="flex items-center gap-2 p-4">
    <Logo size={24} />
    <span className="font-bold text-lg">DEA3D</span>
  </div>
);

const sidebarFooter = (
  <div className="text-xs text-default-500 text-center p-4">
    © 2025 DEA3D
  </div>
);

export default function SidebarWithState() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <Sidebar
      items={sidebarItems}
      isCollapsed={isCollapsed}
      onToggle={() => setIsCollapsed(!isCollapsed)}
      header={sidebarHeader}
      footer={sidebarFooter}
      className="h-auto"
    />
  );
}
