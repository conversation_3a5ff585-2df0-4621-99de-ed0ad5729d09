"use client";

import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Navbar as HeroUINavbar,
  NavbarContent,
  NavbarBrand,
  NavbarItem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import { Tooltip } from "@heroui/tooltip";
import NextLink from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/dropdown";
import { Avatar } from "@heroui/avatar";

import {
  PhoneIcon,
  LogoutIcon,
  UserIcon,
} from "@/components/icons";

export default function ConditionalNavbar() {
  const pathname = usePathname();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const { isAuthenticated, user, logout } = useAuth();
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Durante l'hydration, mostra sempre la navbar per utenti non autenticati
  if (!isClient) {
    return (
      <HeroUINavbar 
        maxWidth="full" 
        position="sticky"
        className="shadow-sm border-b border-default-200 bg-black dark:bg-black"
      >
        <NavbarContent justify="start">
          <NavbarBrand>
            <NextLink href="/" className="flex items-center">
              <Image
                src="/images/logo_200.png"
                alt="DEA3D Logo"
                width={120}
                height={40}
                className="h-10 w-auto"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="hidden">
                <span className="font-bold text-xl text-primary">DEA3D</span>
              </div>
            </NextLink>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent justify="center">
          <NavbarItem>
            <Link
              href="tel:+390123456789"
              className="flex items-center gap-2 text-default-700 hover:text-primary transition-colors"
            >
              <PhoneIcon size={20} />
              <span className="font-medium hidden sm:inline">+39 ************</span>
              <span className="font-medium sm:hidden">************</span>
            </Link>
          </NavbarItem>
        </NavbarContent>

        <NavbarContent justify="end">
          <NavbarItem>
            <Button
              color="primary"
              variant="flat"
              className="font-medium"
            >
              Accedi
            </Button>
          </NavbarItem>
        </NavbarContent>
      </HeroUINavbar>
    );
  }

  const handleLogout = () => {
    logout();
  };

  const handleProfile = () => {
    router.push("/profile");
  };

  const handleLogin = () => {
    router.push("/login");
  };

  return (
    <HeroUINavbar 
      maxWidth="full" 
      position="sticky"
      className="shadow-sm border-b border-default-200 bg-black dark:bg-black"
    >
      {/* Sezione Sinistra - Logo */}
      <NavbarContent justify="start">
        <NavbarBrand>
          <NextLink href={isAuthenticated ? "/dashboard" : "/"} className="flex items-center">
            <Image
              src="/images/logo_200.png"
              alt="DEA3D Logo"
              width={120}
              height={40}
              className="h-10 w-auto"
              onError={(e) => {
                // Fallback se l'immagine non esiste
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
            {/* Fallback text logo se l'immagine non esiste */}
            <div className="hidden">
              <span className="font-bold text-xl text-primary">DEA3D</span>
            </div>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      {/* Sezione Centro - Telefono */}
      <NavbarContent justify="center">
        <NavbarItem>
          <Link
            href="tel:+390123456789"
            className="flex items-center gap-2 text-default-700 hover:text-primary transition-colors"
          >
            <PhoneIcon size={20} />
            <span className="font-medium hidden sm:inline">+39 ************</span>
            <span className="font-medium sm:hidden">************</span>
          </Link>
        </NavbarItem>
      </NavbarContent>

      {/* Sezione Destra - Condizionale */}
      <NavbarContent justify="end">
        <NavbarItem>
          {isAuthenticated && user ? (
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <div className="flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity">
                  <div className="text-right hidden sm:block">
                    <p className="text-sm font-medium text-white">{user.denominazione}</p>
                    <p className="text-xs text-default-400">@{user.username}</p>
                  </div>
                  <Avatar
                    size="sm"
                    name={user.nome_completo}
                    className="bg-primary text-white"
                  />
                </div>
              </DropdownTrigger>
              <DropdownMenu aria-label="User menu actions">
                <DropdownItem
                  key="profile"
                  className="h-14 gap-2"
                  textValue="Profile info"
                >
                  <div className="flex flex-col">
                    <p className="font-semibold">{user.denominazione}</p>
                    <p className="text-sm text-default-500">{user.email}</p>
                  </div>
                </DropdownItem>
                <DropdownItem
                  key="user-profile"
                  startContent={<UserIcon size={16} />}
                  onPress={handleProfile}
                >
                  Il mio profilo
                </DropdownItem>
                <DropdownItem
                  key="logout"
                  color="danger"
                  startContent={<LogoutIcon size={16} />}
                  onPress={handleLogout}
                >
                  Logout
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          ) : (
            <Button
              color="primary"
              variant="flat"
              onPress={handleLogin}
              className="font-medium"
            >
              Accedi
            </Button>
          )}
        </NavbarItem>
      </NavbarContent>
    </HeroUINavbar>
  );
}