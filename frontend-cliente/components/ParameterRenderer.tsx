import React from 'react';
import {
  Input,
  RadioGroup,
  Radio,
  Select,
  SelectItem,
  CheckboxGroup,
  Checkbox,
  Textarea,
  Card,
  CardBody,
  Image
} from '@heroui/react';
import { Parametro, ParameterValue, TipoControlloUI } from '../types/api';

// Define a type for control types without the prefix
type ControlType = 'SELECT' | 'RADIO' | 'CHECKBOX_GROUP' | 'INPUT_TEXT' | 'INPUT_NUMBER' | 'TEXTAREA';

interface ParameterRendererProps {
  parameter: Parametro;
  value: ParameterValue | undefined;
  onChange: (value: ParameterValue) => void;
  disabled?: boolean;
}

export const ParameterRenderer: React.FC<ParameterRendererProps> = ({
  parameter,
  value,
  onChange,
  disabled = false
}) => {
  const { nome_parametro, tipo_controllo_ui, descrizione, foto, valori } = parameter;
  
  // Estrae il tipo di controllo UI pulito
  const controlType = tipo_controllo_ui.replace('TipoControlloUI.', '') as ControlType;

  const renderControl = () => {
    switch (controlType) {
      case 'SELECT':
        return (
          <Select
            label={nome_parametro}
            description={descrizione}
            selectedKeys={value !== undefined ? [String(value)] : []}
            onSelectionChange={(keys) => {
              const selectedKey = Array.from(keys)[0];
              if (selectedKey) {
                onChange(Number(selectedKey));
              }
            }}
            isDisabled={disabled}
          >
            {valori.map(valore => (
              <SelectItem key={String(valore.id_valore_parametro)}>
                {valore.testo_visualizzato_ui}
              </SelectItem>
            ))}
          </Select>
        );

      case 'RADIO':
        return (
          <RadioGroup
            label={nome_parametro}
            description={descrizione}
            value={value !== undefined ? String(value) : ''}
            onValueChange={(val) => onChange(Number(val))}
            isDisabled={disabled}
          >
            {valori.map(valore => (
              <Radio key={String(valore.id_valore_parametro)} value={String(valore.id_valore_parametro)}>
                <div className="flex items-center gap-2">
                  {valore.foto && (
                    <Image
                      src={valore.foto}
                      alt={valore.testo_visualizzato_ui}
                      width={24}
                      height={24}
                      className="rounded"
                    />
                  )}
                  <span>{valore.testo_visualizzato_ui}</span>
                  {valore.colore && (
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: valore.colore }}
                    />
                  )}
                </div>
              </Radio>
            ))}
          </RadioGroup>
        );

      case 'CHECKBOX_GROUP':
        return (
          <CheckboxGroup
            label={nome_parametro}
            description={descrizione}
            value={Array.isArray(value) ? value.map(String) : []}
            onChange={(selected) => onChange(selected.map(Number))}
            isDisabled={disabled}
          >
            {valori.map(valore => (
              <Checkbox key={String(valore.id_valore_parametro)} value={String(valore.id_valore_parametro)}>
                <div className="flex items-center gap-2">
                  {valore.foto && (
                    <Image
                      src={valore.foto}
                      alt={valore.testo_visualizzato_ui}
                      width={24}
                      height={24}
                      className="rounded"
                    />
                  )}
                  <span>{valore.testo_visualizzato_ui}</span>
                  {valore.colore && (
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: valore.colore }}
                    />
                  )}
                </div>
              </Checkbox>
            ))}
          </CheckboxGroup>
        );

      case 'INPUT_TEXT':
        return (
          <Input
            label={nome_parametro}
            description={descrizione}
            value={typeof value === 'string' ? value : ''}
            onChange={(e) => onChange(e.target.value)}
            isDisabled={disabled}
          />
        );

      case 'INPUT_NUMBER':
        return (
          <Input
            type="number"
            label={nome_parametro}
            description={descrizione}
            value={typeof value === 'number' ? String(value) : ''}
            onChange={(e) => onChange(Number(e.target.value))}
            isDisabled={disabled}
          />
        );

      case 'TEXTAREA':
        return (
          <Textarea
            label={nome_parametro}
            description={descrizione}
            value={typeof value === 'string' ? value : ''}
            onChange={(e) => onChange(e.target.value)}
            isDisabled={disabled}
          />
        );

      default:
        return (
          <div className="text-red-500">
            Tipo di controllo non supportato: {tipo_controllo_ui}
          </div>
        );
    }
  };

  return (
    <Card className="w-full">
      <CardBody className="space-y-4">
        {foto && (
          <div className="flex justify-center">
            <Image
              src={foto}
              alt={nome_parametro}
              width={200}
              height={150}
              className="rounded-lg object-cover"
            />
          </div>
        )}
        {renderControl()}
      </CardBody>
    </Card>
  );
};