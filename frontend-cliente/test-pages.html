<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagine DEA3D</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .page { border: 1px solid #ccc; margin: 20px 0; padding: 20px; }
        .page h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .feature { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; flex: 1; }
        .action-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0; }
        .action-card { background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>Test delle Pagine Create per DEA3D</h1>
    
    <div class="page">
        <h2>🏠 Homepage (/) - Layout senza sidebar</h2>
        <p><strong>Descrizione:</strong> Pagina di benvenuto con header, contenuto e footer</p>
        
        <div style="text-align: center; padding: 20px;">
            <h1>Benvenuto in <span style="color: #007bff;">DEA3D</span></h1>
            <p>La piattaforma digitale per la gestione completa del laboratorio odontotecnico.</p>
            
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0;">
                <div class="feature">
                    <div style="font-size: 2em;">🦷</div>
                    <h3>Produzione CAD/CAM</h3>
                    <p>Gestisci l'intero workflow di produzione digitale</p>
                </div>
                <div class="feature">
                    <div style="font-size: 2em;">⚙️</div>
                    <h3>Componenti Protesici</h3>
                    <p>Ordina e traccia tutti i componenti necessari</p>
                </div>
                <div class="feature">
                    <div style="font-size: 2em;">📊</div>
                    <h3>Dashboard Completa</h3>
                    <p>Monitora progetti, ordini e comunicazioni</p>
                </div>
            </div>
            
            <button class="button">Accedi alla Piattaforma</button>
        </div>
    </div>

    <div class="page">
        <h2>🔐 Pagina Login (/login) - Layout senza sidebar</h2>
        <p><strong>Descrizione:</strong> Form di login semplice (senza logica reale per ora)</p>
        
        <div style="max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px;">
            <h3 style="text-align: center;">Accedi a DEA3D</h3>
            <p style="text-align: center; color: #666;">Inserisci le tue credenziali per accedere</p>
            
            <form style="display: flex; flex-direction: column; gap: 15px;">
                <input type="email" placeholder="Inserisci la tua email" style="padding: 10px; border: 1px solid #ccc; border-radius: 5px;" required>
                <input type="password" placeholder="Inserisci la tua password" style="padding: 10px; border: 1px solid #ccc; border-radius: 5px;" required>
                <button type="submit" class="button">Accedi</button>
            </form>
        </div>
    </div>

    <div class="page">
        <h2>📊 Dashboard (/dashboard) - Layout con sidebar</h2>
        <p><strong>Descrizione:</strong> Pannello di controllo con statistiche e azioni rapide</p>
        
        <div>
            <h1>Dashboard</h1>
            <p>Benvenuto nel tuo pannello di controllo DEA3D</p>
            
            <div class="stats">
                <div class="stat-card">
                    <div style="background: orange; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">5</div>
                    <p><strong>PROGETTI DA VALIDARE</strong></p>
                </div>
                <div class="stat-card">
                    <div style="background: green; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">12</div>
                    <p><strong>ORDINI IN PARTENZA</strong></p>
                </div>
                <div class="stat-card">
                    <div style="background: blue; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">3</div>
                    <p><strong>COMUNICAZIONI</strong></p>
                </div>
            </div>
            
            <h2>Azioni Rapide</h2>
            <div class="action-grid">
                <div class="action-card">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 2em;">🦷</div>
                        <div>
                            <h3>Ordina produzione CAD CAM</h3>
                            <p>Gestisci ordini per produzione CAD/CAM</p>
                        </div>
                    </div>
                </div>
                <div class="action-card">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 2em;">🔧</div>
                        <div>
                            <h3>Ordina componenti protesici</h3>
                            <p>Gestisci ordini per componenti protesici</p>
                        </div>
                    </div>
                </div>
                <div class="action-card">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 2em;">📋</div>
                        <div>
                            <h3>Gestione progetti</h3>
                            <p>Visualizza e gestisci tutti i progetti</p>
                        </div>
                    </div>
                </div>
                <div class="action-card">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 2em;">💬</div>
                        <div>
                            <h3>Comunicazioni</h3>
                            <p>Visualizza messaggi e notifiche</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="page">
        <h2>🔧 Funzionalità Implementate</h2>
        <ul>
            <li>✅ <strong>Homepage (/):</strong> Layout senza sidebar, header, contenuto con testo e bottone login, footer</li>
            <li>✅ <strong>Pagina Login (/login):</strong> Form di login semplice, redirect alla dashboard</li>
            <li>✅ <strong>Dashboard (/dashboard):</strong> Layout con sidebar, statistiche e bottoni azioni</li>
            <li>✅ <strong>Layout Condizionale:</strong> Homepage e login senza sidebar, dashboard e altre pagine con sidebar</li>
            <li>✅ <strong>Componenti:</strong> ConditionalLayout per gestire la sidebar condizionalmente</li>
            <li>✅ <strong>Routing:</strong> Navigazione tra le pagine con Next.js App Router</li>
        </ul>
    </div>

    <div class="page">
        <h2>📁 File Creati/Modificati</h2>
        <ul>
            <li><code>app/page.tsx</code> - Nuova homepage con contenuto e bottone login</li>
            <li><code>app/login/page.tsx</code> - Pagina di login con form</li>
            <li><code>app/dashboard/page.tsx</code> - Dashboard con statistiche e azioni</li>
            <li><code>app/layout.tsx</code> - Layout principale modificato per supportare layout condizionale</li>
            <li><code>components/ConditionalLayout.tsx</code> - Componente per gestire sidebar condizionalmente</li>
            <li><code>contexts/AuthContext.tsx</code> - Context per gestione autenticazione (preparato per futuro)</li>
        </ul>
    </div>

    <div class="page">
        <h2>🚀 Prossimi Passi</h2>
        <ul>
            <li>Implementare logica di autenticazione reale con JWT</li>
            <li>Aggiungere protezione delle route autenticate</li>
            <li>Sostituire emoji con icone SVG professionali</li>
            <li>Implementare le pagine collegate dai bottoni della dashboard</li>
            <li>Aggiungere gestione errori e loading states</li>
            <li>Implementare logout e gestione sessione</li>
        </ul>
    </div>
</body>
</html>