<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Adapted API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Test Adapted API (Simulating Frontend Calls)</h1>
    
    <div class="section">
        <h2>1. Test getLavorazioni() - Should return Lavorazione format</h2>
        <button onclick="testGetLavorazioni()">Test getLavorazioni</button>
        <div id="lavorazioni-result" class="result"></div>
    </div>

    <div class="section">
        <h2>2. Test getConfigurazioneIniziale() - Should return ParametroConfigurazione format</h2>
        <button onclick="testGetConfigurazioneIniziale()">Test getConfigurazioneIniziale</button>
        <div id="config-result" class="result"></div>
    </div>

    <div class="section">
        <h2>3. Test postConfigurazioneDinamica() - Should return ConfigurazioneDinamicaResponse format</h2>
        <button onclick="testPostConfigurazioneDinamica()">Test postConfigurazioneDinamica</button>
        <div id="dynamic-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://*************:5001/api/v1';

        // Simulated BackendAdapter functions
        class BackendAdapter {
            static parametroToLavorazione(parametro) {
                return {
                    id_lavorazione: parametro.id_parametro,
                    codice_lavorazione: `LAV_${parametro.id_parametro}`,
                    nome_lavorazione: parametro.nome_parametro,
                    descrizione: parametro.descrizione || ''
                };
            }

            static parametriToLavorazioni(parametri) {
                return parametri
                    .filter(p => p.is_root)
                    .map(p => this.parametroToLavorazione(p));
            }

            static backendValoreToFrontendValore(valore) {
                return {
                    id_valore_parametro: valore.id_valore_parametro,
                    valore_memorizzato: valore.testo_visualizzato_ui,
                    testo_visualizzato_ui: valore.testo_visualizzato_ui,
                    default_selezionato: false
                };
            }

            static parametroToParametroConfigurazione(parametro) {
                return {
                    id_parametro: parametro.id_parametro,
                    codice_parametro: `PARAM_${parametro.id_parametro}`,
                    nome_parametro: parametro.nome_parametro,
                    tipo_controllo_ui: parametro.tipo_controllo_ui,
                    obbligatorio: false,
                    ordine_visualizzazione: 0,
                    valori_disponibili: parametro.valori.map(v => this.backendValoreToFrontendValore(v)),
                    placeholder_ui: undefined,
                    unita_misura: undefined,
                    ha_dipendenze_figlie: parametro.has_dependent_children
                };
            }

            static parametriToParametriConfigurazione(parametri) {
                return parametri.map(p => this.parametroToParametroConfigurazione(p));
            }

            static evaluationResponseToConfigurazioneDinamica(evaluationResponse) {
                return {
                    nuovi_parametri_dipendenti: this.parametriToParametriConfigurazione(evaluationResponse.dependent_parameters),
                    aggiornamenti_valori_parametri_visibili: [],
                    id_parametri_da_rimuovere: []
                };
            }

            static frontendSelectionsToBackendSelections(selezioniCorrenti) {
                return selezioniCorrenti
                    .filter(s => typeof s.id_valore_selezionato === 'number')
                    .map(s => ({
                        parameter_id: s.id_parametro,
                        value_id: s.id_valore_selezionato
                    }));
            }
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        async function testGetLavorazioni() {
            const resultDiv = document.getElementById('lavorazioni-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                // Simulate the adapted API call
                const rootParameters = await makeRequest(`${API_BASE}/parameters/root`);
                const lavorazioni = BackendAdapter.parametriToLavorazioni(rootParameters);
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Success! Converted ${rootParameters.length} root parameters to ${lavorazioni.length} lavorazioni</div>
                    <h4>Original Backend Data:</h4>
                    <pre>${JSON.stringify(rootParameters, null, 2)}</pre>
                    <h4>Adapted Frontend Data:</h4>
                    <pre>${JSON.stringify(lavorazioni, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }

        async function testGetConfigurazioneIniziale() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                // Simulate the adapted API call
                const idLavorazione = 1;
                let initialConfig = await makeRequest(`${API_BASE}/parameters/${idLavorazione}/initial-config`);
                
                // If empty, try to get dependent parameters by evaluating first value
                if (initialConfig.length === 0) {
                    const rootParameter = await makeRequest(`${API_BASE}/parameters/${idLavorazione}`);
                    if (rootParameter.valori.length > 0) {
                        const firstValue = rootParameter.valori[0];
                        try {
                            const evaluationResponse = await makeRequest(`${API_BASE}/dependency_evaluation/evaluate`, {
                                method: 'POST',
                                body: JSON.stringify({
                                    current_parameters: [{
                                        parameter_id: idLavorazione,
                                        value_id: firstValue.id_valore_parametro
                                    }]
                                })
                            });
                            initialConfig = evaluationResponse.dependent_parameters;
                        } catch (evalError) {
                            console.warn('Could not evaluate dependencies:', evalError);
                        }
                    }
                }
                
                const parametriConfigurazione = BackendAdapter.parametriToParametriConfigurazione(initialConfig);
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Success! Converted ${initialConfig.length} parameters to ParametroConfigurazione format</div>
                    <h4>Original Backend Data:</h4>
                    <pre>${JSON.stringify(initialConfig, null, 2)}</pre>
                    <h4>Adapted Frontend Data:</h4>
                    <pre>${JSON.stringify(parametriConfigurazione, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }

        async function testPostConfigurazioneDinamica() {
            const resultDiv = document.getElementById('dynamic-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                // Simulate the adapted API call
                const selezioniCorrenti = [
                    { id_parametro: 1, id_valore_selezionato: 1 } // Lavorazioni -> Ponte
                ];
                
                const backendSelections = BackendAdapter.frontendSelectionsToBackendSelections(selezioniCorrenti);
                
                const evaluationResponse = await makeRequest(`${API_BASE}/dependency_evaluation/evaluate`, {
                    method: 'POST',
                    body: JSON.stringify({
                        current_parameters: backendSelections
                    })
                });

                const configurazioneDinamica = BackendAdapter.evaluationResponseToConfigurazioneDinamica(evaluationResponse);
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Success! Converted evaluation response to ConfigurazioneDinamicaResponse format</div>
                    <h4>Frontend Input:</h4>
                    <pre>${JSON.stringify(selezioniCorrenti, null, 2)}</pre>
                    <h4>Backend Input (converted):</h4>
                    <pre>${JSON.stringify(backendSelections, null, 2)}</pre>
                    <h4>Backend Response:</h4>
                    <pre>${JSON.stringify(evaluationResponse, null, 2)}</pre>
                    <h4>Adapted Frontend Response:</h4>
                    <pre>${JSON.stringify(configurazioneDinamica, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>