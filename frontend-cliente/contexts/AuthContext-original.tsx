"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface User {
  id: number;
  username: string;
  email: string;
  nome_completo: string;
  denominazione: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verifica se l'utente e' autenticato al caricamento
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Verifica il token con l'endpoint /me
      const response = await fetch(`${API_BASE_URL}/api/client/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser({
          id: userData.id,
          username: userData.username,
          email: userData.email,
          nome_completo: userData.nome_completo,
          denominazione: userData.denominazione,
        });
        setIsAuthenticated(true);
      } else {
        // Token non valido, prova a fare refresh
        await refreshToken();
      }
    } catch (error) {
      console.error('Errore verifica autenticazione:', error);
      // Rimuovi token non validi
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/client/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Errore durante il login');
      }

      const data = await response.json();
      
      // Salva i token
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('refresh_token', data.refresh_token);
      
      // Imposta i dati utente
      setUser({
        id: data.user.id,
        username: data.user.username,
        email: data.user.email,
        nome_completo: data.user.nome_completo,
        denominazione: data.user.denominazione,
      });
      setIsAuthenticated(true);
      
    } catch (error) {
      console.error('Errore login:', error);
      throw error;
    }
  };

  const logout = () => {
    // Rimuovi i token
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    
    // Reset stato
    setIsAuthenticated(false);
    setUser(null);
    
    // Redirect al login
    window.location.href = '/login';
  };

  const refreshToken = async () => {
    try {
      const refreshTokenValue = localStorage.getItem('refresh_token');
      if (!refreshTokenValue) {
        throw new Error('Nessun refresh token disponibile');
      }

      const response = await fetch(`${API_BASE_URL}/api/client/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${refreshTokenValue}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Refresh token non valido');
      }

      const data = await response.json();
      
      // Aggiorna il token
      localStorage.setItem('access_token', data.access_token);
      
      // Riprova a ottenere i dati utente
      await checkAuthStatus();
      
    } catch (error) {
      console.error('Errore refresh token:', error);
      // Logout se il refresh fallisce
      logout();
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      user, 
      isLoading, 
      login, 
      logout, 
      refreshToken 
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}