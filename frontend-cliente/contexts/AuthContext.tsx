"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface User {
  id: number;
  username: string;
  email: string;
  nome_completo: string;
  denominazione: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  debugInfo: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Debug: Hardcode URL per test
const API_BASE_URL = 'https://dea3d-api.prismanet.com';

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState('');
  const [isClient, setIsClient] = useState(false);

  // Inizializza il client-side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Debug info
  useEffect(() => {
    if (!isClient) return;
    
    const envUrl = process.env.NEXT_PUBLIC_API_URL;
    const envBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    setDebugInfo(`ENV_URL: ${envUrl}, ENV_BASE_URL: ${envBaseUrl}, USING: ${API_BASE_URL}`);
    console.log('🔍 Auth Debug:', {
      NEXT_PUBLIC_API_URL: envUrl,
      NEXT_PUBLIC_API_BASE_URL: envBaseUrl,
      API_BASE_URL: API_BASE_URL,
      window_location: typeof window !== 'undefined' ? window.location.href : 'SSR'
    });
  }, [isClient]);

  useEffect(() => {
    if (isClient) {
      checkAuthStatus();
    }
  }, [isClient]);

  const checkAuthStatus = async () => {
    try {
      // Check if we're in the browser before accessing localStorage
      if (typeof window === 'undefined') {
        setIsLoading(false);
        return;
      }
      
      const token = localStorage.getItem('access_token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      console.log('🔍 Checking auth with token:', token.substring(0, 20) + '...');
      
      const response = await fetch(`${API_BASE_URL}/api/client/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Auth check response:', response.status);

      if (response.ok) {
        const userData = await response.json();
        console.log('🔍 User data:', userData);
        setUser({
          id: userData.id,
          username: userData.username,
          email: userData.email,
          nome_completo: userData.nome_completo,
          denominazione: userData.denominazione,
        });
        setIsAuthenticated(true);
      } else {
        console.log('🔍 Auth check failed, trying refresh...');
        await refreshToken();
      }
    } catch (error) {
      console.error('🔍 Auth check error:', error);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      console.log('🔍 Login attempt:', { username, url: `${API_BASE_URL}/api/client/auth/login` });
      
      const response = await fetch(`${API_BASE_URL}/api/client/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      console.log('🔍 Login response status:', response.status);
      console.log('🔍 Login response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('🔍 Login error data:', errorData);
        throw new Error(errorData.msg || 'Errore durante il login');
      }

      const data = await response.json();
      console.log('🔍 Login success data:', data);
      
      // Salva i token
      if (typeof window !== 'undefined') {
        localStorage.setItem('access_token', data.access_token);
        localStorage.setItem('refresh_token', data.refresh_token);
      }
      
      // Imposta i dati utente
      setUser({
        id: data.user.id,
        username: data.user.username,
        email: data.user.email,
        nome_completo: data.user.nome_completo,
        denominazione: data.user.denominazione,
      });
      setIsAuthenticated(true);
      
    } catch (error) {
      console.error('🔍 Login error:', error);
      throw error;
    }
  };

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
    setIsAuthenticated(false);
    setUser(null);
    // Forza il reindirizzamento al login
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  };

  const refreshToken = async () => {
    try {
      if (typeof window === 'undefined') {
        throw new Error('Not in browser environment');
      }
      
      const refreshTokenValue = localStorage.getItem('refresh_token');
      if (!refreshTokenValue) {
        throw new Error('Nessun refresh token disponibile');
      }

      const response = await fetch(`${API_BASE_URL}/api/client/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${refreshTokenValue}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Refresh token non valido');
      }

      const data = await response.json();
      if (typeof window !== 'undefined') {
        localStorage.setItem('access_token', data.access_token);
      }
      await checkAuthStatus();
      
    } catch (error) {
      console.error('🔍 Refresh error:', error);
      logout();
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      user, 
      isLoading, 
      login, 
      logout, 
      refreshToken,
      debugInfo
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}