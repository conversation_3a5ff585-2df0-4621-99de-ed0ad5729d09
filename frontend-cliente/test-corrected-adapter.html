<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Corrected Adapter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .success { color: green; }
        .error { color: red; }
        .lavorazione { 
            border: 1px solid #ddd; 
            padding: 10px; 
            margin: 5px; 
            border-radius: 5px; 
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Test Corrected Adapter - Lavorazioni from Values</h1>
    
    <div class="section">
        <h2>1. Test Corrected getLavorazioni() - Should show individual work types</h2>
        <button onclick="testCorrectedGetLavorazioni()">Test Corrected getLavorazioni</button>
        <div id="corrected-lavorazioni-result" class="result"></div>
    </div>

    <div class="section">
        <h2>2. Test getConfigurazioneIniziale() for specific work type</h2>
        <button onclick="testConfigForPonte()">Test Config for Ponte (ID: 1)</button>
        <button onclick="testConfigForIntarsio()">Test Config for Intarsio (ID: 2)</button>
        <div id="config-specific-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://*************:5001/api/v1';

        // Corrected BackendAdapter functions
        class CorrectedBackendAdapter {
            static valoreParametroToLavorazione(valore, parametroRoot) {
                return {
                    id_lavorazione: valore.id_valore_parametro,
                    codice_lavorazione: `LAV_${valore.id_valore_parametro}`,
                    nome_lavorazione: valore.testo_visualizzato_ui,
                    descrizione: valore.descrizione || ''
                };
            }

            static parametriToLavorazioni(parametri) {
                const lavorazioni = [];
                
                parametri
                    .filter(p => p.is_root)
                    .forEach(parametroRoot => {
                        // Ogni valore del parametro root diventa una lavorazione
                        parametroRoot.valori.forEach(valore => {
                            lavorazioni.push(this.valoreParametroToLavorazione(valore, parametroRoot));
                        });
                    });
                
                return lavorazioni;
            }

            static backendValoreToFrontendValore(valore) {
                return {
                    id_valore_parametro: valore.id_valore_parametro,
                    valore_memorizzato: valore.testo_visualizzato_ui,
                    testo_visualizzato_ui: valore.testo_visualizzato_ui,
                    default_selezionato: false
                };
            }

            static parametroToParametroConfigurazione(parametro, ordineVisualizzazione = 0) {
                return {
                    id_parametro: parametro.id_parametro,
                    codice_parametro: `PARAM_${parametro.id_parametro}`,
                    nome_parametro: parametro.nome_parametro,
                    tipo_controllo_ui: parametro.tipo_controllo_ui,
                    obbligatorio: false,
                    ordine_visualizzazione: ordineVisualizzazione,
                    valori_disponibili: parametro.valori.map(v => this.backendValoreToFrontendValore(v)),
                    placeholder_ui: undefined,
                    unita_misura: undefined,
                    ha_dipendenze_figlie: parametro.has_dependent_children
                };
            }

            static parametriToParametriConfigurazione(parametri) {
                return parametri.map((p, index) => this.parametroToParametroConfigurazione(p, index));
            }
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        async function testCorrectedGetLavorazioni() {
            const resultDiv = document.getElementById('corrected-lavorazioni-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                // Simulate the corrected adapted API call
                const rootParameters = await makeRequest(`${API_BASE}/parameters/root`);
                const lavorazioni = CorrectedBackendAdapter.parametriToLavorazioni(rootParameters);
                
                let lavorazioniHtml = '';
                lavorazioni.forEach(lav => {
                    lavorazioniHtml += `
                        <div class="lavorazione">
                            <strong>${lav.nome_lavorazione}</strong><br>
                            ID: ${lav.id_lavorazione}<br>
                            Codice: ${lav.codice_lavorazione}
                        </div>
                    `;
                });
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Success! Converted to ${lavorazioni.length} individual lavorazioni</div>
                    <h4>Individual Lavorazioni (Work Types):</h4>
                    ${lavorazioniHtml}
                    <h4>Raw Data:</h4>
                    <pre>${JSON.stringify(lavorazioni, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }

        async function testConfigForWorkType(workTypeId, workTypeName) {
            const resultDiv = document.getElementById('config-specific-result');
            resultDiv.innerHTML = `Loading configuration for ${workTypeName}...`;
            
            try {
                // Simulate the corrected getConfigurazioneIniziale
                // First find the root parameter that contains this value
                const rootParameters = await makeRequest(`${API_BASE}/parameters/root`);
                let rootParameterId = null;
                
                for (const rootParam of rootParameters) {
                    const hasValue = rootParam.valori.some(v => v.id_valore_parametro === workTypeId);
                    if (hasValue) {
                        rootParameterId = rootParam.id_parametro;
                        break;
                    }
                }
                
                if (!rootParameterId) {
                    throw new Error(`Could not find root parameter for value ID ${workTypeId}`);
                }
                
                // Now evaluate dependencies for this selection
                const evaluationResponse = await makeRequest(`${API_BASE}/dependency_evaluation/evaluate`, {
                    method: 'POST',
                    body: JSON.stringify({
                        current_parameters: [
                            {
                                parameter_id: rootParameterId,
                                value_id: workTypeId
                            }
                        ]
                    })
                });
                
                const parametriConfigurazione = CorrectedBackendAdapter.parametriToParametriConfigurazione(evaluationResponse.dependent_parameters);
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Success! Configuration for ${workTypeName} (ID: ${workTypeId})</div>
                    <h4>Root Parameter ID: ${rootParameterId}</h4>
                    <h4>Dependent Parameters Found: ${parametriConfigurazione.length}</h4>
                    <pre>${JSON.stringify(parametriConfigurazione, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }

        async function testConfigForPonte() {
            await testConfigForWorkType(1, 'Ponte');
        }

        async function testConfigForIntarsio() {
            await testConfigForWorkType(2, 'Intarsio');
        }
    </script>
</body>
</html>