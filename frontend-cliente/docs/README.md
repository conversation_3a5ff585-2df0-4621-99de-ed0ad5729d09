# Sistema di Configurazione Dentale - Documentazione Completa

## 📋 Indice

1. [Panoramica del Sistema](#panoramica-del-sistema)
2. [Architettura](#architettura)
3. [Guida Utente](#guida-utente)
4. [<PERSON>uida Sviluppatore](#guida-sviluppatore)
5. [API Reference](#api-reference)
6. [Troubleshooting](#troubleshooting)
7. [Deployment](#deployment)

---

## 📖 Panoramica del Sistema

### Scopo
Sistema web per la configurazione dinamica di lavorazioni dentali con interfaccia interattiva per la selezione di denti e parametri di lavorazione.

### Caratteristiche Principali
- **Mappa Interattiva dei Denti**: Selezione visuale dei denti da lavorare
- **Configurazione Dinamica**: Parametri che cambiano in base alle selezioni
- **Sistema di Dipendenze**: Regole complesse per mostrare/nascondere parametri
- **Interfaccia Intuitiva**: Modal per configurazione dettagliata
- **Riepilogo Completo**: Visualizzazione delle configurazioni selezionate

### Tecnologie Utilizzate
- **Frontend**: Next.js 15, React 18, TypeScript, HeroUI, TailwindCSS
- **Backend**: Python Flask, SQLAlchemy, MySQL
- **Architettura**: REST API con layer di adattamento

---

## 🏗️ Architettura

### Diagramma Architetturale

```
┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND CLIENTE                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  TeethMap   │  │    Modal    │  │   SelectedWorks     │  │
│  │ (Mappa UI)  │  │(Configuraz.)│  │    (Riepilogo)      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   LAYER DI ADATTAMENTO                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Adapted API │  │   Backend   │  │   Type Adapters     │  │
│  │  Service    │  │   Adapter   │  │   (Conversioni)     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    BACKEND API                             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Parameters  │  │ Dependency  │  │   Database          │  │
│  │    API      │  │ Evaluation  │  │   (MySQL)           │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Flusso dei Dati

```
1. Caricamento Pagina
   ├── Frontend richiede parametri root
   ├── Backend restituisce parametri "Lavorazioni"
   ├── Adapter converte valori in lavorazioni separate
   └── UI mostra mappa denti + carica lavorazioni

2. Selezione Dente
   ├── Click su dente → Apre modal
   ├── Modal mostra bottoni lavorazioni (Ponte, Intarsio, etc.)
   └── Utente seleziona tipo lavorazione

3. Configurazione Parametri
   ├── Selezione lavorazione → Richiesta configurazione iniziale
   ├── Backend valuta dipendenze per la selezione
   ├── Adapter converte parametri in formato frontend
   └── UI mostra controlli dinamici

4. Valutazione Dinamica
   ├── Modifica parametro → Trigger valutazione dipendenze
   ├── Backend applica regole complesse
   ├── Nuovi parametri aggiunti/rimossi dinamicamente
   └── UI aggiorna controlli in tempo reale

5. Salvataggio
   ├── Utente conferma configurazione
   ├── Dati salvati in stato locale
   └── Riepilogo aggiornato
```

---

## 👤 Guida Utente

### Accesso al Sistema

1. **Navigazione**: Aprire `http://localhost:3000/produzione-cad-cam/pagina2`
2. **Caricamento**: Il sistema carica automaticamente la mappa dei denti
3. **Stato Iniziale**: Mappa vuota pronta per la selezione

### Configurazione di una Lavorazione

#### Passo 1: Selezione del Dente
- **Click su un dente** nella mappa interattiva
- Si apre il modal "Seleziona Lavorazione per Dente X"

#### Passo 2: Scelta della Lavorazione
Il modal mostra i tipi di lavorazione disponibili:
- 🔘 **Ponte**: Per lavorazioni di ponte dentale
- 🔘 **Intarsio**: Per restauri intarsiati
- 🔘 **Elemento Singolo/Intarsio**: Per elementi singoli
- 🔘 **Elemento Singolo Avvitato**: Per impianti avvitati

#### Passo 3: Configurazione Parametri
Dopo aver selezionato una lavorazione:
1. **Parametri Iniziali**: Appaiono automaticamente i parametri base
2. **Parametri Dinamici**: Nuovi parametri appaiono in base alle selezioni
3. **Controlli Disponibili**:
   - **Select/Radio**: Selezione singola da lista
   - **Checkbox**: Selezione multipla
   - **Input Text**: Inserimento testo libero
   - **Input Number**: Inserimento valori numerici

#### Passo 4: Salvataggio
- **Click "Salva"**: Conferma la configurazione
- **Aggiornamento Automatico**: Il riepilogo si aggiorna
- **Indicatore Visuale**: Il dente viene evidenziato sulla mappa

### Gestione delle Configurazioni

#### Modifica di una Configurazione Esistente
1. **Click sul dente già configurato**
2. Il modal si apre con la configurazione attuale
3. **Modifica parametri** come necessario
4. **Salva** per aggiornare

#### Rimozione di una Configurazione
1. **Apri modal** del dente configurato
2. **Deseleziona la lavorazione** (click sullo stesso bottone)
3. **Salva** per rimuovere la configurazione

#### Visualizzazione Riepilogo
Il pannello destro mostra:
- **Lista denti configurati**
- **Tipo di lavorazione** per ogni dente
- **Parametri selezionati** con i loro valori
- **Aggiornamento in tempo reale**

### Funzionalità Avanzate

#### Selezione Ponti
- **Click sui cerchi** tra i denti per selezionare ponti
- **Configurazione multipla**: Un ponte può coinvolgere più denti
- **Gestione coordinata**: Parametri condivisi tra denti del ponte

#### Parametri Dipendenti
- **Cascata di Parametri**: Alcuni parametri ne attivano altri
- **Validazione Automatica**: Controlli di coerenza in tempo reale
- **Suggerimenti**: Valori predefiniti intelligenti

---

## 👨‍💻 Guida Sviluppatore

### Setup Ambiente di Sviluppo

#### Prerequisiti
```bash
# Node.js 18+
node --version

# npm o yarn
npm --version

# Backend Python in esecuzione
curl http://*************:5001/api/v1/parameters/root
```

#### Installazione
```bash
# Clone del repository
git clone <repository-url>
cd frontend-cliente

# Installazione dipendenze
npm install

# Configurazione ambiente
cp .env.example .env.local
# Editare .env.local con le configurazioni corrette

# Avvio sviluppo
npm run dev
```

#### Struttura del Progetto
```
frontend-cliente/
├── app/                          # Next.js App Router
│   ├── produzione-cad-cam/
│   │   ├── pagina1/              # Form informazioni laboratorio
│   │   └── pagina2/              # Configuratore principale
│   │       ├── components/       # Componenti specifici
│   │       ├── adapters/         # Layer di adattamento
│   │       ├── services/         # Servizi API
│   │       └── types.ts          # Tipi TypeScript
│   └── configuratore/            # Nuovo configuratore (alternativo)
├── components/                   # Componenti riutilizzabili
├── services/                     # Servizi API globali
├── types/                        # Tipi globali
├── hooks/                        # Custom hooks
└── docs/                         # Documentazione
```

### Componenti Principali

#### TeethMap
```typescript
// Componente mappa interattiva
interface TeethMapProps {
  onTeethSelect: (toothId: string) => void;
  onBridgeSelect: (bridgeId: string) => void;
  selectedTeeth: string[];
  selectedBridges: string[];
  teethWorks: Record<string, SelectedWorkEntry>;
}
```

**Responsabilità**:
- Rendering mappa SVG dei denti
- Gestione click e selezioni
- Indicatori visuali per denti configurati

#### TeethWorkModal
```typescript
// Modal di configurazione
interface TeethWorkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (lavorazione: Lavorazione | null, parameters?: Record<number, any>, allDynamicParameters?: ParametroConfigurazione[] | null) => void;
  toothId: string;
  currentWorkType?: string;
  currentParameters?: Record<number, any>;
  availableWorkTypes: Lavorazione[];
}
```

**Responsabilità**:
- Gestione stato modal
- Caricamento configurazione iniziale
- Valutazione dinamica parametri
- Debouncing chiamate API

#### DynamicParameterForm
```typescript
// Form parametri dinamici
interface DynamicParameterFormProps {
  dynamicParameters: ParametroConfigurazione[];
  selectedDynamicValues: Record<number, any>;
  onDynamicValueChange: (paramId: number, value: any) => void;
  loadingConfig: boolean;
  configError: string | null;
}
```

**Responsabilità**:
- Rendering controlli UI dinamici
- Gestione tipi di input diversi
- Validazione e formattazione valori

### Layer di Adattamento

#### BackendAdapter
```typescript
export class BackendAdapter {
  // Converte valori parametro root in lavorazioni
  static parametriToLavorazioni(parametri: Parametro[]): Lavorazione[]
  
  // Converte parametri backend in formato frontend
  static parametriToParametriConfigurazione(parametri: Parametro[]): ParametroConfigurazione[]
  
  // Converte richieste frontend in formato backend
  static frontendSelectionsToBackendSelections(selezioni: any[]): any[]
}
```

**Scopo**: Isolare le differenze tra formato dati backend e frontend

#### AdaptedAPI
```typescript
// Mantiene interfaccia esistente usando nuovo backend
export async function getLavorazioni(): Promise<Lavorazione[]>
export async function getConfigurazioneIniziale(id: number): Promise<ParametroConfigurazione[]>
export async function postConfigurazioneDinamica(...): Promise<ConfigurazioneDinamicaResponse>
```

**Scopo**: Compatibilità con codice esistente

### Gestione dello Stato

#### Stato Locale (Modal)
```typescript
const [selectedWork, setSelectedWork] = useState<string | null>(null);
const [dynamicParameters, setDynamicParameters] = useState<ParametroConfigurazione[] | null>(null);
const [selectedDynamicValues, setSelectedDynamicValues] = useState<Record<number, any>>({});
```

#### Stato Globale (Pagina)
```typescript
const [teethWorks, setTeethWorks] = useState<Record<string, SelectedWorkEntry>>({});
const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);
const [selectedBridges, setSelectedBridges] = useState<string[]>([]);
```

### Pattern di Sviluppo

#### Aggiunta Nuovo Tipo di Controllo UI
1. **Backend**: Aggiungere enum in `TipoControlloUI`
2. **Adapter**: Nessuna modifica necessaria
3. **Frontend**: Aggiungere case in `DynamicParameterForm`

```typescript
// In DynamicParameterForm.tsx
case 'NUOVO_CONTROLLO':
  return (
    <NuovoComponente
      label={parameter.nome_parametro}
      value={value}
      onChange={onChange}
    />
  );
```

#### Aggiunta Nuova Regola di Dipendenza
1. **Database**: Inserire in `RegolaDipendenzaComplessa`
2. **Backend**: Logica automatica in `dependency_evaluation`
3. **Frontend**: Gestione automatica tramite adapter

#### Debug e Logging
```typescript
// Abilitare debug dettagliato
localStorage.setItem('debug', 'true');

// Console logs disponibili:
// - Caricamento lavorazioni
// - Configurazione iniziale
// - Valutazione dinamica
// - Conversioni adapter
```

### Testing

#### Test Unitari
```bash
# Test componenti
npm run test

# Test con coverage
npm run test:coverage
```

#### Test Integrazione
```bash
# Test API adapter
open test-corrected-adapter.html

# Test flusso completo
npm run test:e2e
```

#### Test Manuali
1. **Caricamento**: Verificare mappa e lavorazioni
2. **Selezione**: Click dente → Modal con 4 bottoni
3. **Configurazione**: Selezione parametri dinamici
4. **Salvataggio**: Aggiornamento riepilogo
5. **Modifica**: Riapertura configurazione esistente

---

## 📚 API Reference

### Endpoint Backend

#### GET /api/v1/parameters/root
**Descrizione**: Recupera parametri root (lavorazioni)
```json
// Response
[
  {
    "id_parametro": 1,
    "nome_parametro": "Lavorazioni",
    "is_root": true,
    "has_dependent_children": true,
    "valori": [
      {
        "id_valore_parametro": 1,
        "testo_visualizzato_ui": "Ponte"
      }
    ]
  }
]
```

#### GET /api/v1/parameters/{id}
**Descrizione**: Dettagli parametro specifico
```json
// Response
{
  "id_parametro": 1,
  "nome_parametro": "Lavorazioni",
  "tipo_controllo_ui": "TipoControlloUI.SELECT",
  "valori": [...],
  "has_dependent_children": true
}
```

#### POST /api/v1/dependency_evaluation/evaluate
**Descrizione**: Valuta dipendenze parametri
```json
// Request
{
  "current_parameters": [
    {
      "parameter_id": 1,
      "value_id": 1
    }
  ]
}

// Response
{
  "dependent_parameters": [...]
}
```

### API Frontend Adattate

#### getLavorazioni()
```typescript
// Converte parametri root in lavorazioni
const lavorazioni = await getLavorazioni();
// Returns: Lavorazione[]
```

#### getConfigurazioneIniziale(id)
```typescript
// Ottiene configurazione per lavorazione specifica
const config = await getConfigurazioneIniziale(1); // ID valore "Ponte"
// Returns: ParametroConfigurazione[]
```

#### postConfigurazioneDinamica(...)
```typescript
// Valuta dipendenze dinamiche
const response = await postConfigurazioneDinamica(
  idLavorazione,
  selezioniCorrenti,
  parametriVisualizzati,
  parametroTrigger,
  valorePrecedente
);
// Returns: ConfigurazioneDinamicaResponse
```

### Tipi TypeScript

#### Lavorazione
```typescript
interface Lavorazione {
  id_lavorazione: number;        // ID valore parametro
  codice_lavorazione: string;    // Codice generato
  nome_lavorazione: string;      // Nome visualizzato
  descrizione: string;           // Descrizione opzionale
}
```

#### ParametroConfigurazione
```typescript
interface ParametroConfigurazione {
  id_parametro: number;
  nome_parametro: string;
  tipo_controllo_ui: string;
  valori_disponibili: ValoreParametro[];
  ha_dipendenze_figlie: boolean;
  obbligatorio: boolean;
  ordine_visualizzazione: number;
}
```

#### ValoreParametro
```typescript
interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
  valore_memorizzato: string;
  default_selezionato: boolean;
}
```

---

## 🔧 Troubleshooting

### Problemi Comuni

#### 1. Modal mostra solo "Lavorazioni"
**Sintomo**: Un solo bottone invece di 4 bottoni separati
**Causa**: Adapter non aggiornato o cache browser
**Soluzione**:
```bash
# Verificare adapter
grep -n "valoreParametroToLavorazione" frontend-cliente/app/produzione-cad-cam/pagina2/adapters/backend-adapter.ts

# Pulire cache
rm -rf .next
npm run dev
```

#### 2. Errore 404 su API
**Sintomo**: `Failed to fetch` nelle chiamate API
**Causa**: Backend non in esecuzione o URL errato
**Soluzione**:
```bash
# Verificare backend
curl http://*************:5001/api/v1/parameters/root

# Verificare configurazione
cat frontend-cliente/.env.local
```

#### 3. Parametri dinamici non caricano
**Sintomo**: Selezione lavorazione non mostra parametri
**Causa**: Regole dipendenze non configurate o errore valutazione
**Soluzione**:
```bash
# Test manuale valutazione
curl -X POST http://*************:5001/api/v1/dependency_evaluation/evaluate \
  -H "Content-Type: application/json" \
  -d '{"current_parameters": [{"parameter_id": 1, "value_id": 1}]}'

# Verificare console browser per errori
```

#### 4. Configurazioni non si salvano
**Sintomo**: Modal si chiude ma riepilogo non aggiorna
**Causa**: Errore nella funzione `handleWorkSave`
**Soluzione**:
```typescript
// Verificare console per errori
// Controllare che selectedWork sia valorizzato
console.log('selectedWork:', selectedWork);
console.log('availableWorkTypes:', availableWorkTypes);
```

### Log di Debug

#### Abilitazione Debug
```typescript
// In console browser
localStorage.setItem('debug', 'true');
location.reload();
```

#### Log Disponibili
- `Loading lavorazioni...`: Caricamento iniziale
- `Calling getConfigurazioneIniziale`: Configurazione parametri
- `Backend Response for Dynamic Config`: Valutazione dipendenze
- `Updating dynamicParameters`: Aggiornamento parametri
- `Selected values initialized`: Inizializzazione valori

### Verifiche di Sistema

#### Checklist Backend
- [ ] Server in esecuzione su porta 5001
- [ ] Database connesso e popolato
- [ ] Parametri root configurati con `is_root=true`
- [ ] Regole dipendenze attive

#### Checklist Frontend
- [ ] Dipendenze installate (`npm install`)
- [ ] File `.env.local` configurato
- [ ] Build senza errori (`npm run build`)
- [ ] Console browser senza errori critici

#### Checklist Integrazione
- [ ] API backend accessibili
- [ ] Adapter registrato correttamente
- [ ] Tipi TypeScript allineati
- [ ] Test adapter passano

---

## 🚀 Deployment

### Ambiente di Sviluppo

#### Setup Locale
```bash
# Frontend
cd frontend-cliente
npm install
npm run dev
# Disponibile su http://localhost:3000

# Backend (separato)
# Assicurarsi che sia in esecuzione su http://*************:5001
```

#### Configurazione Ambiente
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://*************:5001
```

### Ambiente di Produzione

#### Build Frontend
```bash
# Build ottimizzata
npm run build

# Test build locale
npm start

# Export statico (se necessario)
npm run export
```

#### Configurazione Produzione
```bash
# .env.production
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
```

#### Deploy su Vercel
```bash
# Installare Vercel CLI
npm i -g vercel

# Deploy
vercel

# Configurare variabili ambiente su dashboard Vercel
```

#### Deploy su Server
```bash
# Build e copy
npm run build
scp -r .next/ user@server:/var/www/frontend-cliente/

# Configurare nginx/apache per servire i file statici
```

### Monitoraggio

#### Metriche da Monitorare
- **Tempo di caricamento**: Pagina iniziale < 2s
- **Errori API**: Rate < 1%
- **Conversioni**: Configurazioni completate
- **Performance**: Core Web Vitals

#### Logging Produzione
```typescript
// Configurare logging strutturato
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'production') {
      // Inviare a servizio logging (es. LogRocket, Sentry)
    } else {
      console.log(message, data);
    }
  }
};
```

### Backup e Sicurezza

#### Backup Configurazioni
- **Stato locale**: Salvato in localStorage
- **Backup periodico**: Implementare export/import
- **Versioning**: Git per codice, database per configurazioni

#### Sicurezza
- **HTTPS**: Obbligatorio in produzione
- **CORS**: Configurato correttamente
- **Validazione**: Input sanitization
- **Rate Limiting**: Protezione API

---

## 📞 Supporto

### Contatti
- **Sviluppatore**: [Nome] - [email]
- **Repository**: [URL GitHub]
- **Documentazione**: [URL Docs]

### Risorse Aggiuntive
- **HeroUI Docs**: https://heroui.com
- **Next.js Docs**: https://nextjs.org/docs
- **TypeScript Handbook**: https://www.typescriptlang.org/docs

### Contribuire
1. Fork del repository
2. Creare branch feature (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push branch (`git push origin feature/amazing-feature`)
5. Aprire Pull Request

---

*Documentazione aggiornata al: [Data corrente]*
*Versione sistema: 1.0.0*