# Integrazione con le Nuove API Backend

## Panoramica

Il frontend cliente è stato riscritturato per utilizzare in modo naturale le API del backend, sfruttando il sistema di dipendenze complesse e la logica dei parametri root.

## Flusso di Utilizzo

### 1. Selezione Parametro Root (Lavorazione)
- **Endpoint**: `GET /api/v1/parameters/root`
- **Scopo**: Recupera tutti i parametri root disponibili (equivalenti alle "lavorazioni")
- **Risposta**: Array di oggetti `Parametro` con `is_root: true`

### 2. Configurazione Iniziale
- **Endpoint**: `GET /api/v1/parameters/{id}/initial-config`
- **Scopo**: Recupera i parametri che devono essere mostrati inizialmente per una lavorazione
- **Logica**: Trova le regole che hanno come condizione il parametro root e restituisce i parametri con effetto "MOSTRA"

### 3. Valutazione Dinamica delle Dipendenze
- **Endpoint**: `POST /api/v1/dependency_evaluation/evaluate`
- **Scopo**: Valuta le dipendenze quando un parametro viene modificato
- **Trigger**: Quando un parametro con `has_dependent_children: true` viene modificato

## Struttura dei Dati

### Parametro
```typescript
interface Parametro {
  id_parametro: number;
  nome_parametro: string;
  tipo_controllo_ui: string;
  descrizione?: string;
  foto?: string;
  is_root: boolean;
  ordine_visualizzazione: number;
  attivo: boolean;
  valori: ValoreParametro[];
  has_dependent_children: boolean;
}
```

### Valore Parametro
```typescript
interface ValoreParametro {
  id_valore_parametro: number;
  testo_visualizzato_ui: string;
  ordine_visualizzazione: number;
  foto?: string;
  colore?: string;
  descrizione?: string;
}
```

## Componenti Principali

### 1. `useParameterFlow` Hook
Gestisce l'intero flusso di configurazione:
- Caricamento parametri root
- Selezione lavorazione
- Gestione valori selezionati
- Valutazione dipendenze

### 2. `ParameterRenderer` Component
Renderizza dinamicamente i controlli UI basandosi su `tipo_controllo_ui`:
- SELECT
- RADIO
- CHECKBOX_GROUP
- INPUT_TEXT
- INPUT_NUMBER
- TEXTAREA

### 3. `apiService`
Servizio centralizzato per le chiamate API con gestione errori.

## Tipi di Controllo UI Supportati

| Tipo | Descrizione | Valore Restituito |
|------|-------------|-------------------|
| SELECT | Menu a tendina | `number` (id_valore_parametro) |
| RADIO | Pulsanti radio | `number` (id_valore_parametro) |
| CHECKBOX_GROUP | Gruppo di checkbox | `number[]` (array di id_valore_parametro) |
| INPUT_TEXT | Campo di testo | `string` |
| INPUT_NUMBER | Campo numerico | `number` |
| TEXTAREA | Area di testo | `string` |

## Gestione delle Dipendenze

1. **Trigger**: Un parametro con `has_dependent_children: true` viene modificato
2. **Valutazione**: Il sistema invia una richiesta di valutazione con i parametri correnti
3. **Risposta**: Il backend restituisce i nuovi parametri dipendenti ordinati per `ordine_visualizzazione`
4. **Aggiornamento**: L'UI aggiorna i parametri mantenendo l'ordine stabile

### Ordinamento dei Parametri

Il backend garantisce un ordine stabile dei parametri tramite il campo `ordine_visualizzazione`:
- **Lavorazioni** (ordine: 1) - Parametro root
- **Tipologia dente** (ordine: 2)
- **Tipologia Impianto** (ordine: 3)
- **Tecnica** (ordine: 4)
- **Connessione** (ordine: 5)
- **Diametro** (ordine: 6)

Questo garantisce che l'ordine di visualizzazione rimanga consistente indipendentemente da quando i parametri vengono aggiunti o rimossi dalle regole di dipendenza.

## Vantaggi del Nuovo Approccio

1. **Naturale**: Utilizza direttamente la logica del backend senza adattamenti
2. **Flessibile**: Supporta tutti i tipi di controllo UI del backend
3. **Scalabile**: Facile aggiungere nuovi tipi di parametri
4. **Performante**: Caricamento incrementale dei parametri
5. **Mantenibile**: Codice più pulito e tipizzato

## Migrazione dal Vecchio Sistema

Il vecchio sistema utilizzava:
- Concetto di "Lavorazione" separato
- API specifiche per la configurazione dinamica
- Logica di mapping complessa

Il nuovo sistema:
- Usa i parametri root come lavorazioni
- Sfrutta il sistema di regole del backend
- Elimina la necessità di mapping

## Prossimi Passi

1. **Test**: Verificare il funzionamento con dati reali
2. **Ottimizzazioni**: Implementare caching e debouncing
3. **UI/UX**: Migliorare l'interfaccia utente
4. **Validazione**: Aggiungere validazione lato client
5. **Errori**: Migliorare la gestione degli errori